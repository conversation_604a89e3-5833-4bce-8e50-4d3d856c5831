name: Deploy to Production Server 2

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build:
    runs-on: [ self-hosted, Linux, X64, prod2-server ]

    steps:
      - uses: actions/checkout@v3

      - name: Check if the branch is master
        run: |
          if [[ "${{ github.ref }}" != "refs/heads/master" ]]; then
            echo "Error: Workflow must be run on the master branch."
            exit 1
          fi

      - name: Create a multiline file
        run: |
          cat << EOF > env.vars
          SPRING_DATASOURCE_WEB_URL=jdbc:mysql://${{ vars.PROD_MYSQL_HOSTNAME }}:3306/${{ vars.PROD_MYSQL_DBNAME_WEB }}?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=America/Chicago 
          SPRING_DATASOURCE_WEB_USERNAME=${{ secrets.PROD_MYSQL_USERNAME }}
          SPRING_DATASOURCE_WEB_PASSWORD=${{ secrets.PROD_MYSQL_PASSWORD }}
          SPRING_DATASOURCE_FMCSA_URL=jdbc:mysql://${{ vars.PROD_MYSQL_HOSTNAME }}:3306/${{ vars.PROD_MYSQL_DBNAME_FMCSA }}?useSSL=false&allowPublicKeyRetrieval=true
          SPRING_DATASOURCE_FMCSA_USERNAME=${{ secrets.PROD_MYSQL_USERNAME }}
          SPRING_DATASOURCE_FMCSA_PASSWORD=${{ secrets.PROD_MYSQL_PASSWORD }} 
          SPRING_RABBITMQ_HOST=rabbitmq
          SPRING_PROFILES_ACTIVE=prod
          JWT_SECRET=${{ secrets.JWT_PRIVATE_KEY }}
          SPRING_MAIL_HOST=${{ vars.EMAIL_SERVER }}
          SPRING_MAIL_PORT=${{ vars.EMAIL_PORT }}
          SPRING_MAIL_USERNAME=${{ secrets.EMAIL_USERNAME }}
          SPRING_MAIL_PASSWORD=${{ secrets.EMAIL_PASSWORD }}          
          BULKLOADS_PUBNUB_PUB_KEY=${{ secrets.PROD_PUBNUB_PUB_KEY }}
          BULKLOADS_PUBNUB_SUB_KEY=${{ secrets.PROD_PUBNUB_SUB_KEY }}      
          BULKLOADS_AWS_ACCESS_KEY=${{ secrets.PROD_AWS_ACCESS_KEY }}
          BULKLOADS_AWS_SECRET_KEY=${{ secrets.PROD_AWS_SECRET_KEY }}
          BULKLOADS_FUSION_REACTOR_APPLICATION_NAME=JAVA_SPRING_PROD_2
          HOST_NODE_NAME=prod2
          JAVA_TOOL_OPTIONS=-Xms10g -Xmx20g -XX:MaxDirectMemorySize=64m -XX:MaxMetaspaceSize=1024m
          EOF

      - name: Start RabbitMQ
        run: docker compose up -d rabbitmq

      - name: Dockerize (including FusionReactor)
        run: |
          ./gradlew clean bootBuildImage -PfusionReactorInstanceName=fr_prod2

      - name: Start Bulkloads Fusion Reactor Server
        env:
          EXTERNAL_PORT: 9001
          FUSION_REACTOR_CONTEXT: /home/<USER>/fusionreactor
          SERVER_CONTAINER_NAME: prod2
        run: docker compose up -d bulkloads-server

      - name: Wait for API to be ready
        uses: mydea/action-wait-for-api@v1
        with:
          url: http://localhost:9001/actuator/health
          expected-response-field: status
          expected-response-field-value: UP
          timeout: 1800

      - name: Match the SHA (current commit hash)
        uses: mydea/action-wait-for-api@v1
        with:
          url: http://localhost:9001/actuator/info
          expected-response-field: 'git.commit.id.full'
          expected-response-field-value: ${{ github.sha }}
          timeout: 10
          interval: 5
