#!/usr/bin/env bash

# • If $TMS_BRANCH is **set**, clone the TMS repo and build from source
# • Otherwise download the latest pre‑built artifact (name: tms-test) and unpack it

set -euo pipefail
[[ "${DEBUG:-}" == "1" ]] && set -x
: "${GITHUB_TOKEN:?GITHUB_TOKEN is not set – aborting.}"

DEVOPS_BRANCH="${DEVOPS_BRANCH:-test}"
TMS_BRANCH="${TMS_BRANCH:-}"
TMS_DIR="/opt/bulkloads-tms"
TMS_BUILD_DIR="/var/www/tms"

ARTIFACT_NAME="tms-test"
TMP_ARTIFACT_DIR="/tmp/tms_artifact"


# echo "▶ DEVOPS_BRANCH is set to $DEVOPS_BRANCH"
repo_url="https://${GITHUB_TOKEN}@github.com/bulkloads/bulkloads-devops.git"

if [[ -d "$DEVOPS_DIR/.git" && -z "${USE_MOUNTED_DEVOPS:-}" ]]; then
  echo "▶ Repo exists – pulling latest"
  git -C "$DEVOPS_DIR" remote set-url origin "$repo_url"
  git -C "$DEVOPS_DIR" fetch origin "$DEVOPS_BRANCH"
  git -C "$DEVOPS_DIR" reset --hard "origin/$DEVOPS_BRANCH"
elif [[ ! -d "$DEVOPS_DIR/.git" && -z "${USE_MOUNTED_DEVOPS:-}" ]]; then
  echo "▶ First‑time clone into [$DEVOPS_DIR]"
  echo "▶ Cloning from $repo_url"
  git clone --depth 1 --branch "$DEVOPS_BRANCH" "$repo_url" "$DEVOPS_DIR"
else
  echo "▶ Using mounted devops directory – skipping git operations"
fi

cd "$DEVOPS_DIR"

# ─── Build from source  (if TMS_BRANCH provided) ─────────────────────────────
if [[ -n "$TMS_BRANCH" ]]; then
  echo "▶ TMS_BRANCH is set to $TMS_BRANCH – building TMS from source"

  # force HTTPS cloning with token
  git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "**************:"
  git config --global url."https://${GITHUB_TOKEN}@github.com/".insteadOf "ssh://**************/"

  tms_repo_url="https://${GITHUB_TOKEN}@github.com/bulkloads/bulkloads-tms.git"

  if [[ -d "$TMS_DIR/.git" ]]; then
    echo "▶ TMS repo exists – pulling latest"
    git -C "$TMS_DIR" remote set-url origin "$tms_repo_url"
    git -C "$TMS_DIR" fetch origin "$TMS_BRANCH"
    git -C "$TMS_DIR" reset --hard "origin/$TMS_BRANCH"
  else
    echo "▶ First‑time clone of TMS into [$TMS_DIR]"
    mkdir -p "$TMS_DIR"
    echo "▶ Cloning TMS from $tms_repo_url"
    git clone --depth 1 --branch "$TMS_BRANCH" "$tms_repo_url" "$TMS_DIR"
  fi

  cd "$TMS_DIR"

  echo "▶ Installing TMS dependencies"
  yarn install --immutable

  echo "▶ Building TMS project"
  NODE_OPTIONS="--max_old_space_size=8192" yarn build

  echo "▶ Copying TMS build files to NGINX directory"
  rm -rf "$TMS_BUILD_DIR"
  mkdir -p "$TMS_BUILD_DIR"
  cp -r ./build/* "$TMS_BUILD_DIR/"

  echo "✅ TMS build completed. Files are available in $TMS_BUILD_DIR"

# ─── Otherwise: fetch latest artefact from GitHub Actions  ────────────────────
else
  echo "▶ No TMS_BRANCH supplied – using latest artifact '$ARTIFACT_NAME'"

  api="https://api.github.com/repos/bulkloads/bulkloads-tms/actions/artifacts"

  echo "▶ Querying artifact list"
  artifact_id=$(
    curl -s -H "Authorization: Bearer ${GITHUB_TOKEN}" \
         -H "Accept: application/vnd.github+json" \
         "${api}?per_page=100" |
    jq -r --arg NAME "$ARTIFACT_NAME" '
        .artifacts
        | map(select(.name==$NAME and .expired==false))
        | sort_by(.updated_at) | last | .id'
  )

  if [[ -z "$artifact_id" || "$artifact_id" == "null" ]]; then
    echo "❌ No valid artifact named '$ARTIFACT_NAME' found – aborting."
    exit 1
  fi

  echo "▶ Downloading artifact id $artifact_id"
  curl -sL -H "Authorization: Bearer ${GITHUB_TOKEN}" \
       -H "Accept: application/vnd.github+json" \
       -o /tmp/artifact.zip \
       "https://api.github.com/repos/bulkloads/bulkloads-tms/actions/artifacts/${artifact_id}/zip"

  echo "▶ Unpacking artifact"
  rm -rf "$TMP_ARTIFACT_DIR" && mkdir -p "$TMP_ARTIFACT_DIR"
  unzip -oq /tmp/artifact.zip -d "$TMP_ARTIFACT_DIR"

  echo "▶ Deploying to NGINX directory"
  rm -rf "$TMS_BUILD_DIR"
  mkdir -p "$TMS_BUILD_DIR"

  # If the zip contains a top‑level build/ directory use it, otherwise copy all
  if [[ -d "$TMP_ARTIFACT_DIR/build" ]]; then
    cp -r "$TMP_ARTIFACT_DIR/build/"* "$TMS_BUILD_DIR/"
  else
    cp -r "$TMP_ARTIFACT_DIR/"* "$TMS_BUILD_DIR/"
  fi

  echo "✅ Artifact deployed to $TMS_BUILD_DIR"
fi

# ─── Run the local Ansible playbook to configure NGINX ────────────────────────
echo "▶ Using playbook:"
cat playbooks/nginx-local.yaml

echo "▶ Executing the nginx playbook"
ansible-playbook -i "localhost," -c local -e "@/opt/dev_server.yaml" playbooks/nginx-local.yaml

exec tail -f /dev/null
