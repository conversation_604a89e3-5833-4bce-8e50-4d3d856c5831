#!/usr/bin/env python3
import sys
import subprocess
import os

def install_requirements():
    print("Installing required packages...")
    subprocess.check_call([sys.executable, "-m", "pip", "install", 
                          "mysql-connector-python", "timezonefinder", "python-dotenv"])
    print("Packages installed successfully.")

def run_timezone_script():
    print("Running timezone script...")
    script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "set_timezones.py")
    subprocess.check_call([sys.executable, script_path])
    print("Timezone script completed.")

if __name__ == "__main__":
    try:
        install_requirements()
        run_timezone_script()
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)