package com.bulkloads.common.mui.model;

import lombok.Getter;

public class QueryStringBuilder {

  @Getter
  private StringBuilder queryString;
  private int itemIndex = 0;

  public QueryStringBuilder() {
    this.queryString = new StringBuilder();
  }

  public static QueryStringBuilder builder() {
    return new QueryStringBuilder();
  }

  public QueryStringBuilder addFilter(String field, String operator, String value) {
    queryString.append("filter[items][")
        .append(itemIndex)
        .append("][field]=")
        .append(field)
        .append("&");

    queryString.append("filter[items][")
        .append(itemIndex)
        .append("][operator]=")
        .append(operator)
        .append("&");

    queryString.append("filter[items][")
        .append(itemIndex)
        .append("][value]=")
        .append(value)
        .append("&");

    itemIndex++;
    return this;
  }

  public QueryStringBuilder addSort(String field, String sort) {
    queryString.append("sort[")
        .append(itemIndex)
        .append("][field]=")
        .append(field)
        .append("&");
    queryString.append("filter[")
        .append(itemIndex)
        .append("][sort]=")
        .append(sort)
        .append("&");
    itemIndex++;
    return this;
  }


  public String build() {
    if (queryString.length() > 0) {
      queryString.setLength(queryString.length() - 1);
    }
    return queryString.toString();
  }

}
