package com.bulkloads.web.routing.util;

import static com.bulkloads.web.routing.util.BearingUtil.bearingDirection;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import com.bulkloads.web.routing.domain.vo.Coordinates;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;

class BearingUtilTest {

  @ParameterizedTest
  @CsvSource({
      "0.0, N",
      "45.0, NE",
      "90.0, E",
      "135.0, SE",
      "180.0, S",
      "-135.0, SW",
      "-90.0, W",
      "-45.0, NW"})
  void shouldCalculateBearingDirection(double bearing, String expectedDirection) {
    String actualDirection = bearingDirection(bearing);
    assertEquals(expectedDirection, actualDirection);
  }

  @Test
  void shouldThrowExceptionForInvalidBearing() {
    assertThrows(IllegalArgumentException.class,
        () -> bearingDirection(200.0));
  }

  @ParameterizedTest
  @CsvSource({
      "0.0, 0.0, 1.0, 0.0, 0.0",
      "0.0, 0.0, 0.0, 1.0, 90.0",
      "0.0, 0.0, -1.0, 0.0, 180.0",
      "0.0, 0.0, 0.0, -1.0, -90.0",
      "1.0, 1.0, 2.0, 2.0, 45.0",
      "1.0, 1.0, 0.0, 0.0, -135.0"})
  void shouldCalculateBearing(double originLat, double originLong, double destLat, double destLong, double expectedBearing) {
    final Coordinates origin = Coordinates.builder()
        .latitude(originLat)
        .longitude(originLong)
        .build();

    final Coordinates destination = Coordinates.builder()
        .latitude(destLat)
        .longitude(destLong)
        .build();

    double actualBearing = BearingUtil.bearing(origin, destination);
    assertEquals(expectedBearing, actualBearing, 0.03);
  }
}