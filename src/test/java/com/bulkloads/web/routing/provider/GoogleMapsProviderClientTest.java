package com.bulkloads.web.routing.provider;

import static com.bulkloads.web.routing.provider.GoogleMapsProviderClient.GOOGLE;
import static com.bulkloads.web.routing.util.RouteTestUtil.buildDestination;
import static com.bulkloads.web.routing.util.RouteTestUtil.buildOrigin;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.verify;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Optional;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.service.dto.RouteDto;
import com.google.maps.model.Distance;
import com.google.maps.model.DistanceMatrix;
import com.google.maps.model.DistanceMatrixElement;
import com.google.maps.model.DistanceMatrixElementStatus;
import com.google.maps.model.DistanceMatrixRow;
import com.google.maps.model.Duration;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.internal.matchers.apachecommons.ReflectionEquals;
import org.mockito.junit.jupiter.MockitoExtension;
import lombok.SneakyThrows;

@ExtendWith(MockitoExtension.class)
class GoogleMapsProviderClientTest {

  @Spy
  @InjectMocks
  GoogleMapsProviderClient providerClient;

  @Test
  @SneakyThrows
  void getRoute_shouldReturnRouteWhenDistanceMatrixProvidesOk() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final RouteDto expected = buildRouteDtoOk(origin, destination);
    final DistanceMatrix response = buildDistanceMatrixOk();

    doReturn(response).when(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());

    final Optional<RouteDto> result = providerClient.getRoute(origin, destination);

    assertTrue(result.isPresent());
    assertTrue(new ReflectionEquals(expected, "dateCreated").matches(result.get()));

    verify(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());
  }

  @Test
  @SneakyThrows
  void getRoute_shouldReturnRouteWhenDistanceMatrixProvidesNotFound() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();
    final RouteDto expected = buildRouteDtoNotFound(origin, destination);
    final DistanceMatrix response = buildDistanceMatrixNotFound();

    doReturn(response).when(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());

    final Optional<RouteDto> result = providerClient.getRoute(origin, destination);

    assertTrue(result.isPresent());
    assertTrue(new ReflectionEquals(expected, "dateCreated").matches(result.get()));

    verify(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());
  }

  @Test
  @SneakyThrows
  void getRoute_shouldReturnEmptyWhenDistanceMatrixFails() {
    final Location origin = buildOrigin();
    final Location destination = buildDestination();

    doThrow(new Exception()).when(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());

    final Optional<RouteDto> result = providerClient.getRoute(origin, destination);

    assertTrue(result.isEmpty());

    verify(providerClient).getDistanceMatrix(origin.getCoordinates(), destination.getCoordinates());
  }

  private DistanceMatrix buildDistanceMatrixNotFound() {
    final DistanceMatrixRow distanceMatrixRow = new DistanceMatrixRow();
    final DistanceMatrixElement distanceMatrixElement = new DistanceMatrixElement();
    distanceMatrixElement.distance = new Distance();
    distanceMatrixElement.duration = new Duration();
    distanceMatrixElement.status = DistanceMatrixElementStatus.NOT_FOUND;
    distanceMatrixRow.elements = new DistanceMatrixElement[] {distanceMatrixElement};
    final DistanceMatrixRow[] rows = new DistanceMatrixRow[] { distanceMatrixRow };
    return new DistanceMatrix(new String[] {}, new String[] {}, rows);
  }

  private DistanceMatrix buildDistanceMatrixOk() {
    final DistanceMatrixRow distanceMatrixRow = new DistanceMatrixRow();
    final DistanceMatrixElement distanceMatrixElement = new DistanceMatrixElement();
    distanceMatrixElement.distance = new Distance();
    distanceMatrixElement.distance.inMeters = 2;
    distanceMatrixElement.distance.humanReadable = "10 miles";
    distanceMatrixElement.duration = new Duration();
    distanceMatrixElement.duration.inSeconds = 600;
    distanceMatrixElement.duration.humanReadable = "10 minutes";
    distanceMatrixElement.status = DistanceMatrixElementStatus.OK;
    distanceMatrixRow.elements = new DistanceMatrixElement[] {distanceMatrixElement};
    final DistanceMatrixRow[] rows = new DistanceMatrixRow[] { distanceMatrixRow };
    return new DistanceMatrix(new String[] {}, new String[] {}, rows);
  }

  public static RouteDto buildRouteDtoNotFound(final Location origin, final Location destination) {
    return buildRouteDtoCommon(origin, destination)
        .setBearing(44.97818294146504)
        .setBearingDirection("NE")
        .setErrorMessage("NOT_FOUND")
        .setMiles(BigDecimal.valueOf(-1.0))
        .setDuration(-1L)
        .setDurationText("")
        .setResponse("originAddresses: [], destinationAddresses: [], rows: [[DistanceMatrixElement NOT_FOUND distance=null, duration=null]]");
  }

  public static RouteDto buildRouteDtoOk(final Location origin, final Location destination) {
    return buildRouteDtoCommon(origin, destination)
        .setBearing(44.97818294146504)
        .setBearingDirection("NE")
        .setErrorMessage("OK")
        .setMiles(BigDecimal.valueOf(0.001242742))
        .setDuration(600L)
        .setDurationText("10 minutes")
        .setResponse("originAddresses: [], destinationAddresses: [], rows: [[DistanceMatrixElement OK distance=10 miles, duration=10 minutes]]");
  }

  private static RouteDto buildRouteDtoCommon(final Location origin, final Location destination) {
    return new RouteDto()
        .setStartAddress(origin.getAddress())
        .setStartCity(origin.getCity())
        .setStartState(origin.getState())
        .setStartZip(origin.getZip())
        .setStartCountry(origin.getCountry())
        .setEndAddress(destination.getAddress())
        .setEndCity(destination.getCity())
        .setEndState(destination.getState())
        .setEndZip(destination.getZip())
        .setEndCountry(destination.getCountry())
        .setStartLat(origin.getCoordinates().getLatitude())
        .setStartLng(origin.getCoordinates().getLongitude())
        .setEndLat(destination.getCoordinates().getLatitude())
        .setEndLng(destination.getCoordinates().getLongitude())
        .setDateCreated(Instant.now())
        .setStops(0)
        .setProvider(GOOGLE);
  }
}