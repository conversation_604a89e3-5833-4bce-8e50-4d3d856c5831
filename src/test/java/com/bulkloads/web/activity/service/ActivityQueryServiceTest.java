package com.bulkloads.web.activity.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.activity.repository.ActivityQueryRepository;
import com.bulkloads.web.activity.service.dto.ActivityListResponse;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ActivityQueryServiceTest {

  @Mock
  private ActivityQueryRepository activityQueryRepository;

  @InjectMocks
  private ActivityQueryService activityQueryService;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(activityQueryRepository);
  }

  @Test
  @WithMockActor
  void shouldGetActivities() {
    final String userIds = "1,2,3";
    final Integer activityTypeId = 1;
    final Integer pastDays = 7;
    final Integer skip = 0;
    final Integer limit = 100;
    final int userCompanyId = 1;
    final List<ActivityListResponse> expectedResponse = List.of();

    when(activityQueryRepository.getActivities(userCompanyId, userIds, activityTypeId, pastDays, skip, limit))
        .thenReturn(expectedResponse);

    final List<ActivityListResponse> result = activityQueryService.getActivities(
        userIds, activityTypeId, pastDays, skip, limit);

    assertEquals(expectedResponse, result);
    verify(activityQueryRepository).getActivities(userCompanyId, userIds, activityTypeId, pastDays, skip, limit);
  }

  @Test
  @WithMockActor
  void shouldGetActivitiesWithNullParameters() {
    final int userCompanyId = 1;
    final List<ActivityListResponse> expectedResponse = List.of();

    when(activityQueryRepository.getActivities(userCompanyId, null, null, null, null, null))
        .thenReturn(expectedResponse);

    final List<ActivityListResponse> result = activityQueryService.getActivities(
        null, null, null, null, null);

    assertEquals(expectedResponse, result);
    verify(activityQueryRepository).getActivities(userCompanyId, null, null, null, null, null);
  }

  @Test
  @WithMockActor
  void shouldGetActivityTypes() {
    final List<ActivityTypeListResponse> expectedResponse = List.of();

    when(activityQueryRepository.getActivityTypes(null)).thenReturn(expectedResponse);

    final List<ActivityTypeListResponse> result = activityQueryService.getActivityTypes();

    assertEquals(expectedResponse, result);
    verify(activityQueryRepository).getActivityTypes(null);
  }
}
