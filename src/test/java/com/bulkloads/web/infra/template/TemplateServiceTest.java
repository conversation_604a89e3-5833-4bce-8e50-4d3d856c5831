package com.bulkloads.web.infra.template;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;

class TemplateServiceTest {

  TemplateService templateService;

  @Mock
  AppProperties appProperties;

  @BeforeEach
  void setUp() {
    appProperties = mock(AppProperties.class);
    when(appProperties.isProdMode()).thenReturn(true);
    templateService = new TemplateService(appProperties);
  }

  @Test
  void shouldProcessTemplateContentSuccessfully() {
    String templateContent = "Hello ${name}, welcome to ${platform}!";
    Map<String, Object> model = new HashMap<>();
    model.put("name", "John Doe");
    model.put("platform", "Bulkloads");

    String result = templateService.processFromTemplateContent(templateContent, model);

    assertNotNull(result);
    assertEquals("Hello John Doe, welcome to Bulkloads!", result);
  }

  @Test
  void shouldThrowExceptionForInvalidTemplateContent() {
    String invalidTemplateContent = "Hello ${name";
    Map<String, Object> model = new HashMap<>();
    model.put("name", "John Doe");

    assertThrows(BulkloadsException.class, () ->
        templateService.processFromTemplateContent(invalidTemplateContent, model));
  }

  @Test
  void shouldProcessTemplateContentWithEmptyModel() {
    String templateContent = "Hello ${name}, welcome to ${platform}!";
    Map<String, Object> model = Collections.emptyMap();

    assertThrows(BulkloadsException.class, () ->
        templateService.processFromTemplateContent(templateContent, model));
  }
}
