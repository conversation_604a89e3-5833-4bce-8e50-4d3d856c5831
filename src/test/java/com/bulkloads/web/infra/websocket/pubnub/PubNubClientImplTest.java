package com.bulkloads.web.infra.websocket.pubnub;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import com.pubnub.api.PubNub;
import com.pubnub.api.PubNubException;
import com.pubnub.api.endpoints.presence.HereNow;
import com.pubnub.api.endpoints.pubsub.Publish;
import com.pubnub.api.endpoints.pubsub.Signal;
import com.pubnub.api.models.consumer.PNPublishResult;
import com.pubnub.api.models.consumer.presence.PNHereNowResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class PubNubClientImplTest {

  public static final String CHANNEL = "channel";
  public static final String MESSAGE = "message";
  public static final boolean INCLUDE_UUIDS = true;
  public static final boolean INCLUDE_STATE = true;
  public static final long TIME_TOKEN = 12345L;
  public static final String META = "meta";
  @Mock
  PubNub pubnub;
  @Mock
  PNPublishResult pnPublishResult;
  @Mock
  Publish publish;
  @Mock
  Signal signal;
  @Mock
  HereNow hereNow;
  @Mock
  PNHereNowResult pnHereNowResult;
  @Mock
  PubNubException pubNubException;
  @InjectMocks
  private PubNubClientImpl pubNubClientImpl;

  @AfterEach
  void tearDown() {
    verifyNoMoreInteractions(pubnub, pnPublishResult, publish, signal, hereNow, pnHereNowResult, pubNubException);
  }

  @Test
  void shouldPublishMessageSuccessfully() throws PubNubException {

    when(pubnub.publish()).thenReturn(publish);
    when(publish.channel(CHANNEL)).thenReturn(publish);
    when(publish.message(MESSAGE)).thenReturn(publish);
    when(publish.meta(META)).thenReturn(publish);
    when(publish.sync()).thenReturn(pnPublishResult);
    when(pnPublishResult.getTimetoken()).thenReturn(TIME_TOKEN);

    pubNubClientImpl.publish(CHANNEL, META, MESSAGE);

    verify(publish).channel(CHANNEL);
    verify(publish).message(MESSAGE);
    verify(publish).meta(META);
    verify(publish).sync();
    verify(pubnub).publish();
  }

  @Test
  void shouldHandlePublishMessageException() throws PubNubException {
    when(pubnub.publish()).thenReturn(publish);
    when(publish.channel(CHANNEL)).thenReturn(publish);
    when(publish.message(MESSAGE)).thenReturn(publish);
    when(publish.meta(META)).thenReturn(publish);
    when(publish.sync()).thenThrow(pubNubException);

    assertThrows(BulkloadsException.class, () -> {
      pubNubClientImpl.publish(CHANNEL, META, MESSAGE);
    });

    verify(publish).channel(CHANNEL);
    verify(publish).message(MESSAGE);
    verify(publish).meta(META);
    verify(publish).sync();
    verify(pubnub).publish();
  }

  @Test
  void shouldSignalMessageSuccessfully() throws PubNubException {

    when(pubnub.signal()).thenReturn(signal);
    when(signal.channel(CHANNEL)).thenReturn(signal);
    when(signal.message(MESSAGE)).thenReturn(signal);
    when(signal.sync()).thenReturn(pnPublishResult);
    when(pnPublishResult.getTimetoken()).thenReturn(TIME_TOKEN);

    pubNubClientImpl.signal(CHANNEL, MESSAGE);

    verify(signal).channel(CHANNEL);
    verify(signal).message(MESSAGE);
    verify(signal).sync();
    verify(pubnub).signal();
  }

  @Test
  void shouldHandleSignalMessageException() throws PubNubException {
    when(pubnub.signal()).thenReturn(signal);
    when(signal.channel(CHANNEL)).thenReturn(signal);
    when(signal.message(MESSAGE)).thenReturn(signal);
    when(signal.sync()).thenThrow(pubNubException);

    assertThrows(BulkloadsException.class, () -> {
      pubNubClientImpl.signal(CHANNEL, MESSAGE);
    });

    verify(signal).channel(CHANNEL);
    verify(signal).message(MESSAGE);
    verify(signal).sync();
    verify(pubnub).signal();
  }

  @Test
  void shouldGetHereNowMessageSuccessfully() throws PubNubException {

    when(pubnub.hereNow()).thenReturn(hereNow);
    when(hereNow.channels(List.of(CHANNEL))).thenReturn(hereNow);
    when(hereNow.includeUUIDs(INCLUDE_UUIDS)).thenReturn(hereNow);
    when(hereNow.includeState(INCLUDE_STATE)).thenReturn(hereNow);
    when(hereNow.sync()).thenReturn(pnHereNowResult);

    pubNubClientImpl.hereNow(CHANNEL, INCLUDE_UUIDS, INCLUDE_STATE);

    verify(hereNow).channels(List.of(CHANNEL));
    verify(hereNow).includeUUIDs(INCLUDE_UUIDS);
    verify(hereNow).includeState(INCLUDE_STATE);
    verify(hereNow).sync();
    verify(pubnub).hereNow();
  }

  @Test
  void shouldHandleHereNowException() throws PubNubException {

    when(pubnub.hereNow()).thenReturn(hereNow);
    when(hereNow.channels(List.of(CHANNEL))).thenReturn(hereNow);
    when(hereNow.includeUUIDs(INCLUDE_UUIDS)).thenReturn(hereNow);
    when(hereNow.includeState(INCLUDE_STATE)).thenReturn(hereNow);
    when(hereNow.sync()).thenThrow(pubNubException);

    assertThrows(BulkloadsException.class, () -> {
      pubNubClientImpl.hereNow(CHANNEL, INCLUDE_UUIDS, INCLUDE_STATE);
    });

    verify(hereNow).channels(List.of(CHANNEL));
    verify(hereNow).includeUUIDs(INCLUDE_UUIDS);
    verify(hereNow).includeState(INCLUDE_STATE);
    verify(hereNow).sync();
    verify(pubnub).hereNow();
  }

  @Test
  void shouldGetGlobalHereNowMessageSuccessfully() throws PubNubException {
    when(pubnub.hereNow()).thenReturn(hereNow);
    when(hereNow.channels(List.of(CHANNEL))).thenReturn(hereNow);
    when(hereNow.includeUUIDs(INCLUDE_UUIDS)).thenReturn(hereNow);
    when(hereNow.includeState(INCLUDE_STATE)).thenReturn(hereNow);
    when(hereNow.sync()).thenReturn(pnHereNowResult);

    pubNubClientImpl.globalHereNow(CHANNEL, INCLUDE_UUIDS, INCLUDE_STATE);

    verify(pubnub).hereNow();
    verify(hereNow).channels(List.of(CHANNEL));
    verify(hereNow).includeUUIDs(INCLUDE_UUIDS);
    verify(hereNow).includeState(INCLUDE_STATE);
    verify(hereNow).sync();
  }
}
