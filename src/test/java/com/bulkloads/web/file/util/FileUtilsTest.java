package com.bulkloads.web.file.util;

import static com.bulkloads.config.AppConstants.Paths.TEMP;
import static com.bulkloads.web.file.util.FileUtils.deleteFileIfExists;
import static com.bulkloads.web.file.util.FileUtils.ensureDirectoryExists;
import static com.bulkloads.web.file.util.FileUtils.resolveResource;
import static com.bulkloads.web.file.util.FileUtils.saveMultipartFileToDisk;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.UUID;
import com.bulkloads.common.StringUtil;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class FileUtilsTest {

  @Test
  void testSaveMultipartFileToDisk() throws IOException {

    String uuid = UUID.randomUUID().toString().toLowerCase();
    String filename = "example_filename.txt";
    String fileContent = "Sample file content";

    ensureDirectoryExists(TEMP);
    log.info("Working in [{}]", TEMP);

    MultipartFile multipartFile = new MockMultipartFile(
        "file",
        filename,
        "text/plain",
        fileContent.getBytes()
    );

    try (MockedStatic<StringUtil> mockedStringUtils = mockStatic(StringUtil.class)) {

      mockedStringUtils.when(StringUtil::getUuid).thenReturn(uuid);

      Path savedPath = saveMultipartFileToDisk(multipartFile);
      Path expectedPath = TEMP.resolve(uuid + "-" + filename);

      System.out.println(savedPath);

      assertThat(savedPath).isNotNull();
      assertThat(Files.exists(savedPath)).isTrue();
      assertThat(savedPath.toString()).contains(uuid);
      assertThat(expectedPath).isEqualTo(savedPath);
      assertThat(expectedPath.toString()).isEqualTo(savedPath.toString());

      deleteFileIfExists(savedPath);
    }
  }

  @Test
  void testResolveResource() {

    // Just make the sane asssumption that filetype icons exist
    assertThat(Files.exists(resolveResource("images"))).isTrue();
    assertThat(Files.exists(resolveResource("images/filetype_icons"))).isTrue();
    assertThat(Files.exists(resolveResource("images/filetype_icons/jpeg.png"))).isTrue();
    assertThat(Files.exists(resolveResource("images/filetype_icons/png.png"))).isTrue();
    assertThat(Files.exists(resolveResource("images/filetype_icons/pdf.png"))).isTrue();

    // also check the testing files
    assertThat(Files.exists(resolveResource("test_files"))).isTrue();
    assertThat(Files.exists(resolveResource("test_files/jpeg"))).isTrue();
    assertThat(Files.exists(resolveResource("test_files/jpeg/1.jpeg"))).isTrue();
    assertThat(Files.exists(resolveResource("test_files/png"))).isTrue();
    assertThat(Files.exists(resolveResource("test_files/png/1.png"))).isTrue();
  }

}