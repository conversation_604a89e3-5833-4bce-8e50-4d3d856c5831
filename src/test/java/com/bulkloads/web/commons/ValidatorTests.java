package com.bulkloads.web.commons;

import static org.junit.jupiter.api.Assertions.assertEquals;
import java.util.Optional;
import com.bulkloads.IntegrationTest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import jakarta.validation.Validator;
import jakarta.validation.constraints.Email;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class ValidatorTests extends IntegrationTest {

  @Autowired
  Validator validator;

  @Test
  void test_validator_works_on_optionals() {
    Orig orig = new Orig();
    orig.setPropertyA(Optional.of("Value1"));
    orig.setPropertyB(Optional.of("Value2"));

    var violations = validator.validate(orig);
    assertEquals(1, violations.size());
  }

  // https://docs.jboss.org/hibernate/stable/validator/reference/en-US/html_single/#example-container-element-constraints-optional
  @Data
  static class Orig {

    Optional<@Email String> propertyA;
    Optional<String> propertyB;
    Optional<String> propertyC;
  }

  @Data
  static class Dest {

    String propertyA;
    String propertyB;
    String propertyC;
  }

}
