package com.bulkloads.web.common;

import static io.jsonwebtoken.lang.Assert.isTrue;
import com.bulkloads.IntegrationTest;
import com.bulkloads.web.user.domain.entity.BlUserSettings;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.repository.UserTypeRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCrypt;
import org.springframework.transaction.annotation.Transactional;
import io.jsonwebtoken.lang.Assert;

@Transactional
public class TestUsersProvider extends IntegrationTest {

  public static final Logger log = LoggerFactory.getLogger(TestUsersProvider.class);
  public static final String TESTING_PASSWORD = "asdf";

  @Autowired
  public UserRepository userRepository;

  @Autowired
  public UserTypeRepository userTypeRepository;

  protected User createUser(String designator, boolean isSiteAdmin) {
    log.info("Creating user: {} admin: {}", designator, isSiteAdmin);
    UserCompany userCompany = new UserCompany();
    userCompany.setCompanyName(designator + "-userCompany");
    userCompany.setIntegrationMcpEnabled(true);

    var carrier = userTypeRepository.findByUserType("Carrier").orElseThrow();
    userCompany.getUserTypes().add(carrier);

    if (isSiteAdmin) {
      var admin = userTypeRepository.findByUserType("Admin").orElseThrow();
      userCompany.getUserTypes().add(admin);
    }

    User user = new User();
    user.setFirstName(designator + "-first-name");
    user.setLastName(designator + "-last-name");
    user.setUsername(designator + "-user-name");
    user.setPasswordHash(BCrypt.hashpw(TESTING_PASSWORD, BCrypt.gensalt()));
    user.setUserCompany(userCompany);
    userCompany.setOwner(user);
    user.setEmail(designator + "@bulkloads.com");
    user = userRepository.save(user);

    Assert.notNull(user.getUserCompany());
    Assert.notNull(user.getUserCompany().getOwner());

    BlUserSettings blUserSettings = new BlUserSettings();
    blUserSettings.setDeletionDate(null);
    blUserSettings.setUserCompany(user.getUserCompany());
    blUserSettings.setUser(user);

    user.setBlUserSettings(blUserSettings);

    isTrue(!user.getUserCompany().getUserTypes().isEmpty());
    return user;
  }

  protected User createUser(String designator) {
    return createUser(designator, false);
  }

  protected User createAdmin(String designator) {
    return createUser(designator, true);
  }

}
