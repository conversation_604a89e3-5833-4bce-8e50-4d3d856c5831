package com.bulkloads.web.addressbook.abuser.domain;

import static com.bulkloads.common.Parsers.wrap;
import static com.bulkloads.web.commons.Util.authenticate;
import static com.bulkloads.web.confg.TestConstants.DEFAULT_APP_NAME;
import com.bulkloads.security.Actor;
import com.bulkloads.web.addressbook.abcompany.domain.AbCompanyDomainService;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyResponse;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserRequest;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.bulkloads.web.common.TestUsersProvider;
import com.bulkloads.web.infra.websocket.WebSocketService;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class AbUserServiceTest extends TestUsersProvider {

  @Autowired
  AbUserRepository abUserRepository;
  @Autowired
  AbUserDomainService abUserDomainService;
  @Autowired
  AbUserService abUserService;
  @Autowired
  AbCompanyRepository abCompanyRepository;
  @Autowired
  AbCompanyDomainService abCompanyDomainService;
  @Autowired
  AbCompanyService abCompanyService;
  @MockBean
  WebSocketService webSocketService;


  @Test
  @Disabled
  void findDuplicate() {

    final Actor user = Actor.fromUser(createUser("user"), DEFAULT_APP_NAME);
    authenticate(user);

    AbCompanyRequest abCompanyRequest = AbCompanyRequest.builder()
        .companyName(wrap("Test Company"))
        .userTypeIds(wrap("20,30,40,50"))
        .build();
    AbCompanyResponse abCompanyResponse = abCompanyService.create(abCompanyRequest);

    AbUserRequest abUserRequest = new AbUserRequest();
    abUserRequest.setEmail(wrap("<EMAIL>"));
    abUserRequest.setFirstName(wrap("John"));
    abUserRequest.setAbCompanyId(wrap(abCompanyResponse.getAbCompanyId()));
    AbUserResponse abUserResponse = abUserService.create(abUserRequest);

    AbUser abUser = abUserRepository.findById(abUserResponse.getAbUserId()).orElseThrow();

    abUser.setFirstName(
        "Some very long first name that is not a duplicate of any other first"
        + " name in the database and is not null or empty or blank or whitespace"
        + " or anything else that is not a valid first name for a user. Some very"
        + " long first name that is not a duplicate of any other first name in the"
        + " database and is not null or empty or blank or whitespace or anything else"
        + " that is not a valid first name for a user.");

    abUserRepository.findDuplicate(
        abCompanyResponse.getUserCompanyId(),
        abUserResponse.getAbCompanyId(),
        abUser);
  }


}