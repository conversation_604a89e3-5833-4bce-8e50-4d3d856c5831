package com.bulkloads.web.addressbook.abuser.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import static com.bulkloads.web.confg.TestConstants.ADDRESS_BOOK_USERS_URL;
import static com.bulkloads.web.confg.TestConstants.DUMMY_ROLE;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import static org.springframework.restdocs.mockmvc.RestDocumentationRequestBuilders.delete;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import com.bulkloads.config.security.WithMockActor;
import com.bulkloads.web.ControllerTest;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserRequest;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import lombok.SneakyThrows;

@WebMvcTest(AbUserController.class)
@AutoConfigureMockMvc(addFilters = false)
class AbUserControllerTest extends ControllerTest {

  @MockBean
  AbUserService abUserService;

  @MockBean
  AbCompanyService abCompanyService;

  @AfterEach
  void tearDown() {
    super.tearDownMocks();
    verifyNoMoreInteractions(abUserService, abCompanyService);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenCreateAbUser_thenShouldBeOk() {
    final AbUserRequest request = new AbUserRequest();
    request.setAbCompanyId(Optional.of(1));
    request.setFirstName(Optional.of("John"));
    request.setLastName(Optional.of("Doe"));
    request.setExternalAbUserId(Optional.empty());
    request.setEmail(Optional.empty());
    request.setPhone1(Optional.empty());
    request.setPreferredContactMethod(Optional.empty());
    request.setAbUserNotes(Optional.empty());
    request.setAbUserRoleIds(Optional.empty());

    final AbUserResponse response = new AbUserResponse();
    response.setAbUserId(1);

    final String requestJson = """
        {
          "ab_company_id": 1,
          "first_name": "John",
          "last_name": "Doe"
        }""";

    final String responseJson = """
        {
          "message": "Created successfully",
          "ab_user_id": 1,
          "key": 1,
          "data": {
            "ab_user_id": 1
          }
        }""";

    when(abUserService.create(request)).thenReturn(response);

    mockMvc.perform(post(ADDRESS_BOOK_USERS_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(requestJson))
        .andExpect(status().isOk())
        .andExpect(content().json(responseJson));

    verify(abUserService).create(request);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenCreateAbUser_thenShouldBeForbidden() {
    final String requestJson = """
        {
          "ab_company_id": 1,
          "first_name": "John",
          "last_name": "Doe"
        }""";

    mockMvc.perform(post(ADDRESS_BOOK_USERS_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(requestJson))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenCreateAbUser_thenShouldBeUnauthorized() {
    final String requestJson = """
        {
          "ab_company_id": 1,
          "first_name": "John",
          "last_name": "Doe"
        }""";

    mockMvc.perform(post(ADDRESS_BOOK_USERS_URL)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(requestJson))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenUpdateAbUser_thenShouldBeOk() {
    final int abUserId = 1;
    final AbUserRequest request = new AbUserRequest();
    final AbUserResponse response = new AbUserResponse();
    response.setAbUserId(abUserId);

    final String requestJson = """
        {
          "first_name": "Jane",
          "last_name": "Smith"
        }""";

    final String responseJson = """
        {
          "message": "User details updated",
          "ab_user_id": 1,
          "key": 1,
          "data": {
            "ab_user_id": 1
          }
        }""";

    when(abUserService.update(abUserId, request)).thenReturn(response);

    mockMvc.perform(put(ADDRESS_BOOK_USERS_URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(requestJson))
        .andExpect(status().isOk())
        .andExpect(content().json(responseJson));

    verify(abUserService).update(abUserId, request);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenDeleteAbUser_thenShouldBeOk() {
    final int abUserId = 1;

    final String responseJson = """
        {
          "message": "User deleted"
        }""";

    doNothing().when(abUserService).remove(abUserId);

    mockMvc.perform(delete(ADDRESS_BOOK_USERS_URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(responseJson));

    verify(abUserService).remove(abUserId);
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = DUMMY_ROLE)
  void givenAuthenticatedUserWithWrongRole_whenDeleteAbUser_thenShouldBeForbidden() {
    final int abUserId = 1;

    mockMvc.perform(delete(ADDRESS_BOOK_USERS_URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isForbidden());
  }

  @Test
  @SneakyThrows
  void givenUnauthenticatedUser_whenDeleteAbUser_thenShouldBeUnauthorized() {
    final int abUserId = 1;

    mockMvc.perform(delete(ADDRESS_BOOK_USERS_URL + "/{ab_user_id}", abUserId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isUnauthorized());
  }

  @Test
  @SneakyThrows
  @WithMockActor(roles = ROLE_USER)
  void givenAuthenticatedUserWithRightRole_whenCreateAbUserFromUser_thenShouldBeOk() {
    final int userIdToReplicate = 123;
    final AbUserResponse response = new AbUserResponse();
    response.setAbUserId(1);
    response.setAbCompanyId(2);

    final String responseJson = """
        {
          "message": "Created successfully",
          "ab_user_id": 1,
          "ab_company_id": 2,
          "key": 1,
          "data": {
            "ab_user_id": 1,
            "ab_company_id": 2
          }
        }""";

    when(abCompanyService.createAbCompanyAndAbUserFromUser(userIdToReplicate)).thenReturn(response);

    mockMvc.perform(post(ADDRESS_BOOK_USERS_URL + "/from_user/{user_id}", userIdToReplicate)
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
        .andExpect(status().isOk())
        .andExpect(content().json(responseJson));

    verify(abCompanyService).createAbCompanyAndAbUserFromUser(userIdToReplicate);
  }
}
