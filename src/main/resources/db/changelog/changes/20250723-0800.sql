-- liquibase formatted sql

-- changeset theo:20250723-0800
alter table qb_load_invoices rename to qb_invoices;

create table qb_bills
(
    load_invoice_id int          not null,
    qb_bill_id   varchar(255) not null,
    primary key (load_invoice_id)
);

alter table qb_bills
    add constraint uq_qb_bills_load_invoice_id_qb_bill_id
        unique (load_invoice_id, qb_bill_id);

-- rollback drop table qb_bills;

CALL createEndpointAndPermissions('QuickBooks', 'POST', '/quickbooks/bills', 'Create Quickbooks Bills', '1,2');
CALL createEndpointAndPermissions('QuickBooks','GET','/quickbooks/bills','Get all not Quickbooks posted Bills','1,2');
