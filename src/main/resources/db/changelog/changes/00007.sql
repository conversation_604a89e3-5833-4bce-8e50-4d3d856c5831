-- liquibase formatted sql

-- changeset andreas:20241213-1 endDelimiter:/

-- function to create index if not exists
DROP PROCEDURE IF EXISTS `CreateIndex`
/

CREATE PROCEDURE `CreateIndex`
(
    given_table    VARCHAR(64),
    given_index    VARCHAR(64),
    given_columns  VARCHAR(64)
)
BEGIN

    DECLARE IndexIsThere INTEGER;

    SELECT COUNT(1) INTO IndexIsThere
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_schema = database()
      AND   table_name   = given_table
      AND   index_name   = given_index;

    IF IndexIsThere = 0 THEN
        SET @sqlstmt = CONCAT('CREATE INDEX ',given_index,' ON ',
                              given_table,' (',given_columns,')');
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    ELSE
        SELECT CONCAT('Index ',given_index,' already exists on Table ',
                      database(),'.',given_table) CreateindexErrorMessage;
    END IF;

END


-- changeset andreas:20241213-2

set sql_safe_updates = 0;

-- email_metrics
update `email_metrics` set `sg_bounce_status` = '' where `sg_bounce_status` is null;
update `email_metrics` set `sg_bounce_type` = '' where `sg_bounce_type` is null;
update `email_metrics` set `sg_event` = '' where `sg_event` is null;
update `email_metrics` set `sg_timestamp` = '' where `sg_timestamp` is null;
alter table email_metrics
    change column `sg_bounce_status` `sg_bounce_status` varchar(255) not null default '',
    change column `sg_bounce_type` `sg_bounce_type` varchar(255) not null default '',
    change column `sg_event` `sg_event` varchar(255) not null default '',
    change column `sg_timestamp` `sg_timestamp` varchar(255) not null default '' ;

-- email_queue

call CreateIndex('email_queue', 'ix_descr','description ASC');
call CreateIndex('email_queue', 'ix_replyto','replyto_email ASC');
call CreateIndex('email_queue', 'ix_sg_status','sg_status ASC');

update `email_queue` set `description` = '' where `description` is null;
update `email_queue` set `replyto_email` = '' where `replyto_email` is null;
update `email_queue` set `sg_status` = '' where `sg_status` is null;
alter table email_queue
    change column `description` `description` varchar(500) not null default '',
    change column `replyto_email` `replyto_email` varchar(100) not null default '',
    change column `sg_status` `sg_status` varchar(100) not null default '' ;


-- equipment
update `equipment` set `mcleod_mapping` = '' where `mcleod_mapping` is null;
update `equipment` set `PE_mapping` = '' where `PE_mapping` is null;
alter table equipment
    change column `mcleod_mapping` `mcleod_mapping` varchar(25) not null default '',
    change column `PE_mapping` `PE_mapping` varchar(25) not null default '' ;

-- external_grades
update `external_grades` set `external_grade_description` = '' where `external_grade_description` is null;
alter table external_grades
    change column `external_grade_description` `external_grade_description` varchar(500) not null default '' ;

-- file_types
update `file_types` set `file_type` = '' where `file_type` is null;
update `file_types` set `ocr_model_id` = '' where `ocr_model_id` is null;
alter table file_types
    change column `file_type` `file_type` varchar(75) not null default '',
    change column `ocr_model_id` `ocr_model_id` varchar(100) not null default '' ;

-- files
update `files` set `fields_submodel_id` = '' where `fields_submodel_id` is null;
alter table files
    change column `fields_submodel_id` `fields_submodel_id` varchar(50) not null default '' ;

-- fl_user_settings
update `fl_user_settings` set `currentSessionID` = '' where `currentSessionID` is null;
update `fl_user_settings` set `forum_view_types` = '' where `forum_view_types` is null;
update `fl_user_settings` set `last_login_device` = '' where `last_login_device` is null;
update `fl_user_settings` set `last_mobile_device` = '' where `last_mobile_device` is null;
alter table fl_user_settings
    change column `currentSessionID` `currentSessionID` varchar(255) not null default '',
    change column `forum_view_types` `forum_view_types` varchar(45) not null default '1,2,3,4,5,6,7,8,9',
    change column `last_login_device` `last_login_device` varchar(25) not null default '',
    change column `last_mobile_device` `last_mobile_device` varchar(50) not null default '' ;

-- forum_post
update `forum_post` set `alias` = '' where `alias` is null;
update `forum_post` set `contact_email` = '' where `contact_email` is null;
update `forum_post` set `contact_name` = '' where `contact_name` is null;
update `forum_post` set `contact_phone` = '' where `contact_phone` is null;
update `forum_post` set `current_address` = '' where `current_address` is null;
update `forum_post` set `current_city` = '' where `current_city` is null;
update `forum_post` set `current_country` = '' where `current_country` is null;
update `forum_post` set `current_state` = '' where `current_state` is null;
update `forum_post` set `current_zip` = '' where `current_zip` is null;
update `forum_post` set `forum_link` = '' where `forum_link` is null;
alter table forum_post
    change column `alias` `alias` varchar(255) not null default '',
    change column `contact_email` `contact_email` varchar(100) not null default '',
    change column `contact_name` `contact_name` varchar(100) not null default '',
    change column `contact_phone` `contact_phone` varchar(20) not null default '',
    change column `current_address` `current_address` varchar(150) not null default '',
    change column `current_city` `current_city` varchar(60) not null default '',
    change column `current_country` `current_country` varchar(5) not null default '',
    change column `current_state` `current_state` varchar(2) not null default '',
    change column `current_zip` `current_zip` varchar(10) not null default '',
    change column `forum_link` `forum_link` varchar(255) not null default '' ;

-- gavilon_data
update `gavilon_data` set `carrieraddress1` = '' where `carrieraddress1` is null;
update `gavilon_data` set `carrieraddress2` = '' where `carrieraddress2` is null;
update `gavilon_data` set `carriercity` = '' where `carriercity` is null;
update `gavilon_data` set `carriername` = '' where `carriername` is null;
update `gavilon_data` set `carrierzip` = '' where `carrierzip` is null;
update `gavilon_data` set `commodity` = '' where `commodity` is null;
update `gavilon_data` set `commoditycode` = '' where `commoditycode` is null;
update `gavilon_data` set `destinationcity` = '' where `destinationcity` is null;
update `gavilon_data` set `destinationcountry` = '' where `destinationcountry` is null;
update `gavilon_data` set `destinationstate` = '' where `destinationstate` is null;
update `gavilon_data` set `destinationzip` = '' where `destinationzip` is null;
update `gavilon_data` set `equipmenttype` = '' where `equipmenttype` is null;
update `gavilon_data` set `origincity` = '' where `origincity` is null;
update `gavilon_data` set `origincountry` = '' where `origincountry` is null;
update `gavilon_data` set `originstate` = '' where `originstate` is null;
update `gavilon_data` set `originzip` = '' where `originzip` is null;
update `gavilon_data` set `ratetype` = '' where `ratetype` is null;
update `gavilon_data` set `tradercontact` = '' where `tradercontact` is null;
update `gavilon_data` set `tradername` = '' where `tradername` is null;
alter table gavilon_data
    change column `carrieraddress1` `carrieraddress1` varchar(100) not null default '',
    change column `carrieraddress2` `carrieraddress2` varchar(100) not null default '',
    change column `carriercity` `carriercity` varchar(100) not null default '',
    change column `carriername` `carriername` varchar(100) not null default '',
    change column `carrierzip` `carrierzip` varchar(100) not null default '',
    change column `commodity` `commodity` varchar(100) not null default '',
    change column `commoditycode` `commoditycode` varchar(20) not null default '',
    change column `destinationcity` `destinationcity` varchar(100) not null default '',
    change column `destinationcountry` `destinationcountry` varchar(100) not null default '',
    change column `destinationstate` `destinationstate` varchar(100) not null default '',
    change column `destinationzip` `destinationzip` varchar(100) not null default '',
    change column `equipmenttype` `equipmenttype` varchar(100) not null default '',
    change column `origincity` `origincity` varchar(100) not null default '',
    change column `origincountry` `origincountry` varchar(100) not null default '',
    change column `originstate` `originstate` varchar(100) not null default '',
    change column `originzip` `originzip` varchar(100) not null default '',
    change column `ratetype` `ratetype` varchar(100) not null default '',
    change column `tradercontact` `tradercontact` varchar(100) not null default '',
    change column `tradername` `tradername` varchar(100) not null default '' ;

-- grade_schedules
update `grade_schedules` set `external_commodity_id` = '' where `external_commodity_id` is null;
alter table grade_schedules
    change column `external_commodity_id` `external_commodity_id` varchar(50) not null default '' ;

-- grades
update `grades` set `grade_description` = '' where `grade_description` is null;
alter table grades
    change column `grade_description` `grade_description` varchar(500) not null default '' ;

-- insurance_leads
update `insurance_leads` set `current_provider` = '' where `current_provider` is null;
update `insurance_leads` set `email` = '' where `email` is null;
update `insurance_leads` set `phone` = '' where `phone` is null;
alter table insurance_leads
    change column `current_provider` `current_provider` varchar(450) not null default '',
    change column `email` `email` varchar(255) not null default '',
    change column `phone` `phone` varchar(50) not null default '' ;

-- invoice_batches
update `invoice_batches` set `message` = '' where `message` is null;
alter table invoice_batches
    change column `message` `message` varchar(100) not null default '' ;

-- invoice_payments
update `invoice_payments` set `authorize_auth_code` = '' where `authorize_auth_code` is null;
update `invoice_payments` set `authorize_transaction_id` = '' where `authorize_transaction_id` is null;
update `invoice_payments` set `bank_name` = '' where `bank_name` is null;
update `invoice_payments` set `check_number` = '' where `check_number` is null;
update `invoice_payments` set `last_four_account_numbers` = '' where `last_four_account_numbers` is null;
alter table invoice_payments
    change column `authorize_auth_code` `authorize_auth_code` varchar(40) not null default '',
    change column `authorize_transaction_id` `authorize_transaction_id` varchar(40) not null default '',
    change column `bank_name` `bank_name` varchar(50) not null default '',
    change column `check_number` `check_number` varchar(40) not null default '',
    change column `last_four_account_numbers` `last_four_account_numbers` varchar(4) not null default '' ;

-- invoices
update `invoices` set `bank_name` = '' where `bank_name` is null;
update `invoices` set `bill_address` = '' where `bill_address` is null;
update `invoices` set `bill_attn_to` = '' where `bill_attn_to` is null;
update `invoices` set `bill_delivery` = '' where `bill_delivery` is null;
update `invoices` set `bill_description` = '' where `bill_description` is null;
update `invoices` set `bill_phone` = '' where `bill_phone` is null;
update `invoices` set `bill_to` = '' where `bill_to` is null;
update `invoices` set `check_number` = '' where `check_number` is null;
update `invoices` set `delete_message` = '' where `delete_message` is null;
update `invoices` set `email` = '' where `email` is null;
update `invoices` set `last_four_account_numbers` = '' where `last_four_account_numbers` is null;
update `invoices` set `old_authorize_auth_code` = '' where `old_authorize_auth_code` is null;
update `invoices` set `old_authorize_transaction_id` = '' where `old_authorize_transaction_id` is null;
update `invoices` set `state` = '' where `state` is null;
alter table invoices
    change column `bank_name` `bank_name` varchar(50) not null default '',
    change column `bill_address` `bill_address` varchar(300) not null default '',
    change column `bill_attn_to` `bill_attn_to` varchar(50) not null default '',
    change column `bill_delivery` `bill_delivery` varchar(25) not null default '',
    change column `bill_description` `bill_description` varchar(75) not null default '',
    change column `bill_phone` `bill_phone` varchar(25) not null default '',
    change column `bill_to` `bill_to` varchar(75) not null default '',
    change column `check_number` `check_number` varchar(40) not null default '',
    change column `delete_message` `delete_message` varchar(1000) not null default '',
    change column `email` `email` varchar(100) not null default '',
    change column `last_four_account_numbers` `last_four_account_numbers` varchar(4) not null default '',
    change column `old_authorize_auth_code` `old_authorize_auth_code` varchar(40) not null default '',
    change column `old_authorize_transaction_id` `old_authorize_transaction_id` varchar(40) not null default '',
    change column `state` `state` varchar(2) not null default '' ;

-- jqm_ranges
update `jqm_ranges` set `range_color` = '' where `range_color` is null;
alter table jqm_ranges
    change column `range_color` `range_color` varchar(8) not null default '' ;

-- jqm_states
update `jqm_states` set `state` = '' where `state` is null;
alter table jqm_states
    change column `state` `state` varchar(50) not null default '' ;

-- lo_carrier_loads
update `lo_carrier_loads` set `destination_country` = '' where `destination_country` is null;
update `lo_carrier_loads` set `origin_country` = '' where `origin_country` is null;
alter table lo_carrier_loads
    change column `destination_country` `destination_country` varchar(20) not null default 'US',
    change column `origin_country` `origin_country` varchar(20) not null default 'US' ;

-- lo_carriers
update `lo_carriers` set `city` = '' where `city` is null;
update `lo_carriers` set `contact_name` = '' where `contact_name` is null;
update `lo_carriers` set `country` = '' where `country` is null;
update `lo_carriers` set `email` = '' where `email` is null;
update `lo_carriers` set `fax` = '' where `fax` is null;
update `lo_carriers` set `insurance_company` = '' where `insurance_company` is null;
update `lo_carriers` set `mailing_address` = '' where `mailing_address` is null;
update `lo_carriers` set `phone_1` = '' where `phone_1` is null;
update `lo_carriers` set `phone_1_comments` = '' where `phone_1_comments` is null;
update `lo_carriers` set `phone_2` = '' where `phone_2` is null;
update `lo_carriers` set `phone_2_comments` = '' where `phone_2_comments` is null;
update `lo_carriers` set `phone_3` = '' where `phone_3` is null;
update `lo_carriers` set `phone_3_comments` = '' where `phone_3_comments` is null;
update `lo_carriers` set `state` = '' where `state` is null;
update `lo_carriers` set `zip` = '' where `zip` is null;
alter table lo_carriers
    change column `city` `city` varchar(100) not null default '',
    change column `contact_name` `contact_name` varchar(75) not null default '',
    change column `country` `country` varchar(10) not null default 'US',
    change column `email` `email` varchar(100) not null default '',
    change column `fax` `fax` varchar(50) not null default '',
    change column `insurance_company` `insurance_company` varchar(100) not null default '',
    change column `mailing_address` `mailing_address` varchar(1000) not null default '',
    change column `phone_1` `phone_1` varchar(50) not null default '',
    change column `phone_1_comments` `phone_1_comments` varchar(30) not null default '',
    change column `phone_2` `phone_2` varchar(50) not null default '',
    change column `phone_2_comments` `phone_2_comments` varchar(30) not null default '',
    change column `phone_3` `phone_3` varchar(50) not null default '',
    change column `phone_3_comments` `phone_3_comments` varchar(30) not null default '',
    change column `state` `state` varchar(2) not null default '',
    change column `zip` `zip` varchar(20) not null default '' ;

-- lo_todo
update `lo_todo` set `due_date` = '' where `due_date` is null;
alter table lo_todo
    change column `due_date` `due_date` varchar(255) not null default '' ;

-- lo_user_settings
update `lo_user_settings` set `currentSessionID` = '' where `currentSessionID` is null;
alter table lo_user_settings
    change column `currentSessionID` `currentSessionID` varchar(255) not null default '' ;

-- load_alerts
update `load_alerts` set `destination_city` = '' where `destination_city` is null;
update `load_alerts` set `old_alert_method` = '' where `old_alert_method` is null;
alter table load_alerts
    change column `destination_city` `destination_city` varchar(60) not null default '',
    change column `old_alert_method` `old_alert_method` varchar(10) not null default '' ;

-- load_analytics_emails
update `load_analytics_emails` set `equipment` = '' where `equipment` is null;
update `load_analytics_emails` set `state` = '' where `state` is null;
update `load_analytics_emails` set `subject` = '' where `subject` is null;
alter table load_analytics_emails
    change column `equipment` `equipment` varchar(255) not null default '',
    change column `state` `state` varchar(255) not null default '',
    change column `subject` `subject` varchar(255) not null default '' ;

-- load_analytics_search
update `load_analytics_search` set `equipment` = '' where `equipment` is null;
alter table load_analytics_search
    change column `equipment` `equipment` varchar(255) not null default '' ;

-- load_assignment_files
update `load_assignment_files` set `attention_note` = '' where `attention_note` is null;
alter table load_assignment_files
    change column `attention_note` `attention_note` varchar(1000) not null default '' ;

-- load_assignment_geo_history
update `load_assignment_geo_history` set `event` = '' where `event` is null;
alter table load_assignment_geo_history
    change column `event` `event` varchar(250) not null default '' ;

-- load_assignments
update `load_assignments` set `reroute_reason` = '' where `reroute_reason` is null;
update `load_assignments` set `reroute_request_reason` = '' where `reroute_request_reason` is null;
alter table load_assignments
    change column `reroute_reason` `reroute_reason` varchar(1000) not null default '',
    change column `reroute_request_reason` `reroute_request_reason` varchar(100) not null default '' ;

-- load_invoice_items
update `load_invoice_items` set `bill_rate_message` = '' where `bill_rate_message` is null;
update `load_invoice_items` set `commodity` = '' where `commodity` is null;
update `load_invoice_items` set `item_description` = '' where `item_description` is null;
update `load_invoice_items` set `previous_bill_rate_message` = '' where `previous_bill_rate_message` is null;
alter table load_invoice_items
    change column `bill_rate_message` `bill_rate_message` varchar(100) not null default '',
    change column `commodity` `commodity` varchar(255) not null default '',
    change column `item_description` `item_description` varchar(1000) not null default '',
    change column `previous_bill_rate_message` `previous_bill_rate_message` varchar(100) not null default '' ;

-- load_invoice_notes
update `load_invoice_notes` set `load_invoice_note` = '' where `load_invoice_note` is null;
alter table load_invoice_notes
    change column `load_invoice_note` `load_invoice_note` varchar(200) not null default '' ;

-- load_invoice_payments_old
update `load_invoice_payments_old` set `payment_notes` = '' where `payment_notes` is null;
alter table load_invoice_payments_old
    change column `payment_notes` `payment_notes` varchar(200) not null default '' ;

-- load_invoices
update `load_invoices` set `email_status` = '' where `email_status` is null;
alter table load_invoices
    change column `email_status` `email_status` varchar(100) not null default '' ;


-- load_views
call CreateIndex('load_views', 'ix_destination_city', 'destination_city ASC');
call CreateIndex('load_views', 'ix_destination_state', 'destination_state ASC');
call CreateIndex('load_views', 'ix_equipment', 'equipment ASC');
call CreateIndex('load_views', 'ix_origin_city', 'origin_city ASC');
call CreateIndex('load_views', 'ix_origin_state', 'origin_state ASC');

update `load_views` set `destination_city` = '' where `destination_city` is null;
update `load_views` set `destination_state` = '' where `destination_state` is null;
update `load_views` set `equipment` = '' where `equipment` is null;
update `load_views` set `origin_city` = '' where `origin_city` is null;
update `load_views` set `origin_state` = '' where `origin_state` is null;
alter table load_views
    change column `destination_city` `destination_city` varchar(60) not null default '',
    change column `destination_state` `destination_state` varchar(5) not null default '',
    change column `equipment` `equipment` varchar(250) not null default '',
    change column `origin_city` `origin_city` varchar(60) not null default '',
    change column `origin_state` `origin_state` varchar(5) not null default '' ;



-- loads
call CreateIndex('loads', 'ix_contact_name', 'contact_name ASC');
call CreateIndex('loads', 'ix_contact_name', 'contact_name ASC');
call CreateIndex('loads', 'ix_contact_number', 'contact_number ASC');
call CreateIndex('loads', 'ix_destination_zipcode', 'destination_zipcode ASC');
call CreateIndex('loads', 'ix_estimated_weight', 'estimated_weight ASC');
call CreateIndex('loads', 'ix_external_rate_product_category_id', 'external_rate_product_category_id ASC');
call CreateIndex('loads', 'ix_lo_comments', 'lo_comments ASC');
call CreateIndex('loads', 'ix_lo_commodity', 'lo_commodity ASC');
call CreateIndex('loads', 'ix_lo_contract_number', 'lo_contract_number ASC');
-- call CreateIndex('loads', 'ix_lo_destination_notes', 'lo_destination_notes ASC');
-- call CreateIndex('loads', 'ix_lo_origin_notes', 'lo_origin_notes ASC');
-- call CreateIndex('loads', 'ix_load_comments', 'load_comments ASC');
call CreateIndex('loads', 'ix_nickname', 'nickname ASC');
call CreateIndex('loads', 'ix_origin_zipcode', 'origin_zipcode ASC');
call CreateIndex('loads', 'ix_outside_tracking_id', 'outside_tracking_id ASC');
call CreateIndex('loads', 'ix_radius', 'radius ASC');
call CreateIndex('loads', 'ix_send_to_amount', 'send_to_amount ASC');
call CreateIndex('loads', 'ix_text_type', 'text_type ASC');

update `loads` set `contact_name` = '' where `contact_name` is null;
update `loads` set `contact_number` = '' where `contact_number` is null;
update `loads` set `destination_zipcode` = '' where `destination_zipcode` is null;
update `loads` set `estimated_weight` = '' where `estimated_weight` is null;
update `loads` set `external_rate_product_category_id` = '' where `external_rate_product_category_id` is null;
update `loads` set `lo_comments` = '' where `lo_comments` is null;
update `loads` set `lo_commodity` = '' where `lo_commodity` is null;
update `loads` set `lo_contract_number` = '' where `lo_contract_number` is null;
update `loads` set `lo_destination_notes` = '' where `lo_destination_notes` is null;
update `loads` set `lo_origin_notes` = '' where `lo_origin_notes` is null;
update `loads` set `load_comments` = '' where `load_comments` is null;
update `loads` set `nickname` = '' where `nickname` is null;
update `loads` set `origin_zipcode` = '' where `origin_zipcode` is null;
update `loads` set `outside_tracking_id` = '' where `outside_tracking_id` is null;
update `loads` set `radius` = '' where `radius` is null;
update `loads` set `send_to_amount` = '' where `send_to_amount` is null;
update `loads` set `text_type` = '' where `text_type` is null;
alter table loads
    change column `contact_name` `contact_name` varchar(100) not null default '',
    change column `contact_number` `contact_number` varchar(50) not null default '',
    change column `destination_zipcode` `destination_zipcode` varchar(7) not null default '',
    change column `estimated_weight` `estimated_weight` varchar(20) not null default '',
    change column `external_rate_product_category_id` `external_rate_product_category_id` varchar(10) not null default '',
    change column `lo_comments` `lo_comments` varchar(500) not null default '',
    change column `lo_commodity` `lo_commodity` varchar(100) not null default '',
    change column `lo_contract_number` `lo_contract_number` varchar(75) not null default '',
    change column `lo_destination_notes` `lo_destination_notes` varchar(1000) not null default '',
    change column `lo_origin_notes` `lo_origin_notes` varchar(1000) not null default '',
    change column `load_comments` `load_comments` varchar(2000) not null default '',
    change column `nickname` `nickname` varchar(50) not null default '',
    change column `origin_zipcode` `origin_zipcode` varchar(7) not null default '',
    change column `outside_tracking_id` `outside_tracking_id` varchar(100) not null default '',
    change column `radius` `radius` varchar(4) not null default '',
    change column `send_to_amount` `send_to_amount` varchar(2) not null default '',
    change column `text_type` `text_type` varchar(15) not null default '' ;




-- logins
call CreateIndex('logins', 'ix_app_version', 'app_version ASC');
call CreateIndex('logins', 'ix_city', 'city ASC');
call CreateIndex('logins', 'ix_country', 'country ASC');
call CreateIndex('logins', 'ix_device', 'device ASC');
call CreateIndex('logins', 'ix_device_model', 'device_model ASC');
call CreateIndex('logins', 'ix_ip_address', 'ip_address ASC');
call CreateIndex('logins', 'ix_os_version', 'os_version ASC');
call CreateIndex('logins', 'ix_state', 'state ASC');

update `logins` set `app_version` = '' where `app_version` is null;
update `logins` set `city` = '' where `city` is null;
update `logins` set `country` = '' where `country` is null;
update `logins` set `device` = '' where `device` is null;
update `logins` set `device_model` = '' where `device_model` is null;
update `logins` set `ip_address` = '' where `ip_address` is null;
update `logins` set `os_version` = '' where `os_version` is null;
update `logins` set `state` = '' where `state` is null;

alter table logins
    change column `app_version` `app_version` varchar(25) not null default '',
    change column `city` `city` varchar(60) not null default '',
    change column `country` `country` varchar(45) not null default '',
    change column `device` `device` varchar(50) not null default '',
    change column `device_model` `device_model` varchar(50) not null default '',
    change column `ip_address` `ip_address` varchar(100) not null default '',
    change column `os_version` `os_version` varchar(25) not null default '',
    change column `state` `state` varchar(25) not null default '' ;


-- mcleod_data
update `mcleod_data` set `callback_phone` = '' where `callback_phone` is null;
update `mcleod_data` set `comment_1` = '' where `comment_1` is null;
update `mcleod_data` set `comment_2` = '' where `comment_2` is null;
update `mcleod_data` set `dispatcher_id` = '' where `dispatcher_id` is null;
update `mcleod_data` set `enhancements` = '' where `enhancements` is null;
update `mcleod_data` set `equipment_type` = '' where `equipment_type` is null;
update `mcleod_data` set `from_city` = '' where `from_city` is null;
update `mcleod_data` set `from_postal_code` = '' where `from_postal_code` is null;
update `mcleod_data` set `locations_list` = '' where `locations_list` is null;
update `mcleod_data` set `posting_id` = '' where `posting_id` is null;
update `mcleod_data` set `rate` = '' where `rate` is null;
update `mcleod_data` set `source_company_id` = '' where `source_company_id` is null;
update `mcleod_data` set `source_user_id` = '' where `source_user_id` is null;
update `mcleod_data` set `to_city` = '' where `to_city` is null;
update `mcleod_data` set `to_postal_code` = '' where `to_postal_code` is null;
alter table mcleod_data
    change column `callback_phone` `callback_phone` varchar(10) not null default '',
    change column `comment_1` `comment_1` varchar(70) not null default '',
    change column `comment_2` `comment_2` varchar(70) not null default '',
    change column `dispatcher_id` `dispatcher_id` varchar(10) not null default '',
    change column `enhancements` `enhancements` varchar(4) not null default '',
    change column `equipment_type` `equipment_type` varchar(2) not null default '',
    change column `from_city` `from_city` varchar(14) not null default '',
    change column `from_postal_code` `from_postal_code` varchar(9) not null default '',
    change column `locations_list` `locations_list` varchar(70) not null default '',
    change column `posting_id` `posting_id` varchar(8) not null default '',
    change column `rate` `rate` varchar(5) not null default '',
    change column `source_company_id` `source_company_id` varchar(45) not null default '',
    change column `source_user_id` `source_user_id` varchar(32) not null default '',
    change column `to_city` `to_city` varchar(14) not null default '',
    change column `to_postal_code` `to_postal_code` varchar(9) not null default '' ;

-- message_files
update `message_files` set `file_url` = '' where `file_url` is null;
update `message_files` set `mime_type` = '' where `mime_type` is null;
update `message_files` set `thumb_url` = '' where `thumb_url` is null;
alter table message_files
    change column `file_url` `file_url` varchar(500) not null default '',
    change column `mime_type` `mime_type` varchar(255) not null default '',
    change column `thumb_url` `thumb_url` varchar(500) not null default '' ;

-- message_groups
update `message_groups` set `group_name` = '' where `group_name` is null;
alter table message_groups
    change column `group_name` `group_name` varchar(100) not null default '' ;

-- messages
update `messages` set `extra_data` = '' where `extra_data` is null;
update `messages` set `from_email` = '' where `from_email` is null;
update `messages` set `from_phone` = '' where `from_phone` is null;
update `messages` set `message` = '' where `message` is null;
update `messages` set `to_email` = '' where `to_email` is null;
update `messages` set `to_phone` = '' where `to_phone` is null;
alter table messages
    change column `extra_data` `extra_data` varchar(1000) not null default '',
    change column `from_email` `from_email` varchar(200) not null default '',
    change column `from_phone` `from_phone` varchar(50) not null default '',
    change column `message` `message` varchar(4000) not null default '',
    change column `to_email` `to_email` varchar(200) not null default '',
    change column `to_phone` `to_phone` varchar(50) not null default '' ;

-- messages_archived
update `messages_archived` set `archived_email` = '' where `archived_email` is null;
update `messages_archived` set `archived_phone` = '' where `archived_phone` is null;
alter table messages_archived
    change column `archived_email` `archived_email` varchar(200) not null default '',
    change column `archived_phone` `archived_phone` varchar(50) not null default '' ;

-- mileages
update `mileages` set `lcostmile` = '' where `lcostmile` is null;
update `mileages` set `lestghg` = '' where `lestghg` is null;
update `mileages` set `lhours` = '' where `lhours` is null;
update `mileages` set `tcostmile` = '' where `tcostmile` is null;
update `mileages` set `testghg` = '' where `testghg` is null;
update `mileages` set `thours` = '' where `thours` is null;
alter table mileages
    change column `lcostmile` `lcostmile` varchar(5) not null default '',
    change column `lestghg` `lestghg` varchar(6) not null default '',
    change column `lhours` `lhours` varchar(6) not null default '',
    change column `tcostmile` `tcostmile` varchar(5) not null default '',
    change column `testghg` `testghg` varchar(6) not null default '',
    change column `thours` `thours` varchar(6) not null default '' ;

-- notifications
call CreateIndex('notifications', 'ix_data_metadata', 'data_metadata ASC');
-- call CreateIndex('notifications', 'ix_device_ids', 'device_ids ASC');
-- call CreateIndex('notifications', 'ix_notification_tokens', 'notification_tokens ASC');

update `notifications` set `data_metadata` = '' where `data_metadata` is null;
update `notifications` set `device_ids` = '' where `device_ids` is null;
update `notifications` set `notification_tokens` = '' where `notification_tokens` is null;

alter table notifications
    change column `data_metadata` `data_metadata` varchar(256) not null default '',
    change column `device_ids` `device_ids` varchar(1024) not null default '',
    change column `notification_tokens` `notification_tokens` varchar(2500) not null default '' ;


-- offers
update `offers` set `replyto_email` = '' where `replyto_email` is null;
update `offers` set `subject` = '' where `subject` is null;
alter table offers
    change column `replyto_email` `replyto_email` varchar(75) not null default '',
    change column `subject` `subject` varchar(500) not null default '' ;

-- orders
update `orders` set `arb_subscription_id` = '' where `arb_subscription_id` is null;
update `orders` set `authorize_auth_code` = '' where `authorize_auth_code` is null;
update `orders` set `authorize_transaction_id` = '' where `authorize_transaction_id` is null;
update `orders` set `bank_name` = '' where `bank_name` is null;
update `orders` set `bill_address` = '' where `bill_address` is null;
update `orders` set `bill_amount` = '' where `bill_amount` is null;
update `orders` set `bill_attn_to` = '' where `bill_attn_to` is null;
update `orders` set `bill_delivery` = '' where `bill_delivery` is null;
update `orders` set `bill_description` = '' where `bill_description` is null;
update `orders` set `bill_phone` = '' where `bill_phone` is null;
update `orders` set `bill_to` = '' where `bill_to` is null;
update `orders` set `check_number` = '' where `check_number` is null;
update `orders` set `email` = '' where `email` is null;
update `orders` set `last_four_account_numbers` = '' where `last_four_account_numbers` is null;
update `orders` set `payment_type` = '' where `payment_type` is null;
update `orders` set `state` = '' where `state` is null;
alter table orders
    change column `arb_subscription_id` `arb_subscription_id` varchar(50) not null default '',
    change column `authorize_auth_code` `authorize_auth_code` varchar(40) not null default '',
    change column `authorize_transaction_id` `authorize_transaction_id` varchar(40) not null default '',
    change column `bank_name` `bank_name` varchar(50) not null default '',
    change column `bill_address` `bill_address` varchar(300) not null default '',
    change column `bill_amount` `bill_amount` varchar(15) not null default '',
    change column `bill_attn_to` `bill_attn_to` varchar(50) not null default '',
    change column `bill_delivery` `bill_delivery` varchar(25) not null default '',
    change column `bill_description` `bill_description` varchar(75) not null default '',
    change column `bill_phone` `bill_phone` varchar(25) not null default '',
    change column `bill_to` `bill_to` varchar(75) not null default '',
    change column `check_number` `check_number` varchar(40) not null default '',
    change column `email` `email` varchar(100) not null default '',
    change column `last_four_account_numbers` `last_four_account_numbers` varchar(4) not null default '',
    change column `payment_type` `payment_type` varchar(15) not null default '',
    change column `state` `state` varchar(2) not null default '' ;

-- phone_numbers
update `phone_numbers` set `carrier_type` = '' where `carrier_type` is null;
update `phone_numbers` set `std_format` = '' where `std_format` is null;
alter table phone_numbers
    change column `carrier_type` `carrier_type` varchar(45) not null default '',
    change column `std_format` `std_format` varchar(20) not null default '' ;

-- rate_product_categories
update `rate_product_categories` set `rate_product_category_equipment` = '' where `rate_product_category_equipment` is null;
alter table rate_product_categories
    change column `rate_product_category_equipment` `rate_product_category_equipment` varchar(100) not null default '' ;

-- rate_products
update `rate_products` set `rate_product_category_name` = '' where `rate_product_category_name` is null;
alter table rate_products
    change column `rate_product_category_name` `rate_product_category_name` varchar(50) not null default '' ;

-- rate_search
update `rate_search` set `rate_product_category_ids` = '' where `rate_product_category_ids` is null;
alter table rate_search
    change column `rate_product_category_ids` `rate_product_category_ids` varchar(100) not null default '' ;

-- rates
update `rates` set `gw_carrier_code` = '' where `gw_carrier_code` is null;
update `rates` set `gw_destination_city` = '' where `gw_destination_city` is null;
update `rates` set `gw_destination_state` = '' where `gw_destination_state` is null;
update `rates` set `gw_origin_city` = '' where `gw_origin_city` is null;
update `rates` set `gw_origin_state` = '' where `gw_origin_state` is null;
update `rates` set `gw_product` = '' where `gw_product` is null;
update `rates` set `gw_remarks` = '' where `gw_remarks` is null;
update `rates` set `rate_destination_city` = '' where `rate_destination_city` is null;
update `rates` set `rate_destination_state` = '' where `rate_destination_state` is null;
update `rates` set `rate_origin_city` = '' where `rate_origin_city` is null;
update `rates` set `rate_origin_state` = '' where `rate_origin_state` is null;
alter table rates
    change column `gw_carrier_code` `gw_carrier_code` varchar(45) not null default '',
    change column `gw_destination_city` `gw_destination_city` varchar(75) not null default '',
    change column `gw_destination_state` `gw_destination_state` varchar(4) not null default '',
    change column `gw_origin_city` `gw_origin_city` varchar(75) not null default '',
    change column `gw_origin_state` `gw_origin_state` varchar(4) not null default '',
    change column `gw_product` `gw_product` varchar(75) not null default '',
    change column `gw_remarks` `gw_remarks` varchar(1000) not null default '',
    change column `rate_destination_city` `rate_destination_city` varchar(75) not null default '',
    change column `rate_destination_state` `rate_destination_state` varchar(2) not null default '',
    change column `rate_origin_city` `rate_origin_city` varchar(75) not null default '',
    change column `rate_origin_state` `rate_origin_state` varchar(2) not null default '' ;

-- ratings
update `ratings` set `carrier_shipper` = '' where `carrier_shipper` is null;
update `ratings` set `feedback` = '' where `feedback` is null;
alter table ratings
    change column `carrier_shipper` `carrier_shipper` varchar(25) not null default '',
    change column `feedback` `feedback` varchar(250) not null default '' ;

-- request_log
call CreateIndex('request_log', 'ix_response_status', 'response_status ASC');

update `request_log` set `response_status` = '' where `response_status` is null;

alter table request_log
    change column `response_status` `response_status` varchar(10) not null default '' ;


-- scales
update `scales` set `description` = '' where `description` is null;
update `scales` set `road_direction` = '' where `road_direction` is null;
alter table scales
    change column `description` `description` varchar(255) not null default '',
    change column `road_direction` `road_direction` varchar(20) not null default '' ;

-- scales_static
update `scales_static` set `address` = '' where `address` is null;
update `scales_static` set `comments` = '' where `comments` is null;
update `scales_static` set `country` = '' where `country` is null;
update `scales_static` set `direction` = '' where `direction` is null;
update `scales_static` set `exit_after` = '' where `exit_after` is null;
update `scales_static` set `exit_before` = '' where `exit_before` is null;
update `scales_static` set `google_embed` = '' where `google_embed` is null;
update `scales_static` set `google_street_embed` = '' where `google_street_embed` is null;
update `scales_static` set `highway` = '' where `highway` is null;
update `scales_static` set `location_description` = '' where `location_description` is null;
update `scales_static` set `mile_marker` = '' where `mile_marker` is null;
update `scales_static` set `parking` = '' where `parking` is null;
update `scales_static` set `phone_1` = '' where `phone_1` is null;
update `scales_static` set `prepass` = '' where `prepass` is null;
update `scales_static` set `required_to_stop` = '' where `required_to_stop` is null;
update `scales_static` set `scale_name` = '' where `scale_name` is null;
update `scales_static` set `type` = '' where `type` is null;
alter table scales_static
    change column `address` `address` varchar(255) not null default '',
    change column `comments` `comments` varchar(255) not null default '',
    change column `country` `country` varchar(255) not null default '',
    change column `direction` `direction` varchar(255) not null default '',
    change column `exit_after` `exit_after` varchar(255) not null default '',
    change column `exit_before` `exit_before` varchar(255) not null default '',
    change column `google_embed` `google_embed` varchar(1000) not null default '',
    change column `google_street_embed` `google_street_embed` varchar(1000) not null default '',
    change column `highway` `highway` varchar(255) not null default '',
    change column `location_description` `location_description` varchar(255) not null default '',
    change column `mile_marker` `mile_marker` varchar(255) not null default '',
    change column `parking` `parking` varchar(255) not null default '',
    change column `phone_1` `phone_1` varchar(255) not null default '',
    change column `prepass` `prepass` varchar(255) not null default '',
    change column `required_to_stop` `required_to_stop` varchar(255) not null default '',
    change column `scale_name` `scale_name` varchar(255) not null default '',
    change column `type` `type` varchar(255) not null default '' ;

-- sched_tasks
update `sched_tasks` set `crontime` = '' where `crontime` is null;
update `sched_tasks` set `interval` = '' where `interval` is null;
update `sched_tasks` set `onComplete` = '' where `onComplete` is null;
update `sched_tasks` set `repeat` = '' where `repeat` is null;
alter table sched_tasks
    change column `crontime` `crontime` varchar(30) not null default '',
    change column `interval` `interval` varchar(20) not null default '',
    change column `onComplete` `onComplete` varchar(550) not null default '',
    change column `repeat` `repeat` varchar(20) not null default '' ;

-- schedules_log_sync_mcp_status
update `schedules_log_sync_mcp_status` set `risk_assessment_overall` = '' where `risk_assessment_overall` is null;
alter table schedules_log_sync_mcp_status
    change column `risk_assessment_overall` `risk_assessment_overall` varchar(20) not null default '' ;

-- sfc_user_settings
update `sfc_user_settings` set `currentSessionID` = '' where `currentSessionID` is null;
alter table sfc_user_settings
    change column `currentSessionID` `currentSessionID` varchar(255) not null default '' ;

-- sff_adviews
update `sff_adviews` set `email` = '' where `email` is null;
update `sff_adviews` set `name` = '' where `name` is null;
update `sff_adviews` set `phone` = '' where `phone` is null;
update `sff_adviews` set `source` = '' where `source` is null;
alter table sff_adviews
    change column `email` `email` varchar(150) not null default '',
    change column `name` `name` varchar(255) not null default '',
    change column `phone` `phone` varchar(100) not null default '',
    change column `source` `source` varchar(50) not null default '' ;

-- sff_customers
update `sff_customers` set `company_name` = '' where `company_name` is null;
update `sff_customers` set `customer_code` = '' where `customer_code` is null;
update `sff_customers` set `email` = '' where `email` is null;
update `sff_customers` set `first_name` = '' where `first_name` is null;
update `sff_customers` set `last_name` = '' where `last_name` is null;
alter table sff_customers
    change column `company_name` `company_name` varchar(255) not null default '',
    change column `customer_code` `customer_code` varchar(15) not null default '',
    change column `email` `email` varchar(255) not null default '',
    change column `first_name` `first_name` varchar(255) not null default '',
    change column `last_name` `last_name` varchar(255) not null default '' ;

-- signup_abandonment
update `signup_abandonment` set `ip_address` = '' where `ip_address` is null;
alter table signup_abandonment
    change column `ip_address` `ip_address` varchar(40) not null default '' ;

-- site_pages
update `site_pages` set `header_content` = '' where `header_content` is null;
update `site_pages` set `page_description` = '' where `page_description` is null;
update `site_pages` set `page_keywords` = '' where `page_keywords` is null;
update `site_pages` set `page_name` = '' where `page_name` is null;
update `site_pages` set `page_title` = '' where `page_title` is null;
update `site_pages` set `section_title` = '' where `section_title` is null;
update `site_pages` set `section_title_right` = '' where `section_title_right` is null;
alter table site_pages
    change column `header_content` `header_content` varchar(500) not null default '',
    change column `page_description` `page_description` varchar(500) not null default '',
    change column `page_keywords` `page_keywords` varchar(500) not null default '',
    change column `page_name` `page_name` varchar(50) not null default '',
    change column `page_title` `page_title` varchar(255) not null default '',
    change column `section_title` `section_title` varchar(255) not null default '',
    change column `section_title_right` `section_title_right` varchar(255) not null default '' ;

-- site_updates
update `site_updates` set `site_update_post_id` = '' where `site_update_post_id` is null;
alter table site_updates
    change column `site_update_post_id` `site_update_post_id` varchar(500) not null default '' ;

-- sms_accounts
update `sms_accounts` set `sender_num` = '' where `sender_num` is null;
update `sms_accounts` set `site` = '' where `site` is null;
update `sms_accounts` set `vasid` = '' where `vasid` is null;
update `sms_accounts` set `vaspid` = '' where `vaspid` is null;
alter table sms_accounts
    change column `sender_num` `sender_num` varchar(20) not null default '',
    change column `site` `site` varchar(15) not null default '',
    change column `vasid` `vasid` varchar(20) not null default '',
    change column `vaspid` `vaspid` varchar(20) not null default '' ;

-- sms_log
update `sms_log` set `message` = '' where `message` is null;
update `sms_log` set `recipients` = '' where `recipients` is null;
update `sms_log` set `sender` = '' where `sender` is null;
update `sms_log` set `transid` = '' where `transid` is null;
alter table sms_log
    change column `message` `message` varchar(255) not null default '',
    change column `recipients` `recipients` varchar(255) not null default '',
    change column `sender` `sender` varchar(255) not null default '',
    change column `transid` `transid` varchar(255) not null default '' ;

-- sms_queue
update `sms_queue` set `message` = '' where `message` is null;
update `sms_queue` set `sent_from` = '' where `sent_from` is null;
update `sms_queue` set `sent_to` = '' where `sent_to` is null;
update `sms_queue` set `transaction_id` = '' where `transaction_id` is null;
alter table sms_queue
    change column `message` `message` varchar(255) not null default '',
    change column `sent_from` `sent_from` varchar(25) not null default '',
    change column `sent_to` `sent_to` varchar(25) not null default '',
    change column `transaction_id` `transaction_id` varchar(255) not null default '' ;

-- states
update `states` set `abbreviation` = '' where `abbreviation` is null;
alter table states
    change column `abbreviation` `abbreviation` varchar(11) not null default '' ;

-- subscriptions
update `subscriptions` set `arb_subscription_id` = '' where `arb_subscription_id` is null;
update `subscriptions` set `bank_name` = '' where `bank_name` is null;
update `subscriptions` set `bill_address` = '' where `bill_address` is null;
update `subscriptions` set `bill_attn_to` = '' where `bill_attn_to` is null;
update `subscriptions` set `bill_delivery` = '' where `bill_delivery` is null;
update `subscriptions` set `bill_description` = '' where `bill_description` is null;
update `subscriptions` set `bill_phone` = '' where `bill_phone` is null;
update `subscriptions` set `bill_to` = '' where `bill_to` is null;
update `subscriptions` set `canceled_message` = '' where `canceled_message` is null;
update `subscriptions` set `email` = '' where `email` is null;
update `subscriptions` set `failed_message` = '' where `failed_message` is null;
update `subscriptions` set `last_four_account_numbers` = '' where `last_four_account_numbers` is null;
update `subscriptions` set `payment_type` = '' where `payment_type` is null;
update `subscriptions` set `state` = '' where `state` is null;
alter table subscriptions
    change column `arb_subscription_id` `arb_subscription_id` varchar(50) not null default '',
    change column `bank_name` `bank_name` varchar(50) not null default '',
    change column `bill_address` `bill_address` varchar(300) not null default '',
    change column `bill_attn_to` `bill_attn_to` varchar(50) not null default '',
    change column `bill_delivery` `bill_delivery` varchar(25) not null default '',
    change column `bill_description` `bill_description` varchar(75) not null default '',
    change column `bill_phone` `bill_phone` varchar(25) not null default '',
    change column `bill_to` `bill_to` varchar(75) not null default '',
    change column `canceled_message` `canceled_message` varchar(1000) not null default '',
    change column `email` `email` varchar(100) not null default '',
    change column `failed_message` `failed_message` varchar(200) not null default '',
    change column `last_four_account_numbers` `last_four_account_numbers` varchar(4) not null default '',
    change column `payment_type` `payment_type` varchar(15) not null default '',
    change column `state` `state` varchar(2) not null default '' ;

-- truck_search
update `truck_search` set `days` = '' where `days` is null;
update `truck_search` set `distance` = '' where `distance` is null;
update `truck_search` set `equipment` = '' where `equipment` is null;
update `truck_search` set `origin_city` = '' where `origin_city` is null;
update `truck_search` set `origin_state` = '' where `origin_state` is null;
alter table truck_search
    change column `days` `days` varchar(11) not null default '',
    change column `distance` `distance` varchar(11) not null default '',
    change column `equipment` `equipment` varchar(500) not null default '',
    change column `origin_city` `origin_city` varchar(60) not null default '',
    change column `origin_state` `origin_state` varchar(5) not null default '' ;

-- truck_views
update `truck_views` set `destination` = '' where `destination` is null;
update `truck_views` set `equipment` = '' where `equipment` is null;
update `truck_views` set `origin_city` = '' where `origin_city` is null;
update `truck_views` set `origin_state` = '' where `origin_state` is null;
alter table truck_views
    change column `destination` `destination` varchar(500) not null default '',
    change column `equipment` `equipment` varchar(250) not null default '',
    change column `origin_city` `origin_city` varchar(60) not null default '',
    change column `origin_state` `origin_state` varchar(5) not null default '' ;

-- trucks
update `trucks` set `comments` = '' where `comments` is null;
update `trucks` set `contact_name` = '' where `contact_name` is null;
update `trucks` set `contact_number` = '' where `contact_number` is null;
update `trucks` set `destination_city` = '' where `destination_city` is null;
update `trucks` set `destination_country` = '' where `destination_country` is null;
update `trucks` set `destination_state` = '' where `destination_state` is null;
update `trucks` set `destination_zipcode` = '' where `destination_zipcode` is null;
update `trucks` set `nickname` = '' where `nickname` is null;
update `trucks` set `origin_city` = '' where `origin_city` is null;
update `trucks` set `origin_country` = '' where `origin_country` is null;
update `trucks` set `origin_zipcode` = '' where `origin_zipcode` is null;
update `trucks` set `outside_tracking_id` = '' where `outside_tracking_id` is null;
update `trucks` set `time_available` = '' where `time_available` is null;
alter table trucks
    change column `comments` `comments` varchar(1000) not null default '',
    change column `contact_name` `contact_name` varchar(50) not null default '',
    change column `contact_number` `contact_number` varchar(50) not null default '',
    change column `destination_city` `destination_city` varchar(40) not null default '',
    change column `destination_country` `destination_country` varchar(45) not null default '',
    change column `destination_state` `destination_state` varchar(15) not null default '',
    change column `destination_zipcode` `destination_zipcode` varchar(15) not null default '',
    change column `nickname` `nickname` varchar(255) not null default '',
    change column `origin_city` `origin_city` varchar(60) not null default '',
    change column `origin_country` `origin_country` varchar(15) not null default '',
    change column `origin_zipcode` `origin_zipcode` varchar(15) not null default '',
    change column `outside_tracking_id` `outside_tracking_id` varchar(100) not null default '',
    change column `time_available` `time_available` varchar(15) not null default '' ;

-- user_company
update `user_company` set `allowed_ips` = '' where `allowed_ips` is null;
update `user_company` set `company_logo_url` = '' where `company_logo_url` is null;
update `user_company` set `company_name_dba` = '' where `company_name_dba` is null;
update `user_company` set `interested_in` = '' where `interested_in` is null;
update `user_company` set `intra_interstate` = '' where `intra_interstate` is null;
update `user_company` set `mc_num` = '' where `mc_num` is null;
alter table user_company
    change column `allowed_ips` `allowed_ips` varchar(200) not null default '',
    change column `company_logo_url` `company_logo_url` varchar(500) not null default '',
    change column `company_name_dba` `company_name_dba` varchar(255) not null default '',
    change column `interested_in` `interested_in` varchar(100) not null default '',
    change column `intra_interstate` `intra_interstate` varchar(10) not null default '',
    change column `mc_num` `mc_num` varchar(20) not null default '' ;

-- user_devices
update `user_devices` set `notification_token` = '' where `notification_token` is null;
alter table user_devices
    change column `notification_token` `notification_token` varchar(500) not null default '' ;

-- user_emails
call CreateIndex('user_emails', 'ix_replyto_email', 'replyto_email ASC');
update `user_emails` set `replyto_email` = '' where `replyto_email` is null;
alter table user_emails
    change column `replyto_email` `replyto_email` varchar(100) not null default '' ;


-- user_geo_history
update `user_geo_history` set `event` = '' where `event` is null;
alter table user_geo_history
    change column `event` `event` varchar(250) not null default '' ;

-- user_geo_tracking
update `user_geo_tracking` set `event` = '' where `event` is null;
alter table user_geo_tracking
    change column `event` `event` varchar(250) not null default '' ;

-- user_info
update `user_info` set `address` = '' where `address` is null;
update `user_info` set `avatar_small` = '' where `avatar_small` is null;
update `user_info` set `bad_email_reason` = '' where `bad_email_reason` is null;
update `user_info` set `cc_avatar` = '' where `cc_avatar` is null;
update `user_info` set `chat_name` = '' where `chat_name` is null;
update `user_info` set `city` = '' where `city` is null;
update `user_info` set `company_notes` = '' where `company_notes` is null;
update `user_info` set `country` = '' where `country` is null;
update `user_info` set `current_address` = '' where `current_address` is null;
update `user_info` set `current_city` = '' where `current_city` is null;
update `user_info` set `current_country` = '' where `current_country` is null;
update `user_info` set `current_state` = '' where `current_state` is null;
update `user_info` set `current_zip` = '' where `current_zip` is null;
update `user_info` set `email` = '' where `email` is null;
update `user_info` set `facebook_email` = '' where `facebook_email` is null;
update `user_info` set `facebook_id` = '' where `facebook_id` is null;
update `user_info` set `fax` = '' where `fax` is null;
update `user_info` set `google_email` = '' where `google_email` is null;
update `user_info` set `google_id` = '' where `google_id` is null;
update `user_info` set `heard_about_us` = '' where `heard_about_us` is null;
update `user_info` set `mailing_address` = '' where `mailing_address` is null;
update `user_info` set `password_hash` = '' where `password_hash` is null;
update `user_info` set `phone_1` = '' where `phone_1` is null;
update `user_info` set `phone_2` = '' where `phone_2` is null;
update `user_info` set `state` = '' where `state` is null;
update `user_info` set `user_phone_1` = '' where `user_phone_1` is null;
update `user_info` set `user_phone_2` = '' where `user_phone_2` is null;
update `user_info` set `website` = '' where `website` is null;
update `user_info` set `zip` = '' where `zip` is null;
alter table user_info
    change column `address` `address` varchar(150) not null default '',
    change column `avatar_small` `avatar_small` varchar(200) not null default 'default.png',
    change column `bad_email_reason` `bad_email_reason` varchar(255) not null default '',
    change column `cc_avatar` `cc_avatar` varchar(255) not null default 'https://s3.amazonaws.com/cdn.bulkloads.com/user_files/profile/thumbs/default.png',
    change column `chat_name` `chat_name` varchar(255) not null default '',
    change column `city` `city` varchar(60) not null default '',
    change column `company_notes` `company_notes` varchar(1000) not null default '',
    change column `country` `country` varchar(5) not null default '',
    change column `current_address` `current_address` varchar(150) not null default '',
    change column `current_city` `current_city` varchar(60) not null default '',
    change column `current_country` `current_country` varchar(5) not null default '',
    change column `current_state` `current_state` varchar(2) not null default '',
    change column `current_zip` `current_zip` varchar(10) not null default '',
    change column `email` `email` varchar(100) not null default '',
    change column `facebook_email` `facebook_email` varchar(100) not null default '',
    change column `facebook_id` `facebook_id` varchar(128) not null default '',
    change column `fax` `fax` varchar(25) not null default '',
    change column `google_email` `google_email` varchar(100) not null default '',
    change column `google_id` `google_id` varchar(128) not null default '',
    change column `heard_about_us` `heard_about_us` varchar(500) not null default '',
    change column `mailing_address` `mailing_address` varchar(255) not null default '',
    change column `password_hash` `password_hash` varchar(80) not null default '',
    change column `phone_1` `phone_1` varchar(25) not null default '',
    change column `phone_2` `phone_2` varchar(25) not null default '',
    change column `state` `state` varchar(2) not null default '',
    change column `user_phone_1` `user_phone_1` varchar(25) not null default '',
    change column `user_phone_2` `user_phone_2` varchar(25) not null default '',
    change column `website` `website` varchar(100) not null default '',
    change column `zip` `zip` varchar(10) not null default '' ;

-- washouts
update `washouts` set `address` = '' where `address` is null;
update `washouts` set `after_hours_contact` = '' where `after_hours_contact` is null;
update `washouts` set `allow_livestock` = '' where `allow_livestock` is null;
update `washouts` set `allow_tanker` = '' where `allow_tanker` is null;
update `washouts` set `cost` = '' where `cost` is null;
update `washouts` set `day_contact` = '' where `day_contact` is null;
update `washouts` set `directions` = '' where `directions` is null;
update `washouts` set `hot_water` = '' where `hot_water` is null;
update `washouts` set `hours_of_operation` = '' where `hours_of_operation` is null;
update `washouts` set `missing_info` = '' where `missing_info` is null;
update `washouts` set `notes` = '' where `notes` is null;
update `washouts` set `origin_city` = '' where `origin_city` is null;
update `washouts` set `origin_state` = '' where `origin_state` is null;
update `washouts` set `phone_1` = '' where `phone_1` is null;
update `washouts` set `phone_2` = '' where `phone_2` is null;
update `washouts` set `public_notes` = '' where `public_notes` is null;
update `washouts` set `washout_name` = '' where `washout_name` is null;
update `washouts` set `website` = '' where `website` is null;
alter table washouts
    change column `address` `address` varchar(150) not null default '',
    change column `after_hours_contact` `after_hours_contact` varchar(255) not null default '',
    change column `allow_livestock` `allow_livestock` varchar(1) not null default '0',
    change column `allow_tanker` `allow_tanker` varchar(1) not null default '',
    change column `cost` `cost` varchar(20) not null default '',
    change column `day_contact` `day_contact` varchar(255) not null default '',
    change column `directions` `directions` varchar(500) not null default '',
    change column `hot_water` `hot_water` varchar(1) not null default '',
    change column `hours_of_operation` `hours_of_operation` varchar(255) not null default '',
    change column `missing_info` `missing_info` varchar(3) not null default '',
    change column `notes` `notes` varchar(255) not null default '',
    change column `origin_city` `origin_city` varchar(60) not null default '',
    change column `origin_state` `origin_state` varchar(2) not null default '',
    change column `phone_1` `phone_1` varchar(15) not null default '',
    change column `phone_2` `phone_2` varchar(15) not null default '',
    change column `public_notes` `public_notes` varchar(500) not null default '',
    change column `washout_name` `washout_name` varchar(50) not null default '',
    change column `website` `website` varchar(500) not null default '' ;



-- drop the indexes created for this script
ALTER TABLE `email_queue` DROP INDEX `ix_descr`, DROP INDEX `ix_replyto`, DROP INDEX `ix_sg_status`;
ALTER TABLE `load_views` DROP INDEX `ix_origin_state`, DROP INDEX `ix_origin_city`, DROP INDEX `ix_equipment`;
ALTER TABLE `load_views` DROP INDEX `ix_destination_state`, DROP INDEX `ix_destination_city` ;
alter table loads DROP INDEX ix_contact_name, DROP INDEX ix_contact_number, DROP INDEX ix_destination_zipcode, DROP INDEX ix_estimated_weight;
alter table loads DROP INDEX ix_external_rate_product_category_id, DROP INDEX ix_lo_comments, DROP INDEX ix_lo_commodity;
-- alter table loads DROP INDEX ix_lo_destination_notes, DROP INDEX ix_lo_origin_notes, DROP INDEX ix_load_comments;
alter table loads DROP INDEX ix_nickname, DROP INDEX ix_origin_zipcode, DROP INDEX ix_outside_tracking_id, DROP INDEX ix_radius;
alter table loads DROP INDEX ix_lo_contract_number, DROP INDEX ix_send_to_amount, DROP INDEX ix_text_type ;
alter table logins DROP INDEX ix_app_version, DROP INDEX ix_city, DROP INDEX ix_country, DROP INDEX ix_device, DROP INDEX ix_device_model;
alter table logins DROP INDEX ix_ip_address, DROP INDEX ix_os_version, DROP INDEX ix_state ;
alter table notifications DROP INDEX ix_data_metadata;
-- alter table notifications DROP INDEX ix_device_ids, DROP INDEX ix_notification_tokens;
alter table request_log DROP INDEX ix_response_status;
alter table user_emails DROP INDEX ix_replyto_email ;


set sql_safe_updates = 1;