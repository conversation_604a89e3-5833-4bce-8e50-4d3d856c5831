-- liquibase formatted sql

-- changeset theo:20250527-1100

alter table eld_user_info add column ab_user_id int null;

call CreateIndex('eld_user_info', 'idx_ab_user_id','ab_user_id');

set sql_safe_updates = 0;
delete from api_endpoints where path = '/eld/users/{int}/link/{int}';
set sql_safe_updates = 1;
-- changeset theo:20250527-1130

CALL createEndpointAndPermissions('ELD','PUT','/eld/users/{int}/link/user/{int}','Link ELD user to user','1,2');
CALL createEndpointAndPermissions('ELD','PUT','/eld/users/{int}/link/ab-user/{int}','Link ELD user to ab user','1,2');
