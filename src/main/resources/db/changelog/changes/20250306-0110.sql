-- liquibase formatted sql

-- changeset john:20250306-0110
set sql_safe_updates = 0;

DELETE FROM file_field_definitions;

INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Admixture (ADM)', 'Admixture_(ADM)', 'string', 'Admixture (ADM)', '', '0', '2025-01-15 17:40:28', '1');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Ang<PERSON><PERSON><PERSON>ths (MOTH)', 'Angoumois_Moths_(MOTH)', 'string', '<PERSON><PERSON><PERSON><PERSON> (MOTH)', '', '0', '2025-01-15 17:40:28', '3');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Animal Filth (ANFL)', 'Animal_Filth_(ANFL)', 'string', 'Animal Filth (ANFL)', '', '0', '2025-01-15 17:40:28', '2');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Badly Stained (BADS)', 'Badly_Stained_(BADS)', 'string', 'Badly Stained (BADS)', '', '0', '2025-01-15 17:40:28', '4');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Badly Weathered (BADW)', 'Badly_Weathered_(BADW)', 'string', 'Badly Weathered (BADW)', '', '0', '2025-01-15 17:40:28', '5');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Bird Excreta (BRDX)', 'Bird_Excreta_(BRDX)', 'string', 'Bird Excreta (BRDX)', '', '0', '2025-01-15 17:40:28', '6');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Bleached (BLCH)', 'Bleached_(BLCH)', 'string', 'Bleached (BLCH)', '', '0', '2025-01-15 17:40:28', '7');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Blight (BLIT)', 'Blight_(BLIT)', 'string', 'Blight (BLIT)', '', '0', '2025-01-15 17:40:28', '8');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Blue Aleurone (BLAL)', 'Blue_Aleurone_(BLAL)', 'string', 'Blue Aleurone (BLAL)', '', '0', '2025-01-15 17:40:28', '9');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Bottom Not Sampled (BNS)', 'Bottom_Not_Sampled_(BNS)', 'string', 'Bottom Not Sampled (BNS)', '', '0', '2025-01-15 17:40:28', '10');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Bright (BRIT)', 'Bright_(BRIT)', 'string', 'Bright (BRIT)', '', '0', '2025-01-15 17:40:28', '11');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Broken Corn (BC)', 'Broken_Corn_(BC)', 'string', 'Broken Corn (BC)', '', '0', '2025-01-15 17:40:28', '12');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Broken Corn & Foreign Material (BCFM)', 'Broken_Corn_&_Foreign_Material_(BCFM)', 'string', 'Broken Corn & Foreign Material (BCFM)', '', '0', '2025-01-15 17:40:28', '13');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Broken Glass (GLAS)', 'Broken_Glass_(GLAS)', 'string', 'Broken Glass (GLAS)', '', '0', '2025-01-15 17:40:28', '14');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Broken Kernel (BK)', 'Broken_Kernel_(BK)', 'string', 'Broken Kernel (BK)', '', '0', '2025-01-15 17:40:28', '15');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Broken Kernels & Foreign Material (BNFM)', 'Broken_Kernels_&_Foreign_Material_(BNFM)', 'string', 'Broken Kernels & Foreign Material (BNFM)', '', '0', '2025-01-15 17:40:28', '16');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Castor Beans (CSTB)', 'Castor_Beans_(CSTB)', 'string', 'Castor Beans (CSTB)', '', '0', '2025-01-15 17:40:28', '23');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Class (CL)', 'Class_(CL)', 'string', 'Class (CL)', '', '0', '2025-01-15 17:40:28', '17');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Cockleburs (CBUR)', 'Cockleburs_(CBUR)', 'string', 'Cockleburs (CBUR)', '', '0', '2025-01-15 17:40:28', '19');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Commercially Objectionable Foreign Odor (COFO)', 'Commercially_Objectionable_Foreign_Odor_(COFO)', 'string', 'Commercially Objectionable Foreign Odor (COFO)', '', '0', '2025-01-15 17:40:28', '20');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Commodity', 'Commodity', 'string', 'Commodity', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Conspicuous Admixture (CADM)', 'Conspicuous_Admixture_(CADM)', 'string', 'Conspicuous Admixture (CADM)', '', '0', '2025-01-15 17:40:28', '22');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Contrasting Classes (CCL)', 'Contrasting_Classes_(CCL)', 'string', 'Contrasting Classes (CCL)', '', '0', '2025-01-15 17:40:28', '21');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Course (CRSE)', 'Course_(CRSE)', 'string', 'Course (CRSE)', '', '0', '2025-01-15 17:40:28', '18');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Crotalaria (CROT)', 'Crotalaria_(CROT)', 'string', 'Crotalaria (CROT)', '', '0', '2025-01-15 17:40:28', '24');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Damaged Kernels (DK)', 'Damaged_Kernels_(DK)', 'string', 'Damaged Kernels (DK)', '', '0', '2025-01-15 17:40:28', '25');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Damaged Kernels (Total) (DKT)', 'Damaged_Kernels_(Total)_(DKT)', 'string', 'Damaged Kernels (Total) (DKT)', '', '0', '2025-01-15 17:40:28', '26');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Damaged Seeds (Total) (DST)', 'Damaged_Seeds_(Total)_(DST)', 'string', 'Damaged Seeds (Total) (DST)', '', '0', '2025-01-15 17:40:28', '27');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Dark, Hard, & Vitreous (DHV)', 'Dark,_Hard,_&_Vitreous_(DHV)', 'string', 'Dark, Hard, & Vitreous (DHV)', '', '0', '2025-03-06 17:26:37');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Defects (Total) (DEF)', 'Defects_(Total)_(DEF)', 'string', 'Defects (Total) (DEF)', '', '0', '2025-01-15 17:40:28', '29');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Dehulled (DH)', 'Dehulled_(DH)', 'string', 'Dehulled (DH)', '', '0', '2025-01-15 17:40:28', '30');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Dent (DENT)', 'Dent_(DENT)', 'string', 'Dent (DENT)', '', '0', '2025-01-15 17:40:28', '31');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Diatomaceous Earth (DIAT)', 'Diatomaceous_Earth_(DIAT)', 'string', 'Diatomaceous Earth (DIAT)', '', '0', '2025-01-15 17:40:28', '32');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Distinctly Discolored (DISC)', 'Distinctly_Discolored_(DISC)', 'string', 'Distinctly Discolored (DISC)', '', '0', '2025-01-15 17:40:28', '33');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Distinctly Green Kernels (DGK)', 'Distinctly_Green_Kernels_(DGK)', 'string', 'Distinctly Green Kernels (DGK)', '', '0', '2025-01-15 17:40:28', '34');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Distinctly Low Quality (DLQ)', 'Distinctly_Low_Quality_(DLQ)', 'string', 'Distinctly Low Quality (DLQ)', '', '0', '2025-01-15 17:40:28', '35');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Dockage (DKG)', 'Dockage_(DKG)', 'string', 'Dockage (DKG)', '', '0', '2025-01-15 17:40:28', '36');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Dyed (DYED)', 'Dyed_(DYED)', 'string', 'Dyed (DYED)', '', '0', '2025-01-15 17:40:28', '37');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Ergoty (ERG)', 'Ergoty_(ERG)', 'string', 'Ergoty (ERG)', '', '0', '2025-01-15 17:40:28', '38');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Erucic Acid (ERC)', 'Erucic_Acid_(ERC)', 'string', 'Erucic Acid (ERC)', '', '0', '2025-01-15 17:40:28', '39');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Extra Heavy (EHVY)', 'Extra_Heavy_(EHVY)', 'string', 'Extra Heavy (EHVY)', '', '0', '2025-01-15 17:40:28', '40');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Facility Location', 'Facility_Location', 'string', 'Facility Location', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Facility Name', 'Facility_Name', 'string', 'Facility Name', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Fine Foreign Material (FINE)', 'Fine_Foreign_Material_(FINE)', 'string', 'Fine Foreign Material (FINE)', '', '0', '2025-01-15 17:40:28', '41');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Flint (FLIN)', 'Flint_(FLIN)', 'string', 'Flint (FLIN)', '', '0', '2025-01-15 17:40:28', '42');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Flint & Dent (FLAD)', 'Flint_&_Dent_(FLAD)', 'string', 'Flint & Dent (FLAD)', '', '0', '2025-01-15 17:40:28', '44');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Foreign Material (FM)', 'Foreign_Material_(FM)', 'string', 'Foreign Material (FM)', '', '0', '2025-01-15 17:40:28', '45');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Foreign Material other than Rye (FMOR)', 'Foreign_Material_other_than_Rye_(FMOR)', 'string', 'Foreign Material Other Than Rye (FMOR)', '', '0', '2025-01-15 17:40:28', '46');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Foreign Material other than Wheat (FMOW)', 'Foreign_Material_other_than_Wheat_(FMOW)', 'string', 'Foreign Material Other Than Wheat (FMOW)', '', '0', '2025-01-15 17:40:28', '47');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Foreign Material other than Wheat or Rye (FMWR)', 'Foreign_Material_other_than_Wheat_or_Rye_(FMWR)', 'string', 'Foreign Material Other Than Wheat Or Rye (FMWR)', '', '0', '2025-01-15 17:40:28', '48');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Frost-Damaged Kernels (FDK)', 'Frost-Damaged_Kernels_(FDK)', 'string', 'Frost-Damaged Kernels (FDK)', '', '0', '2025-01-15 17:40:28', '43');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Garlic Bulblets (GARB)', 'Garlic_Bulblets_(GARB)', 'string', 'Garlic Bulblets (GARB)', '', '0', '2025-01-15 17:40:28', '49');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Garlicky (GAR)', 'Garlicky_(GAR)', 'string', 'Garlicky (GAR)', '', '0', '2025-01-15 17:40:28', '50');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Glucosinolates (GLUC)', 'Glucosinolates_(GLUC)', 'string', 'Glucosinolates (GLUC)', '', '0', '2025-01-15 17:40:28', '51');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('gross', 'gross', 'string', 'Gross', '', '0', '2023-01-06 13:17:39');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Handpicked Foreign Material (HPFM)', 'Handpicked_Foreign_Material_(HPFM)', 'string', 'Handpicked Foreign Material (HPFM)', '', '0', '2025-01-15 17:40:28', '52');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Hard & Vitreous Kernels of Amber Color (HVAC)', 'Hard_&_Vitreous_Kernels_of_Amber_Color_(HVAC)', 'string', 'Hard & Vitreous Kernels Of Amber Color (HVAC)', '', '0', '2025-01-15 17:40:28', '54');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Hard Kernels (HARD)', 'Hard_Kernels_(HARD)', 'string', 'Hard Kernels (HARD)', '', '0', '2025-01-15 17:40:28', '53');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Heat Damaged Kernels (HT)', 'Heat_Damaged_Kernels_(HT)', 'string', 'Heat Damaged Kernels (HT)', '', '0', '2025-01-15 17:40:28', '55');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Heating (HTG)', 'Heating_(HTG)', 'string', 'Heating (HTG)', '', '0', '2025-01-15 17:40:28', '56');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Heavy (HVY)', 'Heavy_(HVY)', 'string', 'Heavy (HVY)', '', '0', '2025-01-15 17:40:28', '57');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('In-Out Bound', 'In-Out_Bound', 'string', 'In-Out Bound', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Inconspicuous Admixture (IADM)', 'Inconspicuous_Admixture_(IADM)', 'string', 'Inconspicuous Admixture (IADM)', '', '0', '2025-01-15 17:40:28', '58');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Infested (INF)', 'Infested_(INF)', 'string', 'Infested (INF)', '', '0', '2025-01-15 17:40:28', '60');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Injured by Frost (IBF)', 'Injured_by_Frost_(IBF)', 'string', 'Injured By Frost (IBF)', '', '0', '2025-01-15 17:40:28', '61');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Injured by Heat (IBHT)', 'Injured_by_Heat_(IBHT)', 'string', 'Injured By Heat (IBHT)', '', '0', '2025-01-15 17:40:28', '62');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Injured by Mold (IBM)', 'Injured_by_Mold_(IBM)', 'string', 'Injured By Mold (IBM)', '', '0', '2025-01-15 17:40:28', '63');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Injured by Sprout (IBS)', 'Injured_by_Sprout_(IBS)', 'string', 'Injured By Sprout (IBS)', '', '0', '2025-01-15 17:40:28', '64');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Insect Damaged Kernels (IDK)', 'Insect_Damaged_Kernels_(IDK)', 'string', 'Insect Damaged Kernels (IDK)', '', '0', '2025-01-15 17:40:28', '59');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Large Animal Excreta (LGAN)', 'Large_Animal_Excreta_(LGAN)', 'string', 'Large Animal Excreta (LGAN)', '', '0', '2025-01-15 17:40:28', '65');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Large Stones (LGST)', 'Large_Stones_(LGST)', 'string', 'Large Stones (LGST)', '', '0', '2025-01-15 17:40:28', '66');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Light Garlicky (LGAR)', 'Light_Garlicky_(LGAR)', 'string', 'Light Garlicky (LGAR)', '', '0', '2025-01-15 17:40:28', '67');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Light Smutty (LSM)', 'Light_Smutty_(LSM)', 'string', 'Light Smutty (LSM)', '', '0', '2025-01-15 17:40:28', '68');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Limed (LIME)', 'Limed_(LIME)', 'string', 'Limed (LIME)', '', '0', '2025-01-15 17:40:28', '69');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Machine Separated Broken (MSFM)', 'Machine_Separated_Broken_(MSFM)', 'string', 'Machine Separated Broken (MSFM)', '', '0', '2025-01-15 17:40:28', '70');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Materially Weathered (MWTH)', 'Materially_Weathered_(MWTH)', 'string', 'Materially Weathered (MWTH)', '', '0', '2025-01-15 17:40:28', '71');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Mechanically Separated Dockage (MDKG)', 'Mechanically_Separated_Dockage_(MDKG)', 'string', 'Mechanically Separated Dockage (MDKG)', '', '0', '2025-01-15 17:40:28', '72');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Moisture (M)', 'Moisture_(M)', 'string', 'Moisture (M)', '', '0', '2025-01-15 17:40:28', '73');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Mold Damaged Kernels (MDK)', 'Mold_Damaged_Kernels_(MDK)', 'string', 'Mold Damaged Kernels (MDK)', '', '0', '2025-01-15 17:40:28', '74');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Musty (MUST)', 'Musty_(MUST)', 'string', 'Musty (MUST)', '', '0', '2025-01-15 17:40:28', '75');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Odor (ODOR)', 'Odor_(ODOR)', 'string', 'Odor (ODOR)', '', '0', '2025-01-15 17:40:28', '76');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Oil (OIL)', 'Oil_(OIL)', 'string', 'Oil (OIL)', '', '0', '2025-01-15 17:40:28', '77');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Plump (PL)', 'Plump_(PL)', 'string', 'Plump (PL)', '', '0', '2025-01-15 17:40:28', '78');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Protein (PROT)', 'Protein_(PROT)', 'string', 'Protein (PROT)', '', '0', '2025-01-15 17:40:28', '79');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Purple Mottled or Stained (PMS)', 'Purple_Mottled_or_Stained_(PMS)', 'string', 'Purple Mottled Or Stained (PMS)', '', '0', '2025-01-15 17:40:28', '80');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Rodent Excreta (RODX)', 'Rodent_Excreta_(RODX)', 'string', 'Rodent Excreta (RODX)', '', '0', '2025-01-15 17:40:28', '81');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Scoured (SCOR)', 'Scoured_(SCOR)', 'string', 'Scoured (SCOR)', '', '0', '2025-01-15 17:40:28', '82');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Shrunken & Broken Kernels (SHBN)', 'Shrunken_&_Broken_Kernels_(SHBN)', 'string', 'Shrunken & Broken Kernels (SHBN)', '', '0', '2025-01-15 17:40:28', '83');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Similar Seeds (SS)', 'Similar_Seeds_(SS)', 'string', 'Similar Seeds (SS)', '', '0', '2025-01-15 17:40:28', '84');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Skinned & Broken Kernels (SKBN)', 'Skinned_&_Broken_Kernels_(SKBN)', 'string', 'Skinned & Broken Kernels (SKBN)', '', '0', '2025-01-15 17:40:28', '86');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Slightly Weathered (SLW)', 'Slightly_Weathered_(SLW)', 'string', 'Slightly Weathered (SLW)', '', '0', '2025-01-15 17:40:28', '85');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Smut Balls (SBAL)', 'Smut_Balls_(SBAL)', 'string', 'Smut Balls (SBAL)', '', '0', '2025-01-15 17:40:28', '87');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Smutty (SMUT)', 'Smutty_(SMUT)', 'string', 'Smutty (SMUT)', '', '0', '2025-01-15 17:40:28', '88');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Sour (SOUR)', 'Sour_(SOUR)', 'string', 'Sour (SOUR)', '', '0', '2025-01-15 17:40:28', '89');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Splits (SPL)', 'Splits_(SPL)', 'string', 'Splits (SPL)', '', '0', '2025-01-15 17:40:28', '90');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Stained (STND)', 'Stained_(STND)', 'string', 'Stained (STND)', '', '0', '2025-01-15 17:40:28', '91');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Stinkbug Damaged (SKD)', 'Stinkbug_Damaged_(SKD)', 'string', 'Stinkbug Damaged (SKD)', '', '0', '2025-01-15 17:40:28', '92');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Stones (STON)', 'Stones_(STON)', 'string', 'Stones (STON)', '', '0', '2025-01-15 17:40:28', '93');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Stress Cracks (SC)', 'Stress_Cracks_(SC)', 'string', 'Stress Cracks (SC)', '', '0', '2025-01-15 17:40:28', '94');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Sulfured (SULF)', 'Sulfured_(SULF)', 'string', 'Sulfured (SULF)', '', '0', '2025-01-15 17:40:28', '95');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('tare', 'tare', 'string', 'Tare', '', '0', '2023-01-06 13:17:39');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Test Weight (TW)', 'Test_Weight_(TW)', 'string', 'Test Weight (TW)', '', '0', '2025-01-15 17:40:28', '96');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Thin (THIN)', 'Thin_(THIN)', 'string', 'Thin (THIN)', '', '0', '2025-01-15 17:40:28', '97');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Ticket Date', 'Ticket_Date', 'string', 'Ticket Date', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Ticket Number', 'Ticket_Number', 'string', 'Ticket Number', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Ticket Time', 'Ticket_Time', 'string', 'Ticket Time', '', '0', '2025-01-15 17:40:28');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Total Other Material (TOM)', 'Total_Other_Material_(TOM)', 'string', 'Total Other Material (TOM)', '', '0', '2025-01-15 17:40:28', '98');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Treated (TRET)', 'Treated_(TRET)', 'string', 'Treated (TRET)', '', '0', '2025-01-15 17:40:28', '99');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Triticale (TRIT)', 'Triticale_(TRIT)', 'string', 'Triticale (TRIT)', '', '0', '2025-01-15 17:40:28', '100');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Unknown Foreign Substance (FSUB)', 'Unknown_Foreign_Substance_(FSUB)', 'string', 'Unknown Foreign Substance (FSUB)', '', '0', '2025-01-15 17:40:28', '101');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Volume', 'Volume', 'string', 'Volume', '', '0', '2025-03-06 17:26:37');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`) VALUES ('Volume UOM', 'Volume_UOM', 'string', 'Volume UOM', '', '0', '2025-03-06 17:26:37');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Washed (WASH)', 'Washed_(WASH)', 'string', 'Washed (WASH)', '', '0', '2025-01-15 17:40:28', '102');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Waxy (WAXY)', 'Waxy_(WAXY)', 'string', 'Waxy (WAXY)', '', '0', '2025-01-15 17:40:28', '103');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Weevils (Live) (LW)', 'Weevils_(Live)_(LW)', 'string', 'Weevils (Live) (LW)', '', '0', '2025-01-15 17:40:28', '104');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('White Aleurone (WHAL)', 'White_Aleurone_(WHAL)', 'string', 'White Aleurone (WHAL)', '', '0', '2025-01-15 17:40:28', '105');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Wild Brome Grass Seed (WBG)', 'Wild_Brome_Grass_Seed_(WBG)', 'string', 'Wild Brome Grass Seed (WBG)', '', '0', '2025-01-15 17:40:28', '107');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Wild Buckwheat (WB)', 'Wild_Buckwheat_(WB)', 'string', 'Wild Buckwheat (WB)', '', '0', '2025-01-15 17:40:28', '106');
INSERT INTO `file_field_definitions` (`field_source_name`, `field_name`, `field_type`, `field_label`, `field_description`, `field_order`, `date`, `grade_id`) VALUES ('Wild Oats (WO)', 'Wild_Oats_(WO)', 'string', 'Wild Oats (WO)', '', '0', '2025-01-15 17:40:28', '108');

call CreateIndex('load_assignments', 'ix_load_assignment_loading_ticket_file_id', 'loading_ticket_file_id asc');
call CreateIndex('load_assignments', 'ix_load_assignment_unloading_ticket_file_id', 'unloading_ticket_file_id asc');

UPDATE file_types SET ocr_model_id = '2025_3_6' WHERE file_type_id in (1,2);
