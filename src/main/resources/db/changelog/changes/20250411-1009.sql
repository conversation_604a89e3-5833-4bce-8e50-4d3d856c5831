-- liquibase formatted sql

-- changeset john :20250411-1009
-- populate existing data with loading_ticket_file_id and unloading_ticket_file_id

set sql_safe_updates = 0;

UPDATE load_assignments la
    INNER JOIN load_assignment_files laf ON la.load_assignment_id = laf.load_assignment_id
    INNER JOIN files f ON laf.file_id = f.file_id
SET la.loading_ticket_file_id = f.file_id
WHERE f.file_type_id = 1
  AND la.loading_ticket_file_id IS NULL
  and laf.deleted = 0;

UPDATE load_assignments la
    INNER JOIN load_assignment_files laf ON la.load_assignment_id = laf.load_assignment_id
    INNER JOIN files f ON laf.file_id = f.file_id
SET la.unloading_ticket_file_id = f.file_id
WHERE f.file_type_id = 2
  AND la.unloading_ticket_file_id IS NULL
  and laf.deleted = 0;

set sql_safe_updates = 1;

-- optimize tables, takes a while
optimize table files;

optimize table load_assignments;

-- changeset john :20250417-2047

set sql_safe_updates = 0;

UPDATE `api_endpoints`
SET `path` = '/facilities/{int}/files'
WHERE description = 'Add a facility file';

set sql_safe_updates = 1;