-- liquibase formatted sql

-- changeset john:20250307-2108

INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('File', 'GET', '/files/ocr/{int}/fields', 'Get OCR fields for a file'),
    ('File', 'PUT', '/files/ocr/{int}/fields', 'Update the OCR fields'),
    ('File', 'GET', '/files/ocr/unapproved', 'Get unapproved OCR files'),
    ('Load Assignments (Shipper)', 'GET', '/loads/my_loads/{int}/assignments/{int}/tickets', 'Get the ticket values for an assignment')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
        method = 'GET' and path = '/files/ocr/{int}/fields'
     or method = 'PUT' and path = '/files/ocr/{int}/fields'
     or method = 'GET' and path = '/files/ocr/unapproved'
     or method = 'GET' and path = '/loads/my_loads/{int}/assignments/{int}/tickets'
  )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;

-- changeset andreas:20250307-2110

INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Agtrax Scale Tickets', 'GET', '/loads/my_loads/assignments/agtrax_unapproved_admin', 'AgTrax loads/files needing approval/ticket matching'),
    ('Agtrax Scale Tickets', 'GET', '/loads/my_loads/assignments/agtrax_unmatched_origin_tickets', 'AgTrax unmatched origin tickets'),
    ('Agtrax Scale Tickets', 'GET', '/loads/my_loads/assignments/agtrax_unmatched_origin_tickets_admin', 'AgTrax unmatched origin tickets')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
    method = 'GET' and path = '/loads/my_loads/assignments/agtrax_unapproved_admin'
        or method = 'GET' and path = '/loads/my_loads/assignments/agtrax_unmatched_origin_tickets'
        or method = 'GET' and path = '/loads/my_loads/assignments/agtrax_unmatched_origin_tickets_admin'
    )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;
