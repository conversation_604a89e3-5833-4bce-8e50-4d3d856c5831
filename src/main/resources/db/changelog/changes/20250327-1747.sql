-- liquibase formatted sql

-- changeset john:20250327-1747

ALTER TABLE `facility_wait_times`
    ADD COLUMN `approved` TINYINT(1) NOT NULL DEFAULT 0 AFTER `facility_id`;

ALTER TABLE `facility_reviews`
    ADD COLUMN `approved` TINYINT(1) NOT NULL DEFAULT 0 AFTER `facility_id`;

INSERT INTO `api_endpoints`
(`category`,`method`,`path`,`description`)
VALUES
    ('Facilities', 'GET', '/facilities/unapproved', 'Get unapproved facilities'),
    ('Facilities', 'GET', '/facilities/reviews/unapproved', 'Get unapproved facility reviews'),
    ('Facilities', 'PUT', '/facilities/{int}/reviews/{int}', 'Update/Approve a facility review'),
    ('Facilities', 'DELETE', '/facilities/{int}/reviews/{int}', 'Delete a facility review'),
    ('Facilities', 'GET', '/facilities/photos/unapproved', 'Get unapproved facility photos'),
    ('Facilities', 'PUT', '/facilities/{int}/photos/{int}', 'Update/Approve a facility photo')
;

INSERT INTO `api_key_endpoints`(
    `api_key_id`,
    `api_endpoint_id`
)
SELECT k.api_key_id, e.api_endpoint_id
FROM api_keys k
         join api_endpoints e
where k.api_key_id in (1,2)
  and (
        method = 'GET' and path = '/facilities/unapproved'
     or method = 'GET' and path = '/facilities/reviews/unapproved'
     or method = 'PUT' and path = '/facilities/{int}/reviews/{int}'
     or method = 'DELETE' and path = '/facilities/{int}/reviews/{int}'
     or method = 'GET' and path = '/facilities/photos/unapproved'
     or method = 'PUT' and path = '/facilities/{int}/photos/{int}'
  )
  and e.api_endpoint_id not in (
    select api_endpoint_id
    from api_key_endpoints
    where api_key_id in (1,2)
)
order by k.api_key_id, e.api_endpoint_id;

-- changeset john:20250328-1047

ALTER TABLE `facility_files`
    ADD COLUMN `approved` TINYINT(1) NOT NULL DEFAULT 0 AFTER `file_id`;

-- changeset john:20250331-1220

ALTER TABLE `facility_wait_times`
    DROP COLUMN `approved`;