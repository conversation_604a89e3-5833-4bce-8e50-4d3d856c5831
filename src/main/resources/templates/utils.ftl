<#function isoDatetime dateValue>
    <#return dateValue?datetime.iso>
</#function>

<#-- works with dates -->
<#function dateFormat dateValue pattern>
    <#if dateValue??>
        <#return dateValue?date.iso?string[pattern]>
    <#else>
        <#return "">
    </#if>
</#function>

<#-- works with instants and datetime -->
<#function datetimeFormat dateTimeValue pattern>
    <#if dateTimeValue??>
        <#return dateTimeValue?datetime.iso?string[pattern]>
    <#else>
        <#return "">
    </#if>
</#function>

<#-- Integers/ IDs -->

<#function intFormat amount>
    <#return amount?string["0"]>
</#function>

<#-- Amounts (dollarFormat) and Percentages -->

<#function amountFormat amount>
    <#return amount?string["#,##0.00"]>
</#function>

<#function dollarFormat amount>
    <#return "$" + amountFormat(amount)>
</#function>

<#function percentageFormat amount>
    <#return amountFormat(amount)>
</#function>

<#-- Weight, Volume, Miles, Hours and Rate -->

<#function numberFormat amount>
    <#if (amount != 0) && (abs(amount) < 1)>
        <#return amount?string["#,###.00###"]>
    <#else>
        <#return amount?string["#,###.#####"]>
    </#if>
</#function>

<#function weightFormat amount>
    <#return numberFormat(amount)>
</#function>

<#function volumeFormat amount>
    <#return numberFormat(amount)>
</#function>

<#function milesFormat amount>
    <#return numberFormat(amount)>
</#function>

<#function hoursFormat amount>
    <#return numberFormat(amount)>
</#function>

<#function rateFormat amount>
    <#return "$" + numberFormat(amount)>
</#function>
