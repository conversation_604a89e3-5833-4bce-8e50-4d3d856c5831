<#-- @ftlvariable name="prodMode" type="boolean" -->
<#-- @ftlvariable name="log" type="org.slf4j.Logger" -->

<#-- @ftlvariable name="loadStatusTitle" type="String" -->
<#-- @ftlvariable name="model" type="com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel" -->

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
    <title>Load Assignment Canceled</title>
    <#include "../common/style.ftl">
    <style type="text/css">
        body {
            max-width: 900px;
            margin: 0 auto;
        }
    </style>
</head>
<body>
<div style="max-width:900px; margin: 0 auto;">
    <#--HEADER-->
    <br>
    <hr>
    <br>
    <div style="text-align: left; font-weight: bold; font-size: 1.2em;">
        ${model.deletedMessage}
    </div>
    <br>
    <hr>
    <br>
    <#--HEADER END-->

    <#if !model.toDeleted || !model.toLoadId?? && model.sharedWithHiredCompanyResponse == 'Pending'>
        <#assign loadStatusTitle = "Load Canceled"/>
    <#elseif !model.toLoadId??>
        <#assign loadStatusTitle = "Load Rejection Confirmed"/>
    <#else>
        <#assign loadStatusTitle = "Load Cancellation Confirmed"/>
    </#if>

    <#include "common.ftl">

    <#if !model.toDeleted>
        <#include "footer.ftl">
    </#if>
</div>
</body>
</html>
