<#-- @ftlvariable name="prodMode" type="boolean" -->
<#-- @ftlvariable name="log" type="org.slf4j.Logger" -->

<#-- @ftlvariable name="loadStatusTitle" type="String" -->
<#-- @ftlvariable name="model" type="com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel" -->

<#-- @ftlvariable name="previewMode" type="boolean" -->

<#include "../utils.ftl">

<#--CONTENT-->
<table class="layout_table" width="95%" align="center" style="font-family:Arial, Helvetica, sans-serif;font-size:14px;border:0;margin-top:20px;"
       border="0">
  <tbody>
  <tr>
    <td colspan="2" style="text-align:center;font-size:20px;font-weight:bold;color:#555654;">
        ${loadStatusTitle}
      <br/>
        <#if model.rerouted?? && model.reroutePickupDrop == "pickup">
          <s>${model.pickupCompanyName}</s>
            ${model.rerouteCompanyName}
        <#else>
            ${model.pickupCompanyName}
        </#if>
      to
        <#if model.isRerouted && model.reroutePickupDrop == "drop">
          <s>${model.dropCompanyName}</s>
            ${model.rerouteCompanyName}
        <#else>
            ${model.dropCompanyName}
        </#if>
      <div style="height:30px;"></div>
    </td>
  </tr>

  <tr>
    <td style="text-align:left;vertical-align:top;">
      <!-- owner information -->
      <table style="width:95%;">
        <tr>
          <td colspan="2" style="text-align:left;border:0;margin-top:0;padding-top:0;padding-bottom:15px;">
              <#if model.companyLogoUrl?? && model.companyLogoUrl?has_content>
                  <#if model.companyLogoUrl?starts_with(model.domainUrl)>
                    <img alt="" src="${model.companyLogoUrl?substring(model.domainUrl?length)}" border="0" style="max-height:50px;"/>
                  </#if>
              <#else>
                <h2 style="margin-top:0; margin-bottom:0;">${model.companyName}</h2>
              </#if>
          </td>
        </tr>

        <tr>
          <td class="header">Company</td>
          <td>${model.companyName}</td>
        </tr>
        <tr>
          <td class="header">Dispatcher</td>
          <td>${model.firstName} ${model.lastName}</td>
        </tr>

          <#if model.phone1?has_content>
            <tr>
              <td class="header">Phone</td>
              <td>${model.phone1}</td>
            </tr>
          </#if>
          <#if model.email?has_content>
            <tr>
              <td class="header">Dispatcher Email</td>
              <td>${model.email}</td>
            </tr>
          </#if>
          <#if model.accountingEmail?has_content>
            <tr>
              <td class="header"><b>BILLING Email</b></td>
              <td><b>${model.accountingEmail}</b></td>
            </tr>
          </#if>
      </table>
    </td>
    <td style="text-align:right;">
      <!-- Load info	 -->
      <table style="width:100%;">
        <tr>
          <td class="header" style="vertical-align:top;"><#if model.isDriver>Assigned Driver<#else>Hired Company</#if></td>
          <td style="vertical-align:top;">
              <#if !model.isIntraCompany>
                <b>${model.toCompanyName}</b>
              </#if>
              <#if model.toFirstName?has_content || model.toLastName?has_content>
                <br/>
                  ${model.toFirstName} ${model.toLastName}
              </#if>
              <#if model.toPhone1?has_content>
                <br/>
                  ${model.toPhone1}
              </#if>
              <#if model.toEmail?has_content>
                <br/>
                  ${model.toEmail}
              </#if>
          </td>
        </tr>

        <tr>
          <td class="header">Load #</td>
          <td>
              <#list model.getAssignments() as assignment>
                  ${assignment.loadAssignmentNumber}&nbsp;
              </#list>
          </td>
        </tr>

          <#if model.truckUserCompanyEquipment?has_content || model.trailerUserCompanyEquipment?has_content>
            <tr>
              <td class="header">Assigned Equipment</td>
              <td>
                  <#if model.truckUserCompanyEquipment?has_content>
                      ${model.truckUserCompanyEquipment}
                    <br/>
                  </#if>
                  <#if model.trailerUserCompanyEquipment?has_content>
                      ${model.trailerUserCompanyEquipment}
                  </#if>
              </td>
            </tr>
          </#if>

          <#if model.shipFrom?has_content || model.shipTo?has_content>
            <tr>
              <td class="header">Ship Dates</td>
              <td>${dateFormat(model.shipFrom, "MMM-dd-YYYY")}
                to ${dateFormat(model.shipTo, "MMM-dd-YYYY")}</td>
            </tr>
          </#if>
          <#if !model.isIntraCompany>
            <tr>
              <td class="header">Confirmation Date</td>
              <td>${.now?string["M/dd/YYYY"]}</td>
            </tr>
          </#if>
        <tr>
          <td class="header">Number of Loads</td>
          <td>${model.assignments?size}</td>
        </tr>
      </table>
    </td>
  </tr>

  <tr> <!-- load info table -->
    <td colspan="2" style="padding-top:20px;">

      <!-- assignment information -->
      <table width="100%" cellpadding="2" cellspacing="0">
        <tr>
          <td class="rowheader">Load #</td>
          <td class="rowheader">Origin #</td>
          <td class="rowheader">Destination #</td>
          <td class="rowheader">Work Order #</td>
          <td class="rowheader">Agreed Rate</td>
          <td class="rowheader">Commodity</td>
          <td class="rowheader">Est Miles</td>
          <td class="rowheader">Extras</td>
          <td class="rowheader">Notes</td>
            <#if !(previewMode?? && previewMode)>
                <#if model.links?has_content>
                  <td class="rowheader"></td>
                </#if>
            </#if>
        </tr>
          <#list model.assignments as assignment>
            <tr>
              <td class="aRow" style="height:45px;">${assignment.loadAssignmentNumber}</td>
              <td class="aRow" style="height:45px;">${assignment.pickupNumber}</td>
                <#if assignment.isRerouted>
                  <td class="aRow" style="height:45px;"><s>${assignment.previousDropNumber}</s><br/>${assignment.dropNumber}</td>
                <#else>
                  <td class="aRow" style="height:45px;">${assignment.dropNumber}</td>
                </#if>
                <#if assignment.isRerouted>
                  <td class="aRow" style="height:45px;"><s>${assignment.previousWorkOrderNumber}</s><br/>${assignment.workOrderNumber}</td>
                <#else>
                  <td class="aRow" style="height:45px;">${assignment.workOrderNumber}</td>
                </#if>
                <#if assignment.isRerouted && assignment.previousRate?has_content && (assignment.previousRate != assignment.rate || assignment.previousRateType != assignment.rateType)>
                  <td class="aRow"><s>$${assignment.previousRate?string["0.######"]}${assignment.previousRateTypeTextMedium}
                          <#if assignment.originalRateVisible && assignment.originalRate?has_content>
                            (${assignment.originalRatePercentage}% of $${assignment.originalRate}${assignment.originalRateTypeTextMedium})
                          </#if>
                    </s><br/>$${assignment.rate?string["0.######"]}${assignment.rateTypeTextMedium}
                      <#if assignment.originalRateVisible && assignment.originalRate?has_content>
                        (${assignment.originalRatePercentage}% of $${assignment.originalRate}${assignment.originalRateTypeTextMedium})
                      </#if>
                  </td>
                <#elseif assignment.rate?has_content && assignment.rateType?has_content>
                  <td class="aRow">$${assignment.rate?string["0.######"]}${assignment.rateTypeTextMedium}
                      <#if assignment.originalRateVisible && assignment.originalRate?has_content>
                        (${assignment.originalRatePercentage}% of $${assignment.originalRate}${assignment.originalRateTypeTextMedium})
                      </#if>
                  </td>
                <#else>
                  <td class="aRow"></td>
                </#if>
              <td class="aRow">${assignment.loCommodity}</td>
              <td class="aRow">${assignment.estimatedMiles?string(",##0")}</td>
              <td class="aRow">${assignment.estSurcharges?string.currency}</td>

                <#assign reqs = []>
                <#if assignment.isHazmat>
                    <#assign reqs = reqs + ['hazmat']>
                </#if>
                <#if assignment.washoutRequired>
                    <#assign reqs = reqs + ['washout required']>
                </#if>
                <#if assignment.pickupApptRequired>
                    <#assign reqs = reqs + ['pick appt required']>
                </#if>
                <#if assignment.dropApptRequired>
                    <#assign reqs = reqs + ['drop appt required']>
                </#if>
                <#if assignment.originalsRequired>
                    <#assign reqs = reqs + ['originals required']>
                </#if>
                <#if assignment.scheduledPickupDate?has_content>
                    <#assign pickupDate = datetimeFormat(assignment.scheduledPickupDate, "MMM-dd")>
                    <#assign pickupTime = datetimeFormat(assignment.scheduledPickupDate, "HH:mm")>
                    <#if pickupTime != "00:00">
                        <#assign pickupDisplay = pickupDate + " " + pickupTime>
                    <#else>
                        <#assign pickupDisplay = pickupDate>
                    </#if>
                    <#assign reqs = reqs + ['Pickup: ' + pickupDisplay]>
                </#if>
                <#if assignment.scheduledDropDate?has_content>
                    <#assign dropDate = datetimeFormat(assignment.scheduledDropDate, "MMM-dd")>
                    <#assign dropTime = datetimeFormat(assignment.scheduledDropDate, "HH:mm")>
                    <#if dropTime != "00:00">
                        <#assign dropDisplay = dropDate + " " + dropTime>
                    <#else>
                        <#assign dropDisplay = dropDate>
                    </#if>
                    <#assign reqs = reqs + ['Drop: ' + dropDisplay]>
                </#if>

              <td class="aRow">${reqs?join("<br/>")}</td>


                <#if !(previewMode?? && previewMode)>
                  <td class="aRow">
                      <#if model.links?has_content && model.links?size gt assignment?index>
                          <#if !assignment.toDeleted>
                            <a href="${model.links[assignment?index].dynamicLink}">view</a>
                          </#if>
                      </#if>
                  </td>
                </#if>

            </tr>
          </#list>

      </table>
      <br/><br/>
      <!-- pickup/drop info -->
      <table width="100%" cellpadding="2" cellspacing="0">
          <#if model.isRerouted && model.reroutePickupDrop == "pickup">

        <caption style="font-size:1.1em;text-align:left;padding:5px;"><b>Origin:
            <s>${model.pickupCompanyName}</s>
                ${model.rerouteCompanyName}
          </b>
            <#if model.rerouteLocation?has_content>
              - <span style="font-size:.9em;">${model.rerouteLocation}</span>
            </#if>
        </caption>
        <tr>
          <td style="vertical-align:top;width:50%;">
              ${model.rerouteAddress}
            <br/>
              <#if model.rerouteDirections?has_content>
                Directions: ${model.rerouteDirections}
              </#if>
          </td>
          <td>
            Origin Notes:
            <b>
                ${model.rerouteReason}
                <#if model.rerouteCompanyNotes?has_content>
                    <#if model.rerouteReason?has_content>
                      <br/>
                    </#if>
                    ${model.rerouteCompanyNotes}
                </#if>
            </b><br/>
            Phone: ${model.rerouteCompanyPhone}<br/>
            Hours: ${model.rerouteReceivingHours}<br/>
            Appointment: <#if model.rerouteApptRequired>Yes<#else>No</#if>
          </td>
        </tr>
        <br/><br/>
          <#else>
        <caption style="font-size:1.1em;text-align:left;padding:5px;"><b>Origin:
                ${model.pickupCompanyName}
          </b>
            <#if model.pickupLocation?has_content>
              - <span style="font-size:.9em;">${model.pickupLocation}</span>
            </#if>
        </caption>
        <tr>
          <td style="vertical-align:top;width:50%;">
              ${model.pickupAddress}
            <br/>
              <#if model.pickupDirections?has_content>
                Directions: ${model.pickupDirections}
              </#if>
          </td>
          <td>
            Origin Notes:
            <b>
                <#if model.pickupNotes?has_content>
                    ${model.pickupNotes}
                </#if>
                <#if model.pickupCompanyNotes?has_content && (!model.pickupNotes?? || model.pickupCompanyNotes != model.pickupNotes)>
                    <#if model.pickupNotes?has_content>
                      <br/>
                    </#if>
                    ${model.pickupCompanyNotes}
                </#if>
            </b><br/>
            Phone: ${model.pickupCompanyPhone}<br/>
            Hours: ${model.pickupReceivingHours}<br/>
            Appointment: <#if model.pickupApptRequired>Yes<#else>No</#if>
          </td>
            </#if>

        </tr>
      </table>
      <br/><br/>


      <table width="100%" cellpadding="2" cellspacing="0">
        <caption style="font-size:1.1em;text-align:left;padding:5px;"><b>Destination:
                <#if model.isRerouted && model.reroutePickupDrop == 'drop'>
            <s>${model.dropCompanyName}</s>
                ${model.rerouteCompanyName}
          </b>
            <#if model.rerouteLocation?has_content>
              - <span style="font-size:.9em;">${model.rerouteLocation}</span>
            </#if>
            <#else>
                ${model.dropCompanyName}
              <br/>
                <#if model.dropLocation != "">
                  - <span style="font-size:.9em;">${model.dropLocation}</span>
                </#if>
            </#if>
        </caption>
        <tr>
            <#if model.isRerouted && model.reroutePickupDrop == 'drop'>
              <td style="vertical-align:top;width:50%;">
                  ${model.rerouteAddress}
                <br/>
                  <#if model.rerouteDirections?has_content>
                    Directions: ${model.rerouteDirections}
                  </#if>
              </td>
              <td>
                Destination Notes:
                <b>
                    ${model.rerouteReason}
                    <#if model.rerouteCompanyNotes?has_content && model.rerouteCompanyNotes?trim?has_content>
                        <#if model.rerouteReason?has_content && model.rerouteReason?trim?has_content>
                          <br/>
                        </#if>
                        ${model.rerouteCompanyNotes}
                    </#if>
                </b><br/>
                Phone: ${model.rerouteCompanyPhone}<br/>
                Hours: ${model.rerouteReceivingHours}<br/>
                Appointment: <#if model.rerouteApptRequired>Yes<#else>No</#if>
              </td>

            <#else>

              <td style="vertical-align:top;width:50%;">
                <!-- (*** is this physical address?) -->
                  ${model.dropAddress}
                <br/>
                  <#if model.dropDirections != "">
                    Directions: ${model.dropDirections}
                  </#if>
              </td>
              <td>
                Destination Notes:
                <b>
                    <#if model.dropNotes?has_content>
                        ${model.dropNotes}
                    </#if>
                    <#if model.dropCompanyNotes?has_content && (!model.dropNotes?? || model.dropCompanyNotes != model.dropNotes)>
                        <#if model.dropNotes?has_content>
                          <br/>
                        </#if>
                        ${model.dropCompanyNotes}
                    </#if>
                </b><br/>
                Phone: ${model.dropCompanyPhone}<br/>
                Hours: ${model.dropReceivingHours}<br/>
                Appointment: <#if model.dropApptRequired>Yes<#else>No</#if>
              </td>
            </#if>
        </tr>
      </table>


    </td>
  </tr>
  <#if model.equipmentNames?has_content>
    <!-- show the trailer types -->
    <tr>
      <td colspan="2" style="padding-top:20px;">
        Trailer Type(s):
          ${model.equipmentNames}
      </td>
    </tr>
  </#if>

  <#if model.loadConfirmationFooter?has_content>
    <tr>
      <td colspan="2" style="padding-top:20px;">
          ${model.loadConfirmationFooter}
      </td>
    </tr>
  </#if>


  </tbody>
</table>
<#--CONTENT END-->
