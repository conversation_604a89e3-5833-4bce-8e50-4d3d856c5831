<#-- @ftlvariable name="prodMode" type="boolean" -->
<#-- @ftlvariable name="log" type="org.slf4j.Logger" -->

<#-- @ftlvariable name="loadStatusTitle" type="String" -->
<#-- @ftlvariable name="model" type="com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel" -->

<#include "../utils.ftl">

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html lang="en">
<head>
    <title>Load Rerouted</title>
    <#include "../common/style.ftl">
</head>
<body>
<div style="max-width:900px; margin: 0 auto;">
    <#--HEADER-->
    <#if (model.editDate?has_content) && (model.editDate?has_content) && (model.rerouteDate?has_content)
    && (isoDatetime(model.editDate) > isoDatetime(model.assignedDate))
    && (isoDatetime(model.editDate) + "-1" > isoDatetime(model.rerouteDate))>
        <div style="text-align: center; font-weight: bold; font-size: 1.4em; color: red">
            REVISED
        </div>
    </#if>

    <#if model.rerouteDate?has_content && model.assignedDate?has_content && model.editDate?has_content
    && (isoDatetime(model.rerouteDate) >= isoDatetime(model.assignedDate))
    && (isoDatetime(model.rerouteDate) >= isoDatetime(model.editDate))>
        <#assign message = model.rerouteReason>
    <#elseif model.personalMessage?has_content>
        <#assign message = model.personalMessage>
    </#if>

    <#if (message?has_content)>
        <br>
        <hr>
        <br>
        <div style="text-align: left; font-weight: bold; font-size: 1.2em;">
            ${message}
        </div>
        <br>
        <hr>
        <br>
    </#if>
    <#--HEADER END-->

    <#assign loadStatusTitle = "Load Rerouted">
    <#include "common.ftl">

    <#include "footer.ftl">
</div>
</body>
</html>
