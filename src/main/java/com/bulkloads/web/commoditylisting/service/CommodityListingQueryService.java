package com.bulkloads.web.commoditylisting.service;

import java.util.List;
import com.bulkloads.web.commoditylisting.repository.CommodityListingQueryRepository;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingListResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingsTotalResponse;
import com.bulkloads.web.user.repository.UserRepository;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class CommodityListingQueryService {

  private final CommodityListingQueryRepository commodityListingQueryRepository;
  private final UserRepository userRepository;

  public List<CommodityListingListResponse> getCommodityListings(String term, String order,
      Integer skip, Integer limit) {
    return commodityListingQueryRepository.getCommodityListings(term, order, skip, limit);
  }

  public CommodityListingsTotalResponse getCommodityListingsTotal(String term) {
    return commodityListingQueryRepository.getCommodityListingsTotal(term);
  }

  public CommodityListingResponse getCommodityListing(Integer commodityListingId) {
    return commodityListingQueryRepository.getCommodityListing(commodityListingId);
  }

}
