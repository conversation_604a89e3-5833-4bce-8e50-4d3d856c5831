package com.bulkloads.web.commoditylisting.repository;

import java.util.Optional;
import com.bulkloads.web.commoditylisting.domain.entity.CommodityListing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CommodityListingRepository extends JpaRepository<CommodityListing, Integer> {

  Optional<CommodityListing> findByCommodityListingIdAndUserCompanyUserCompanyId(int commodityListingId, int userCompanyId);
}
