package com.bulkloads.web.commoditylisting.repository;

import static com.bulkloads.web.commoditylisting.repository.template.GetCommodityListingsQueryTemplate.GET_COMMODITY_LISTINGS_QUERY_TEMPLATE;
import static com.bulkloads.web.commoditylisting.repository.template.GetCommodityListingsQueryTemplate.GET_COMMODITY_LISTINGS_TOTAL_QUERY_TEMPLATE;
import static com.bulkloads.web.commoditylisting.repository.template.GetCommodityListingsQueryTemplate.GET_COMMODITY_LISTING_DETAIL_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingListResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingResponse;
import com.bulkloads.web.commoditylisting.service.dto.CommodityListingsTotalResponse;
import com.bulkloads.web.commoditylisting.service.dto.transformer.CommodityListingListResponseTransformer;
import com.bulkloads.web.commoditylisting.service.dto.transformer.CommodityListingResponseTransformer;
import com.bulkloads.web.commoditylisting.service.dto.transformer.CommodityListingsTotalResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Repository
@RequiredArgsConstructor
public class CommodityListingQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;

  private final CommodityListingListResponseTransformer commodityListingListResponseTransformer;
  private final CommodityListingsTotalResponseTransformer commodityListingsTotalResponseTransformer;
  private final CommodityListingResponseTransformer commodityListingResponseTransformer;

  public List<CommodityListingListResponse> getCommodityListings(String term, String order,
      Integer skip, Integer limit) {
    Map<String, Object> params = new HashMap<>();
    params.put("term", term);
    params.put("order", order);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(
        GET_COMMODITY_LISTINGS_QUERY_TEMPLATE,
        params,
        commodityListingListResponseTransformer
    );
  }

  public CommodityListingsTotalResponse getCommodityListingsTotal(String term) {
    Map<String, Object> params = new HashMap<>();
    params.put("term", term);

    return jpaNativeQueryService.queryForObject(
        GET_COMMODITY_LISTINGS_TOTAL_QUERY_TEMPLATE,
        params,
        commodityListingsTotalResponseTransformer
    );
  }

  public CommodityListingResponse getCommodityListing(Integer commodityListingId) {
    Map<String, Object> params = new HashMap<>();
    params.put("commodityListingId", commodityListingId);

    return jpaNativeQueryService.queryForObject(
        GET_COMMODITY_LISTING_DETAIL_QUERY_TEMPLATE,
        params,
        commodityListingResponseTransformer
    );
  }

}
