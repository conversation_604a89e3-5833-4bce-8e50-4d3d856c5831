package com.bulkloads.web.usercompany.repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import com.bulkloads.web.usercompany.domain.entity.UserType;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.ListCrudRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface UserTypeRepository extends ListCrudRepository<UserType, Integer> {

  Optional<UserType> findByUserType(String userType);

  @Query("""
          select ut from UserType ut
          where
              ut.userTypeId <> 100
              and ut.userTypeId in :userTypeIds
          order by
              ut.userTypeId asc
      """)
  List<UserType> searchByUserTypeIdsOrderByUserTypeIdAsc(Collection<Integer> userTypeIds);

  @Query("""
          select ut from UserType ut
          where
              ut.userTypeId <> 100
              and ut.userType in :userTypes
          order by
              ut.userTypeId asc
      """)
  List<UserType> searchByUserTypesOrderByUserTypeIdAsc(Collection<String> userTypes);

}
