package com.bulkloads.web.usercompany.domain.entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "bl_user_company_settings")
@Getter
@Setter
public class UserCompanySettings {

  @Id
  @OneToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id", referencedColumnName = "user_company_id")
  private UserCompany userCompany;

  @Column(name = "canceled_date")
  private LocalDateTime canceledDate;

  @Column(name = "membership_end_date")
  private LocalDateTime membershipEndDate;

  @Column(name = "trial_start_date")
  private LocalDateTime trialStartDate;

  @Column(name = "trial_end_date")
  private LocalDateTime trialEndDate;

  @Column(name = "mobile_trial_end_date")
  private LocalDateTime mobileTrialEndDate;

  @Column(name = "deletion_date")
  private LocalDateTime deletionDate;

  @Column(name = "monthly_rate")
  private BigDecimal monthlyRate = new BigDecimal("49.95");

  @Column(name = "threemonth_rate")
  private BigDecimal threemonthRate = new BigDecimal("149.95");

  @Column(name = "sixmonth_rate")
  private BigDecimal sixmonthRate = new BigDecimal("249.95");

  @Column(name = "yearly_rate")
  private BigDecimal yearlyRate = new BigDecimal("499.95");

  @Column(name = "biyearly_rate")
  private BigDecimal biyearlyRate = new BigDecimal("899.95");

  @Column(name = "is_custom_rate")
  private Boolean isCustomRate = false;

  @Column(name = "comment")
  private String comment = "";

  @NotNull
  @Column(name = "private_load_posts")
  private Integer privateLoadPosts = 0;

  @NotNull
  @Column(name = "private_load_count")
  private Integer privateLoadCount = 0;

  @NotNull
  @Column(name = "carrier_load_posts")
  private Integer carrierLoadPosts = 0;

  @NotNull
  @Column(name = "carrier_load_count")
  private Integer carrierLoadCount = 0;

  @NotNull
  @Column(name = "public_load_posts")
  private Integer publicLoadPosts = 0;

  @NotNull
  @Column(name = "public_load_count")
  private Integer publicLoadCount = 0;

  @Column(name = "eld_provider_id")
  private String eldProviderId = "";

  @Column(name = "accounting_provider_id")
  private String accountingProviderId = "";

  public boolean isPro() {
    return (membershipEndDate != null && membershipEndDate.isAfter(LocalDateTime.now()))
        || (trialEndDate != null && trialEndDate.isAfter(LocalDateTime.now()))
        || (mobileTrialEndDate != null && mobileTrialEndDate.isAfter(LocalDateTime.now()));
  }
}
