package com.bulkloads.web.report.repository.template;

public class GetProductQueryTemplate {

  public static final String GET_PRODUCT_QUERY_TEMPLATE = """
      select l.rate_product_category, COUNT(*) AS number_of_loads
      from load_assignments la
      inner join loads l on l.load_id = la.load_id
      where
        <% params.put("userCompanyId", userCompanyId) %>
        la.user_company_id = :userCompanyId
      and la.deleted = 0
      group by l.rate_product_category
      """;

}
