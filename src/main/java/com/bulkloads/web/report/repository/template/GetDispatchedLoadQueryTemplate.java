package com.bulkloads.web.report.repository.template;

public class GetDispatchedLoadQueryTemplate {

  public static final String GET_DISPATCHED_LOAD_QUERY_TEMPLATE = """
         select u.user_id, u.first_name, u.last_name, count(*) as load_count
         from load_assignments la
         inner join user_info u using(user_id)
         where
           <% params.put("userCompanyId", userCompanyId) %>
           la.user_company_id = :userCompanyId
           and la.deleted = 0
           and la.assignment_status in ('Assigned','Dispatched','Loading','En Route','Unloading')
         group by u.user_id, u.first_name, u.last_name
      """;

}
