
package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.CarriersUsedReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class CarriersUsedReportResponseTransformer implements TupleTransformer<CarriersUsedReportResponse> {

  @Override
  public CarriersUsedReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return CarriersUsedReportResponse.builder()
        .abCompanyId(parts.asInteger("ab_company_id"))
        .companyName(parts.asString("company_name"))
        .numberOfLoads(parts.asInteger("number_of_loads"))
        .build();
  }
}
