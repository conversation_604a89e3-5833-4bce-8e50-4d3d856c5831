
package com.bulkloads.web.report.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.report.service.dto.ProductReportResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ProductReportResponseTransformer implements TupleTransformer<ProductReportResponse> {

  @Override
  public ProductReportResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return ProductReportResponse.builder()
        .rateProductCategory(parts.asString("rate_product_category"))
        .numberOfLoads(parts.asInteger("number_of_loads"))
        .build();
  }
}
