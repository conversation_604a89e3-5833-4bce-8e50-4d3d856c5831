package com.bulkloads.web.forum.repository;

import java.util.Optional;
import com.bulkloads.web.forum.domain.entity.ForumSubscription;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ForumSubscriptionRepository extends JpaRepository<ForumSubscription, Integer> {

  Optional<ForumSubscription> findByForumSubscriptionId_ForumIdAndForumSubscriptionId_UserId(final int forumId, final int userId);

}
