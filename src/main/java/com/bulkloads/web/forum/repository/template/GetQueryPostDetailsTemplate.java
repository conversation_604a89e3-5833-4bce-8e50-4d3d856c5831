package com.bulkloads.web.forum.repository.template;

public class GetQueryPostDetailsTemplate {

  @SuppressWarnings("checkstyle:FileTabCharacter")
  public static final String GET_QUERY_POST_DETAILS_TEMPLATE = """
      SELECT
      			p.forum_id,
      			forum_title,
      			p.forum_content,
      			p.user_id,
      			p.views,
      			p.date_added,
      			p.last_reply,
      			ifnull(p.last_reply,ifnull(p.show_date, p.date_added)) as last_post,
      			p.alias,
      			p.post_type_id,
      			pts.post_type,
      			pts.maindir,
      			concat('<% print(domain_url) %>', pts.maindir, '/', p.alias, '/') as forum_url,
      			pts.email_category_id,
      			ec.email_category,
      			p.visibility_site_ids,
      			p.forum_link,
      			p.youtube_video_id,
      			p.isFeatured,
      			p.show_date,
      			ifnull(datediff(p.show_date,date(now())),-100) as show_in_days,
      			p.thumb_path,
      			if(p.thumb_path='','', concat('<% print(classifieds_dir) %>','/thumbs/', p.thumb_path)) as thumb_url,
      			p.active,
      			p.approved,
      			p.approved_by_user_id,
      			CONCAT(u2.first_name, ' ', u2.last_name) as approved_by_user,
      			p.premium_only,
      			p.site_id,
      			sites.site_protocol,
      			sites.site_base_href,
      			p.current_city,
      			p.current_state,
      			p.current_zip,
      			p.current_country,
      			p.contact_name,
      			p.contact_email,
      			p.contact_phone,
      
      			-- user info
      			u.first_name,
      			u.last_name,
      			company_name,
      			u.email,
      			u.city,
      			u.state,
      			u.user_phone_1,
      			u.avatar_small,
      			concat('<% print(profiles_dir) %>', '/thumbs/', u.avatar_small) as avatar_thumb_url,
      			u.sign_up_date,
      			ifnull(
      				(SELECT count(1) FROM forum_post WHERE active = 1 and approved = 1 and user_id = p.user_id)
      				, 0) +
      			ifnull(
      				(SELECT count(1) FROM forum_reply WHERE user_id = p.user_id)
      				, 0)
      					as user_comment_count,
      			(SELECT count(1) FROM forum_opinions where opinion = 1
      				AND user_id = p.user_id) as user_likes,
      			(SELECT count(1) FROM forum_opinions where opinion = 0
      				AND user_id = p.user_id) as user_dislikes,
      
      			(SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 1
      				AND forum_id = p.forum_id AND reply_id is null) as likes,
      			(SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 0
      				AND forum_id = p.forum_id AND reply_id is null) as dislikes,
      
      			(SELECT COUNT(1) FROM forum_reply
      				WHERE forum_id = p.forum_id) + 1 as message_count,
      
      			<% if (paramExistsAdd("u_id")) { %>
      				(SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 1
      					AND forum_id = p.forum_id AND reply_id is null AND user_id = :u_id) as i_like,
      				(SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 0
      					AND forum_id = p.forum_id AND reply_id is null AND user_id = :u_id) as i_dislike,
      				if(fsub.forum_id is null,0,1) as i_subscribe
      			 <% } else { %>
      				0 as i_like,
      				0 as i_dislike,
      				0 as i_subscribe
      			<% } %>
      
      		FROM forum_post p
      			INNER JOIN forum_post_types pts using(post_type_id)
      			INNER JOIN user_info u using(user_id)
      			INNER JOIN user_company uc using(user_company_id)
      			INNER JOIN sites on sites.id = p.site_id
      			LEFT JOIN email_categories ec on pts.email_category_id = ec.email_category_id
      			LEFT JOIN user_info u2 ON u2.user_id = p.approved_by_user_id
      
      			<% if (paramExistsAdd("u_id")) { %>
      				LEFT JOIN forum_subscription fsub on p.forum_id = fsub.forum_id and fsub.user_id = :u_id
      			<% } %>
      
      		WHERE (visibility_site_ids = 0 OR find_in_set(1, visibility_site_ids))
      
      		<% if (paramExistsAdd("forum_id")) { %>
                AND p.forum_id = :forum_id
              <% } %>
      
      		ORDER BY p.forum_id desc
      		limit 100
      """;
}
