package com.bulkloads.web.forum.repository.template;

public class GetForumQueryTemplate {

  @SuppressWarnings({"checkstyle:FileTabCharacter", "checkstyle:LineLength", "checkstyle:Indentation"})
  public static final String GET_FORUM_QUERY_TEMPLATE = """
      SELECT
          <% if (paramIsTrue("count")) { %>
              count(distinct p.forum_id) as total
          <% } else { %>
      
              p.forum_id,
              p.forum_title,
              p.views,
              <% if (paramIsTrue("complete_content")) { %>
                  p.forum_content
              <% } else { %>
                  LEFT(p.forum_content, 500) as forum_content
              <% } %>,
              p.user_id,
              u.first_name,
              u.last_name,
              u.avatar_small,
              concat('<% print(profiles_dir) %>', '/thumbs/', u.avatar_small) as avatar_thumb_url,
              p.date_added,
              p.last_reply,
              ifnull(p.last_reply, ifnull(p.show_date, p.date_added)) as last_post,
              p.alias,
              p.post_type_id,
              pts.post_type,
              pts.maindir,
              concat('<% print(domain_url) %>', pts.maindir, '/', p.alias, '/') as forum_url,
              p.visibility_site_ids,
              p.forum_link,
              p.youtube_video_id,
              p.isFeatured,
              p.show_date,
              ifnull(datediff(p.show_date, date(now())), -100) as show_in_days,
              concat('<% print(classifieds_dir) %>','/thumbs/') as thumb_dir,
              p.thumb_path,
              if(p.thumb_path='', '', concat('<% print(classifieds_dir) %>','/thumbs/', p.thumb_path)) as thumb_url,
              p.active,
              p.approved,
              p.approved_by_user_id,
              CONCAT(u2.first_name, ' ', u2.last_name) as approved_by_user,
              p.premium_only,
              p.current_city,
              p.current_state,
              p.current_zip,
              p.current_country,
              p.current_latitude,
              p.current_longitude,
              <% if (paramExistsAdd("latitude") && paramExists("longitude") && paramExists("radius")) { %>
                  round(st_distance_sphere(
                      point(:longitude, :latitude),
                      point(p.current_longitude, p.current_latitude))/1609.34, 1) as distance
              <% } else { %>
                  CAST(NULL as DECIMAL) as distance
              <% } %>,
              (SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 1 AND forum_id = p.forum_id) as likes,
              (SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 0 AND forum_id = p.forum_id) as dislikes,
              (SELECT COUNT(1) FROM forum_reply WHERE forum_id = p.forum_id and forum_reply.approved = 1) + 1 as message_count,
              <% if (paramExistsAdd("u_id")) { %>
                  (SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 1 AND forum_id = p.forum_id AND reply_id is null AND user_id = :u_id) as i_like,
                  (SELECT COUNT(opinion) FROM forum_opinions WHERE opinion = 0 AND forum_id = p.forum_id AND reply_id is null AND user_id = :u_id) as i_dislike,
                  if(fsub.forum_id is null, 0, 1) as i_subscribe
              <% } else { %>
                  0 as i_like,
                  0 as i_dislike,
                  0 as i_subscribe
              <% } %>,
              <% if (paramExistsAdd("term")) { %>
                  ((length(p.forum_title) - length(replace(lcase(p.forum_title), :term, ''))) / length(:term) / (length(p.forum_title) + 1 - length(replace(p.forum_title, ' ', '')))) * 10 +
                  ((length(p.forum_content) - length(replace(lcase(p.forum_content), :term, ''))) / length(:term) / (length(p.forum_content) + 1 - length(replace(p.forum_content, ' ', '')))) +
                  ((length(ifnull(r.content, '')) - length(replace(lcase(ifnull(r.content, '')), 'loads', ''))) / length('loads') / (length(ifnull(r.content, '')) + 1 - length(replace(ifnull(r.content, ''), ' ', '')))) * 0.1
                   as orderBy
              <% } else if (paramExistsAdd("sort") && paramExists("sort").equals("show_date")) { %>
                  p.show_date as orderBy
              <% } else if (paramExists("sort") && paramExists("sort").equals("date_added")) { %>
                  p.date_added as orderBy
              <% } else { %>
                  ifnull(p.last_reply, ifnull(p.show_date, p.date_added)) as orderBy
              <% } %>
          <% } %>
      FROM forum_post p
      <% if (!paramIsTrue("count")) { %>
          INNER JOIN forum_post_types pts ON p.post_type_id = pts.post_type_id
          INNER JOIN user_info u ON p.user_id = u.user_id
          LEFT JOIN user_info u2 ON p.approved_by_user_id = u2.user_id
          <% if (paramExistsAdd("u_id")) { %>
              LEFT JOIN forum_subscription fsub ON p.forum_id = fsub.forum_id AND fsub.user_id = :u_id
          <% } %>
      <% } %>
      <% if (paramExistsAdd("term")) { %>
          LEFT JOIN forum_reply AS r ON r.forum_id = p.forum_id
      <% } %>
      WHERE (p.visibility_site_ids = 0 OR find_in_set(1, p.visibility_site_ids))
      <% if (paramExistsAdd("forum_id")) { %>
          AND p.forum_id IN (:forum_id)
      <% } else { %>
          <% if (!paramIsTrue("is_site_admin")) { %>
              AND (p.approved = 1
              <% if (paramExistsAdd("u_id")) { %>
                  OR p.user_id = :u_id
              <% } %>
              )
          <% } %>
      <% } %>
      
      
      <% if (paramIsTrue("include_premium")) { %>
      <% } else { %>
          AND p.premium_only = 0
      <% } %>
      
      AND p.active = 1
      <% if (paramExists("user_types") && paramExists("user_types").equals("admin") && paramExists("show_in_days") && (param("show_in_days") == null || param("show_in_days").isEmpty())) { %>
          AND (p.show_date is null or p.show_date <= date(now()))
      <% } %>
      <% if (paramExistsAdd("show_in_days")) { %>
          AND datediff(p.show_date, date(now())) = :show_in_days
      <% } %>
      <% if (paramExistsAdd("max_show_in_days")) { %>
          AND datediff(p.show_date, date(now())) <= :max_show_in_days
      <% } %>
      <% if (paramExistsAdd("term")) { %>
          <% params.put("wildTerm", "%"+term+"%") %>
          AND (p.forum_title like :wildTerm OR p.forum_content like :wildTerm OR r.content like :wildTerm)
      <% } %>
      
      
      <% if (paramExistsAdd("post_type_ids")) { %>
          AND p.post_type_id IN (:post_type_ids)
      <% } %>
      
      <% if (paramExistsAdd("is_featured")) { %>
          AND p.isFeatured = :is_featured
      <% } %>
      <% if (paramExistsAdd("latitude") && paramExistsAdd("longitude") && paramExistsAdd("radius")) { %>
          AND round(st_distance_sphere(
              point(:longitude, :latitude),
              point(p.current_longitude, p.current_latitude))/1609.34, 1) < :radius
      <% } %>
      <% if (paramExists("state")) { %>
          AND p.current_state = :state
      <% } %>
        <% if (!paramIsTrue("count") && paramExists("term")) { %>
            GROUP BY p.forum_id
        <% } %>
      <% if (!paramExists("count")) { %>
          <% if (paramExists("latitude") && paramExists("longitude") && paramExists("radius")) { %>
              ORDER BY distance ASC
          <% } else { %>
              ORDER BY orderBy DESC
          <% } %>
      <% } %>
      <% if (paramExistsAdd("limit")) { %>
        LIMIT
        <% if (paramExistsAdd("skip")) { %>
            :skip,
        <% } %>
        :limit
      <% } %>
      """;
}
