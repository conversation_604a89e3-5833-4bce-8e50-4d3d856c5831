package com.bulkloads.web.forum.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "forum_post_types")
public class ForumPostType {

  public static final String ACCESS_LEVEL_USER = "user";
  public static final String ACCESS_LEVEL_ADMIN = "admin";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "post_type_id")
  private Integer postTypeId;

  @Column(name = "post_type")
  private String postType;

  @Column(name = "accesslevel")
  private String accessLevel;

  @Column(name = "email_category_id")
  private Integer emailCategoryId;

  @Column(name = "maindir",  nullable = false)
  private String maindir;
}
