package com.bulkloads.web.forum.domain.entity;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.forum.service.dto.ForumFileRequest;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class ForumUpdateRequest {

  Integer postTypeId;
  String location;
  String forumTitle;
  String forumContent;
  Optional<Boolean> premiumOnly;
  Optional<String> youtubeVideoId;
  Optional<List<ForumFileRequest>> files;

}
