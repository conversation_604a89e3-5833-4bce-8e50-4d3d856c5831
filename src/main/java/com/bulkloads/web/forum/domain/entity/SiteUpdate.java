package com.bulkloads.web.forum.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "site_updates")
public class SiteUpdate {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "site_update_id", columnDefinition = "int")
  private Integer siteUpdateId;

  @Column(name = "site_update_description", columnDefinition = "varchar(200) default ''")
  private String siteUpdateDescription = "";

  @Column(name = "site_update_posted", columnDefinition = "datetime")
  private Instant siteUpdatePosted;

  @Column(name = "site_update_post_id", columnDefinition = "varchar(200) default ''")
  private String siteUpdatePostId;

  @Column(name = "site_updates_type_id", columnDefinition = "int")
  private Integer siteUpdatesTypeId;

  @Column(name = "site_id", columnDefinition = "int default '1'")
  private Integer siteId = 1;
}

