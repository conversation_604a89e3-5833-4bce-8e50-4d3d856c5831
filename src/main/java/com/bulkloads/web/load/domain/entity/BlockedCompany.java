package com.bulkloads.web.load.domain.entity;

import java.time.LocalDate;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "blocked_companies")
@Getter
@Setter
public class BlockedCompany {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "blocked_user_company_id")
  private Integer blockedUserCompanyId;

  @Column(name = "date_added")
  private LocalDate dateAdded;
}

