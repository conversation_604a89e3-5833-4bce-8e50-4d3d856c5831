package com.bulkloads.web.load.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class FavoriteLoadId implements Serializable {

  @Column(name = "user_id")
  Integer userId;

  @Column(name = "load_id")
  Integer loadId;

  @Column(name = "site_id")
  Integer siteId;

}