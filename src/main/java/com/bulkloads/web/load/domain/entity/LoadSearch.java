package com.bulkloads.web.load.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "load_search")
public class LoadSearch {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "load_search_id")
  private Integer loadSearchId;


  @NotNull
  @Column(name = "user_id")
  private Integer userId;


  @NotNull
  @Column(name = "site_id")
  private Integer siteId = 1;


  @Column(name = "search_type")
  private Integer searchType;


  @Column(name = "date")
  private Instant date;


  @NotNull
  @Size(max = 2, message = "Up to 2 chars")
  @Column(name = "origin_country")
  private String originCountry = "US";


  @NotNull
  @Size(max = 200, message = "Up to 200 chars")
  @Column(name = "origin_state")
  private String originState = "";


  @NotNull
  @Size(max = 60, message = "Up to 60 chars")
  @Column(name = "origin_city")
  private String originCity = "";


  @NotNull
  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "origin_zip")
  private String originZip = "";


  @Column(name = "origin_lat")
  private Double originLat;


  @Column(name = "origin_long")
  private Double originLong;


  @Column(name = "distance")
  private Integer originRadius;


  @NotNull
  @Size(max = 2, message = "Up to 2 chars")
  @Column(name = "destination_country")
  private String destinationCountry = "US";


  @NotNull
  @Size(max = 200, message = "Up to 200 chars")
  @Column(name = "destination_state")
  private String destinationState = "";


  @NotNull
  @Size(max = 60, message = "Up to 60 chars")
  @Column(name = "destination_city")
  private String destinationCity = "";


  @NotNull
  @Size(max = 45, message = "Up to 45 chars")
  @Column(name = "destination_zip")
  private String destinationZip = "";


  @Column(name = "destination_lat")
  private Double destinationLat;


  @Column(name = "destination_long")
  private Double destinationInteger;


  @Column(name = "destination_distance")
  private Integer destinationRadius;


  @NotNull
  @Size(max = 500, message = "Up to 500 chars")
  @Column(name = "equipment")
  private String equipmentIds = "";


  @Column(name = "days")
  private Integer days;


  @NotNull
  @Column(name = "favorite_loads")
  private Boolean onlyFavoriteLoads = false;


  @NotNull
  @Column(name = "offered_loads")
  private Boolean onlyOfferedLoads = false;


  @NotNull
  @Column(name = "favorite_lanes")
  private Boolean onlyFavoriteLanes = false;


  @NotNull
  @Column(name = "favorite_companies")
  private Boolean onlyFavoriteCompanies = false;


  @NotNull
  @Size(max = 10, message = "Up to 10 chars")
  @Column(name = "fl_truck_size")
  private String flTruckSize = "";


  @Column(name = "fl_num_of_stops")
  private Integer flNumOfStops;


  @NotNull
  @Size(max = 5000, message = "Up to 5000 chars")
  @Column(name = "user_company_ids")
  private String userCompanyIds = "";


}