package com.bulkloads.web.load.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "favorite_loads")
public class FavoriteLoad {

  @EmbeddedId
  private FavoriteLoadId favoriteLoadId;
  /*
  @NotNull
  @Column(name = "user_id")
  private Integer userId;

  @NotNull
  @Column(name = "load_id")
  private Integer loadId;

  @NotNull
  @Column(name = "site_id")
  private Integer siteId;
  */
  @NotNull
  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();
}
