package com.bulkloads.web.load.service.dto.transformers;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.load.service.dto.LoadTotalResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadTotalResponseTransformer implements TupleTransformer<LoadTotalResponse> {

  @Override
  public LoadTotalResponse transformTuple(Object[] columns, String[] aliases) {

    QueryParts parts = new QueryParts(columns, aliases);

    return LoadTotalResponse.builder()
        .count(parts.asInteger("count"))
        .sumOfLoads(parts.asInteger("sum_of_loads"))
        .build();
  }

}

