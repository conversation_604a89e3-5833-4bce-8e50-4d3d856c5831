package com.bulkloads.web.load.service;

import static com.bulkloads.common.Parsers.parseIntegerCsvToList;
import static com.bulkloads.common.UserUtil.getAbUserId;
import static com.bulkloads.common.UserUtil.getActor;
import static com.bulkloads.common.UserUtil.getUserCompanyId;
import static com.bulkloads.common.UserUtil.getUserCompanyIdOrThrow;
import static com.bulkloads.common.UserUtil.getUserId;
import static com.bulkloads.common.validation.ValidationUtils.exists;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsFalse;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.existsAndIsTrue;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.config.AppConstants.AppName.MOBILE;
import static com.bulkloads.config.AppConstants.BillWeightUse.LOADED_WEIGHT;
import static com.bulkloads.config.AppConstants.BillWeightUse.UNLOAD_WEIGHT;
import static com.bulkloads.config.AppConstants.DELIVERED_COMPLETED;
import static com.bulkloads.config.AppConstants.LoadAccess.CARRIER;
import static com.bulkloads.config.AppConstants.LoadAccess.PRIVATE;
import static com.bulkloads.config.AppConstants.LoadAccess.PUBLIC;
import static com.bulkloads.config.AppConstants.RateType.FLAT;
import static com.bulkloads.config.AppConstants.RateType.HOUR;
import static com.bulkloads.config.AppConstants.RateType.MILE;
import static com.bulkloads.config.AppConstants.RateType.TWO_K;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.PICKUP;
import static java.nio.charset.StandardCharsets.UTF_8;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.apache.commons.lang3.math.NumberUtils.isParsable;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.mui.model.QueryParams;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.security.Actor;
import com.bulkloads.web.addressbook.abcompany.domain.AbCompanyDomainService;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.assignment.domain.AssignmentDomainService;
import com.bulkloads.web.assignment.domain.BookingDomainService;
import com.bulkloads.web.assignment.domain.data.BlankAssignmentData;
import com.bulkloads.web.assignment.domain.data.BookingData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import com.bulkloads.web.assignment.event.BlankAssignmentCreatedEvent;
import com.bulkloads.web.assignment.event.BookingCreatedEvent;
import com.bulkloads.web.assignment.mapper.AssignmentMapper;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.commodity.domain.CommodityDomainService;
import com.bulkloads.web.commodity.domain.data.CommodityData;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.commodity.repository.CommodityRepository;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.contracts.repository.ContractRepository;
import com.bulkloads.web.load.api.dto.MyLoadV2ListResponse;
import com.bulkloads.web.load.domain.LoadDomainService;
import com.bulkloads.web.load.domain.data.LoadData;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.load.domain.entity.LoadAccessStats;
import com.bulkloads.web.load.event.LoadActivatedEvent;
import com.bulkloads.web.load.event.LoadCreatedEvent;
import com.bulkloads.web.load.event.LoadDeactivatedEvent;
import com.bulkloads.web.load.event.LoadReroutedEvent;
import com.bulkloads.web.load.event.LoadUpdatedEvent;
import com.bulkloads.web.load.mapper.LoadMapper;
import com.bulkloads.web.load.repository.LoadRepository;
import com.bulkloads.web.load.service.dto.CreateUpdateLoadResponse;
import com.bulkloads.web.load.service.dto.LoadListResponse;
import com.bulkloads.web.load.service.dto.LoadRequest;
import com.bulkloads.web.load.service.dto.LoadResponse;
import com.bulkloads.web.load.service.dto.LoadSearchRequest;
import com.bulkloads.web.load.service.dto.LoadTotalResponse;
import com.bulkloads.web.load.service.dto.RerouteLoadAssignment;
import com.bulkloads.web.load.service.dto.RerouteLoadRequest;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.routing.domain.vo.Coordinates;
import com.bulkloads.web.routing.domain.vo.Location;
import com.bulkloads.web.routing.service.RouteService;
import com.bulkloads.web.routing.service.dto.RouteDto;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import com.bulkloads.web.usercompany.domain.entity.UserCompanySettings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadService {

  public static final String GENERAL = "general";
  public static final String UNASSIGNED = "Unassigned";
  public static final String LOAD_ID = "load_id";
  public static final String LOAD_ASSIGNMENT_IDS = "load_assignment_ids";
  public static final String REROUTE_AB_COMPANY_ID = "reroute_ab_company_id";
  public static final String LOAD_ASSIGNMENT_ID = "load_assignment_id";
  public static final String REROUTE_CONTRACT_ID = "reroute_contract_id";
  public static final String RATE_TYPE = "rate_type";
  public static final String EST_HOURS = "est_hours";
  public static final String EST_MILES = "est_miles";
  public static final String EST_WEIGHT = "est_weight";
  public static final String RATE = "rate";
  public static final String REROUTE_RATE = "reroute_rate";
  public static final String REROUTE_RATE_TYPE = "reroute_rate_type";
  private static final Map<String, String> SORT_OPTIONS = Map.ofEntries(
      Map.entry("Oldest", "post_date asc"),
      Map.entry("Leads 1-9", "lead_count asc, post_date desc"),
      Map.entry("Leads 9-1", "lead_count desc, post_date desc"),
      Map.entry("Origin State A-Z", "origin_state asc, origin_city asc"),
      Map.entry("Origin State Z-A", "origin_state desc, origin_city asc"),
      Map.entry("Origin City A-Z", "origin_city asc, origin_state asc"),
      Map.entry("Origin City Z-A", "origin_city desc, origin_state asc"),
      Map.entry("Destination State A-Z", "destination_state asc, destination_city asc"),
      Map.entry("Destination State Z-A", "destination_state desc, destination_city asc"),
      Map.entry("Destination City A-Z", "destination_city asc, destination_state asc"),
      Map.entry("Destination City Z-A", "destination_city desc, destination_state asc"),
      Map.entry("Private First", "load_access asc, post_date desc"),
      Map.entry("Public First", "load_access desc, post_date desc"),
      Map.entry("Shipper A-Z", "company_name asc, post_date desc"),
      Map.entry("Shipper Z-A", "company_name desc, post_date desc"),
      Map.entry("Equipment A-Z", "equipment_ids asc, post_date desc"),
      Map.entry("Equipment Z-A", "equipment_ids desc, post_date desc"),
      Map.entry("Shipping Date From 1-9", "ship_from asc, ship_to asc"),
      Map.entry("Shipping Date From 9-1", "ship_from desc, ship_to asc"),
      Map.entry("Shipping Date To 1-9", "ship_to asc, ship_from asc"),
      Map.entry("Shipping Date To 9-1", "ship_to desc, ship_from asc"),
      Map.entry("Miles 1-9", "estimated_miles asc, post_date desc"),
      Map.entry("Miles 9-1", "estimated_miles desc, post_date desc"),
      Map.entry("Bounce Miles 1-9", "origin_bouncemiles asc, current_bouncemiles asc, post_date desc"),
      Map.entry("Bounce Miles 9-1", "origin_bouncemiles desc, current_bouncemiles desc, post_date desc"),
      Map.entry("## of Loads 1-9", "number_of_available_loads asc, post_date desc"),
      Map.entry("## of Loads 9-1", "number_of_available_loads desc, post_date desc"),
      Map.entry("Rate 1-9", "rate asc, post_date desc"),
      Map.entry("Rate 9-1", "rate desc, post_date desc"),
      Map.entry("Product A-Z", "rate_product_category asc, post_date desc"),
      Map.entry("Product Z-A", "rate_product_category desc, post_date desc"),
      Map.entry("Newest Offers", "offer_id desc, offer_status asc"),
      Map.entry("Oldest Offers", "offer_id asc, offer_status asc"),
      Map.entry("Rating", "avg_rating desc, post_date desc")
  );
  public static final Comparator<Assignment> ASSIGNMENT_COMPARATOR = (a1, a2)
      -> a2.getLoadAssignmentId().compareTo(a1.getLoadAssignmentId());

  private final LoadDomainService loadDomainService;
  private final AssignmentDomainService assignmentDomainService;
  private final BookingDomainService bookingDomainService;
  private final AbCompanyDomainService abCompanyDomainService;
  private final CommodityDomainService commodityDomainService;

  private final LoadMapper loadMapper;
  private final AssignmentMapper assignmentMapper;

  private final LoadRepository loadRepository;
  private final AbCompanyRepository abCompanyRepository;
  private final ContractRepository contractRepository;
  private final CommodityRepository commodityRepository;
  private final RateTypeRepository rateTypeRepository;
  private final AssignmentRepository assignmentRepository;

  private final RouteService routeService;
  private final Validator validator;
  private final UserService userService;

  @Transactional
  public CreateUpdateLoadResponse createLoad(final LoadRequest request) {
    final Map<String, String> errors = new HashMap<>();

    enrichRequestWithCreateDefaults(request);

    final LoadData loadData = mapToData(request, errors);
    final Result<Load> loadResult = loadDomainService.create(loadData);

    final Load loadToBeSaved = loadResult.getEntity();

    createAssignmentsAndBookings(loadData, loadToBeSaved, errors);

    checkForErrors(errors);

    // save to get id
    Load savedLoad = loadRepository.save(loadToBeSaved);
    final int loadId = savedLoad.getLoadId();

    refreshCalculatedFields(savedLoad);
    refreshAssignmentCalculatedFields(savedLoad.getLoadId());

    final List<Integer> assignmentIds = savedLoad.getAssignments().stream().map(Assignment::getLoadAssignmentId).toList();

    registerCreatedEvents(savedLoad, assignmentIds);

    loadRepository.save(savedLoad);

    return new CreateUpdateLoadResponse(loadId, assignmentIds);
  }

  @Transactional
  public CreateUpdateLoadResponse updateLoad(final int loadId, final LoadRequest request) {
    final Map<String, String> errors = new HashMap<>();
    final Load existingEntity = loadRepository.findById(loadId).orElseThrow(() -> new ValidationException(LOAD_ID, "You don't own this load"));

    enrichRequestWithUpdateDefaults(request, existingEntity);

    final LoadData loadData = mapToData(request, errors);

    final Result<Load> loadResult = loadDomainService.update(existingEntity, loadData);

    errors.putAll(loadResult.getErrors());

    checkForErrors(errors);

    final Load load = loadResult.getEntity();

    final Map<String, List<Assignment>> assignmentMap = updateAssignmentsAndBookings(loadData, load, errors);

    checkForErrors(errors);

    final Load savedLoad = loadRepository.save(load);

    refreshCalculatedFields(savedLoad);
    refreshAssignmentCalculatedFields(savedLoad.getLoadId());

    registerUpdatedEvents(savedLoad, assignmentMap);

    loadRepository.save(savedLoad);

    final List<Integer> assignmentIds = load.getAssignments().stream().map(Assignment::getLoadAssignmentId).toList();
    return new CreateUpdateLoadResponse(loadId, assignmentIds);
  }

  @Transactional
  public void refreshAssignmentCalculatedFields(final int loadId) {
    final Load load = loadRepository.findById(loadId)
        .orElseThrow(() -> new BulkloadsException("Load %s not found".formatted(loadId)));

    int numberOfAssignedLoads = 0;
    int numberOfAssignedDrivers = 0;
    int numberOfDeliveredLoads = 0;
    int numberOfAvailableLoads = load.getNumberOfLoads();

    if (load.getIsManaged()) {

      for (Assignment assignment : load.getAssignments()) {
        final String assignmentStatus = assignment.getAssignmentStatus();
        if (!UNASSIGNED.equals(assignmentStatus) && !assignment.getDeleted()) {
          numberOfAvailableLoads--;
          numberOfAssignedLoads++;

          if (DELIVERED_COMPLETED.contains(assignmentStatus)) {
            numberOfDeliveredLoads++;
          }

          if (assignment.getIsDriver()) {
            numberOfAssignedDrivers++;
          }
        }
      }
    } else {
      for (Assignment booking : load.getBookings()) {
        final String bookingStatus = booking.getAssignmentStatus();
        if (!UNASSIGNED.equals(bookingStatus) && DELIVERED_COMPLETED.contains(bookingStatus) && !booking.getToDeleted()) {
          numberOfDeliveredLoads++;
        }
      }
    }

    load.setNumberOfAssignedLoads(numberOfAssignedLoads);
    load.setNumberOfAssignedDrivers(numberOfAssignedDrivers);
    load.setNumberOfDeliveredLoads(numberOfDeliveredLoads);
    load.setNumberOfAvailableLoads(numberOfAvailableLoads);
    load.setModifiedDate(Instant.now());

    if (numberOfAvailableLoads <= 0) {
      load.setLoadAccess(PRIVATE);
    }

    loadRepository.save(load);
  }

  @Transactional
  public void activateUserLoad(final int loadId) {
    final int userCompanyId = getUserCompanyIdOrThrow();
    final Load load = loadRepository.findByLoadIdAndUserCompanyUserCompanyId(loadId, userCompanyId)
        .orElseThrow(() -> new ValidationException(GENERAL, "You don't own this load"));

    doActivateLoad(load);
  }

  @Transactional
  public void activateLoad(final int loadId) {
    final Load load = loadRepository.findById(loadId)
        .orElseThrow(() -> new ValidationException(GENERAL, "You don't own this load"));

    doActivateLoad(load);
  }

  @Transactional
  public void deactivateLoad(final int loadId) {
    // @TODO incomplete implementation
    final Load load = loadRepository.findById(loadId)
        .orElseThrow(() -> new ValidationException(GENERAL, "You don't own this load"));

    final Instant now = Instant.now();
    load.setActive(false);
    load.setInactiveByUserId(UserUtil.getUserIdOrThrow());
    load.setInactiveDate(now);
    load.setModifiedDate(now);

    load.registerDomainEvent(new LoadDeactivatedEvent(load.getLoadId()));
    loadRepository.save(load);
  }

  public boolean handleBookingUpdated(final Assignment booking) {
    if (isNull(booking.getToLoad())) {
      return false;
    }
    final Load load = booking.getToLoad();

    if (Boolean.FALSE.equals(load.getActive())) {
      return false;
    }

    final int loadId = load.getLoadId();

    final boolean allBookingStatusesAreEitherDeliveredOrCompleted = load.getBookings().stream()
        .filter(it -> !it.getIsIntraCompany())
        .map(Assignment::getAssignmentStatus)
        .allMatch(DELIVERED_COMPLETED::contains);

    if (!Boolean.TRUE.equals(load.getIsManaged()) && allBookingStatusesAreEitherDeliveredOrCompleted) {
      deactivateLoad(loadId);
      return true;
    }
    return false;
  }

  public List<LoadListResponse> getLoads(final LoadSearchRequest loadSearchRequest,
                                         final String order,
                                         final Integer skip,
                                         final Integer limit) {
    final Integer uId = getUserId().orElse(null);
    final Integer cId = getUserCompanyId().orElse(null);
    final Integer abUserId = getAbUserId().orElse(null);
    final boolean isPro = getActor().map(Actor::isPro).orElse(false);

    // order -> orderBy
    String orderText = Optional.ofNullable(order).orElse("");
    if (orderText.isEmpty()) {
      if (loadSearchRequest.getOriginRadius() != null || loadSearchRequest.getDestinationRadius() != null) {
        orderText = "Bounce Miles 1-9";
      } else if (loadSearchRequest.getOriginState() != null) {
        orderText = "Origin City A-Z";
      } else if (loadSearchRequest.getDestinationState() != null) {
        orderText = "Destination City A-Z";
      }
    }
    final String orderBy = SORT_OPTIONS.getOrDefault(orderText, "post_date desc");
    return loadRepository.findLoads(uId, abUserId, cId, isPro, loadSearchRequest, orderBy, skip, limit);
  }

  public List<MyLoadV2ListResponse> getMyLoadsV2(Boolean active, String userIds,
                                                 QueryParams queryParams,
                                                 int skip, int limit) {
    final int cId = getUserCompanyIdOrThrow();
    return loadRepository.findMyLoadsV2(cId, active, parseIntegerCsvToList(userIds), queryParams, skip, limit);
  }

  public LoadTotalResponse getMyLoadsTotalsV2(Boolean active, String userIds, QueryParams queryParams) {
    final int cId = getUserCompanyIdOrThrow();
    return loadRepository.findMyLoadsTotalsV2(cId, active, parseIntegerCsvToList(userIds), queryParams);
  }

  public LoadTotalResponse getLoadTotals(LoadSearchRequest loadSearchRequest) {
    final Integer uId = getUserId().orElse(null);
    final Integer cId = getUserCompanyId().orElse(null);
    final Integer abUserId = getAbUserId().orElse(null);
    final boolean isPro = getActor().map(Actor::isPro).orElse(false);

    return loadRepository.findLoadTotals(uId, abUserId, cId, isPro, loadSearchRequest);
  }

  public LoadResponse getLoadById(final Integer loadId) {
    final Integer uId = getUserId().orElse(null);
    final Integer cId = getUserCompanyId().orElse(null);
    final Integer abUserId = getAbUserId().orElse(null);
    final boolean isPro = getActor().map(Actor::isPro).orElse(false);

    return loadRepository.findLoadById(uId, abUserId, cId, isPro, loadId);
  }

  public void rerouteLoad(final int loadId, final RerouteLoadRequest dto) {

    final Load load = findLoadById(loadId);

    if (load.getIsRootLoad()) {
      throw new ValidationException(REROUTE_AB_COMPANY_ID, "Only the person that assigned you the load can reroute it. You can request a reroute.");
    }

    final Map<String, String> errors = new HashMap<>();

    final Optional<AbCompany> rerouteAbCompany = findRerouteAbCompany(dto, errors);
    validateRerouteContract(dto, errors);

    errors.putAll(validator.validate(dto)
        .stream()
        .collect(Collectors.toMap(v -> v.getPropertyPath().toString(), ConstraintViolation::getMessage)));

    checkForErrors(errors);

    handleLoadAssignments(load, dto, rerouteAbCompany.orElse(null));

    loadRepository.save(load);
  }

  private void refreshCalculatedFields(final Load load) {
    final User user = userService.getLoggedInUser();
    final Integer userCompanyId = user.getUserCompany().getUserCompanyId();
    final UserCompanySettings userCompanySettings = user.getUserCompany().getUserCompanySettings();
    final Map<String, LoadAccessStats> loadAccessStatsByLoadAccess = loadRepository
        .findLoadAccessStats(userCompanyId).stream()
        .collect(Collectors.toMap(LoadAccessStats::loadAccess, l -> l));

    final LoadAccessStats privateLoadAccessStats = loadAccessStatsByLoadAccess.get(PRIVATE);
    final LoadAccessStats carrierLoadAccessStats = loadAccessStatsByLoadAccess.get(CARRIER);
    final LoadAccessStats publicLoadAccessStats = loadAccessStatsByLoadAccess.get(PUBLIC);

    if (nonNull(privateLoadAccessStats)) {
      userCompanySettings.setPrivateLoadPosts(privateLoadAccessStats.loadPosts().intValue());
      userCompanySettings.setPrivateLoadCount(privateLoadAccessStats.loadCount().intValue());
    }

    if (nonNull(carrierLoadAccessStats)) {
      userCompanySettings.setCarrierLoadPosts(carrierLoadAccessStats.loadPosts().intValue());
      userCompanySettings.setCarrierLoadCount(carrierLoadAccessStats.loadCount().intValue());
    }

    if (nonNull(publicLoadAccessStats)) {
      userCompanySettings.setPublicLoadPosts(publicLoadAccessStats.loadPosts().intValue());
      userCompanySettings.setPublicLoadCount(publicLoadAccessStats.loadCount().intValue());
    }

    //update load_details_url
    final String loadDetailsUrl = getLoadDetailsUrl(load);
    load.setLoadDetailsUrl(loadDetailsUrl);
  }

  private String getLoadDetailsUrl(final Load load) {
    final String originState = load.getOriginState();
    final String originCity = URLEncoder.encode(load.getOriginCity(), UTF_8);
    final String destinationState = load.getDestinationState();
    final String destinationCity = URLEncoder.encode(load.getDestinationCity(), UTF_8);
    final String equipmentNames = URLEncoder.encode(String.join("-", load.getEquipmentNames()), UTF_8);
    final int loadId = load.getLoadId();

    return "/loads/%s-%s-%s-%s/%s/%s/".formatted(originState, originCity, destinationState, destinationCity, equipmentNames, loadId).toLowerCase();
  }

  private void registerCreatedEvents(final Load savedLoad, final List<Integer> assignmentIds) {
    final List<Integer> bookingIds = savedLoad.getBookings().stream().map(Assignment::getLoadAssignmentId).toList();
    savedLoad.registerDomainEvent(new LoadCreatedEvent(savedLoad.getLoadId()));

    if (!assignmentIds.isEmpty()) {
      savedLoad.registerDomainEvent(new BlankAssignmentCreatedEvent(assignmentIds));
    }

    if (!bookingIds.isEmpty()) {
      savedLoad.registerDomainEvent(new BookingCreatedEvent(bookingIds));
    }
  }

  private void registerUpdatedEvents(final Load savedLoad, final Map<String, List<Assignment>> assignmentIds) {
    final List<Integer> bookingIds = assignmentIds.get("bookings").stream().map(Assignment::getLoadAssignmentId).toList();
    final List<Integer> deletedBookingIds = assignmentIds.get("deleted_bookings").stream().map(Assignment::getLoadAssignmentId).toList();
    final List<Integer> updatedAssignmentIds = assignmentIds.get("assignments").stream().map(Assignment::getLoadAssignmentId).toList();
    final List<Integer> deletedAssignmentIds = assignmentIds.get("deleted_assignments").stream().map(Assignment::getLoadAssignmentId).toList();

    final LoadUpdatedEvent loadUpdatedEvent = new LoadUpdatedEvent(savedLoad.getLoadId(), bookingIds,
        deletedBookingIds, updatedAssignmentIds, deletedAssignmentIds);
    savedLoad.registerDomainEvent(loadUpdatedEvent);
  }

  private void createAssignmentsAndBookings(final LoadData loadData, final Load load, final Map<String, String> errors) {
    fixAssignments(loadData, load);

    final boolean isManaged = loadData.getIsManaged().get();
    final boolean isBroker = loadData.getIsBroker().get();

    if (existsAndIsNotEmpty(loadData.getAssignments())) {
      final List<BlankAssignmentData> blankAssignmentData = loadData.getAssignments().get();

      for (int i = 0; i < loadData.getNumberOfLoads().get(); i++) {
        Assignment assignment = null;
        Assignment booking = null;
        if (isManaged) {
          final Result<Assignment> assignmentResult = assignmentDomainService.createBlankAssignment(load, blankAssignmentData.get(i));
          assignment = assignmentResult.getEntity();
          load.getAssignments().add(assignment);
          errors.putAll(assignmentResult.getErrors());
          assignment.setChainLoadAssignment(assignment);
        }

        if (isBroker) {
          final BookingData bookingData = assignmentMapper.mapToBookingData(loadData, blankAssignmentData.get(i));
          bookingData.setToLoad(Optional.of(load));
          final Result<Assignment> bookingResult = bookingDomainService.create(bookingData);
          booking = bookingResult.getEntity();
          load.getBookings().add(booking);
          errors.putAll(bookingResult.getErrors());
          if (nonNull(assignment)) {
            booking.setChainLoadAssignment(assignment);
          } else {
            booking.setChainLoadAssignment(booking);
          }
        }

        if (nonNull(assignment) && nonNull(booking)) {
          assignment.setParentLoadAssignment(booking);
          booking.setChildLoadAssignment(assignment);
        }
      }
    }
  }

  private Map<String, List<Assignment>> updateAssignmentsAndBookings(final LoadData loadData, final Load load, final Map<String, String> errors) {
    fixAssignments(loadData, load);

    final boolean isManaged = load.getIsManaged();
    final boolean isBroker = load.getIsBroker();
    final int newNumberOfLoads = load.getNumberOfLoads();

    //affected bookings
    final List<Assignment> bookingLoadAssignment = new ArrayList<>();
    final List<Assignment> deletedBookingLoadAssignment = new ArrayList<>();

    //affected assignments
    final List<Assignment> assignmentLoadAssignment = new ArrayList<>();
    final List<Assignment> deletedAssignmentLoadAssignment = new ArrayList<>();

    final List<Assignment> assignments = load.getAssignments();
    final List<Assignment> bookings = load.getBookings().stream()
        .filter(booking -> !booking.getIsIntraCompany())
        .sorted(ASSIGNMENT_COMPARATOR)
        .toList();

    final int assignmentsSize = assignments.size();
    final int bookingsSize = bookings.size();

    //delete assignments & parent bookings
    if (assignmentsSize > newNumberOfLoads || !isManaged) {
      int numberOfAssignmentsToDelete = assignmentsSize;
      if (isManaged) {
        numberOfAssignmentsToDelete = assignmentsSize - newNumberOfLoads;
      }

      if (numberOfAssignmentsToDelete > 0) {
        final List<Assignment> unassignedAssignments = assignments.stream()
            .filter(assignment -> assignment.getAssignmentStatus().equals(UNASSIGNED))
            .sorted(ASSIGNMENT_COMPARATOR)
            .toList();

        //check if there are enough blanks to delete, otherwise return error
        if (unassignedAssignments.size() < numberOfAssignmentsToDelete) {
          throw new ValidationException("number_of_loads",
              "%s loads have already been assigned. To reduce the number of loads delete the assignments first.".formatted(
                  assignmentsSize - unassignedAssignments.size()));
        }

        for (int i = 0; i < numberOfAssignmentsToDelete; i++) {
          final Assignment unassignedAssignment = unassignedAssignments.get(unassignedAssignments.size() - 1 - i);
          final Assignment parentLoadAssignment = unassignedAssignment.getParentLoadAssignment();
          deleteAssignment(unassignedAssignment);
          //affected load_assignment_ids
          deletedAssignmentLoadAssignment.add(unassignedAssignment);

          if (nonNull(parentLoadAssignment)) {
            parentLoadAssignment.setChildLoadAssignment(null);
          }

          //if managed & parent, delete booking
          if (isManaged && nonNull(parentLoadAssignment)) {
            deleteBooking(parentLoadAssignment);
            //affected load_assignment_ids
            deletedAssignmentLoadAssignment.add(parentLoadAssignment);
          }
        }
      }
    }

    //delete bookings if no assignments
    if (!isManaged && (bookingsSize > newNumberOfLoads
        || !isBroker)) { //if managed, the bookings are deleted in the previous step through the deleted assignments
      int numberOfBookingsToDelete = bookingsSize;
      if (isBroker) {
        numberOfBookingsToDelete = bookingsSize - newNumberOfLoads;
      }

      if (numberOfBookingsToDelete > 0) {

        for (int i = 0; i < numberOfBookingsToDelete; i++) {
          final Assignment booking = bookings.get(bookings.size() - 1 - i);
          deleteBooking(booking);
          //affected load_assignment_ids
          deletedAssignmentLoadAssignment.add(booking);
        }
      }
    }

    if (existsAndIsNotEmpty(loadData.getAssignments())) {
      final List<BlankAssignmentData> blankAssignmentData = loadData.getAssignments().get();

      for (int i = 0; i < blankAssignmentData.size(); i++) {
        final BlankAssignmentData blankAssignmentDatum = blankAssignmentData.get(i);

        Assignment booking = null;
        Assignment assignment = null;

        if (isBroker) {
          Optional<Assignment> lb = Optional.empty();

          if (nonNull(blankAssignmentDatum.getLoadAssignmentId())) {
            assignment = assignments.stream()
                .filter(ass -> ass.getLoadAssignmentId().equals(blankAssignmentDatum.getLoadAssignmentId()))
                .findFirst().orElseThrow(
                    () -> new ValidationException("assignments",
                        "The assignment with id %s was not found".formatted(blankAssignmentDatum.getLoadAssignmentId()))
                );

            lb = Optional.ofNullable(assignment.getParentLoadAssignment());
          } else if (assignmentsSize > i) {
            assignment = assignments.get(i);
            lb = Optional.ofNullable(assignment.getParentLoadAssignment());
          } else if (bookingsSize > i) {
            lb = Optional.of(bookings.get(i));
          }

          if (lb.isPresent()) { //update booking
            booking = lb.get();

            //update the non-invoiced bookings w/ the default_bill_weight_use if no load_id
            if (!booking.getReadyToInvoice() && !booking.getAutoInvoice() && isNull(booking.getLoad())) {
              booking.setBillWeightUse(load.getDefaultBillWeightUse());
            }

            final BookingData bookingData = assignmentMapper.mapToBookingData(loadData, blankAssignmentDatum);
            bookingData.setToLoad(Optional.of(load));
            final Result<Assignment> bookingResult = bookingDomainService.update(booking, bookingData);
            errors.putAll(bookingResult.getErrors());
            bookingLoadAssignment.add(booking);

          } else { //no existing, create booking
            final BookingData bookingData = assignmentMapper.mapToBookingData(loadData, blankAssignmentDatum);
            bookingData.setToLoad(Optional.of(load));
            final Result<Assignment> bookingResult = bookingDomainService.create(bookingData);
            booking = bookingResult.getEntity();
            load.getBookings().add(booking);
            errors.putAll(bookingResult.getErrors());
            bookingLoadAssignment.add(booking);
          }
        }

        if (isManaged) {

          if (nonNull(blankAssignmentDatum.getLoadAssignmentId())) {
            assignment = assignments.stream()
                .filter(ass -> ass.getLoadAssignmentId().equals(blankAssignmentDatum.getLoadAssignmentId()))
                .findFirst().orElseThrow(
                    () -> new ValidationException("assignments",
                        "The assignment with id %s was not found".formatted(blankAssignmentDatum.getLoadAssignmentId()))
                );
          } else if (assignmentsSize > i) {
            assignment = assignments.get(i);
          }

          if (nonNull(assignment)) {

            //if the assignment is blank, update its bill_weight_use with the load's default
            if (assignment.getAssignmentStatus().equals(UNASSIGNED)) {
              assignment.setBillWeightUse(load.getDefaultBillWeightUse());
            }

            assignmentMapper.blankAssignmentDataToEntity(blankAssignmentDatum, assignment);
            assignmentLoadAssignment.add(assignment);
          } else { //create blank assignment
            final Result<Assignment> assignmentResult = assignmentDomainService.createBlankAssignment(load, blankAssignmentDatum);
            assignment = assignmentResult.getEntity();
            load.getAssignments().add(assignment);
            errors.putAll(assignmentResult.getErrors());
            assignmentLoadAssignment.add(assignment);
          }
        }

        if (nonNull(assignment) && nonNull(booking)) {
          assignment.setParentLoadAssignment(booking);
          booking.setChildLoadAssignment(assignment);
        }
      } //end assignments loop
    }

    //delete assignments not in affected ids
//    final List<Assignment> assignmentsToDelete = load.getAssignments().stream()
//        .filter(it -> !assignmentLoadAssignment.contains(it))
//        .toList();
//
//    final int noOfAssignmentsToDelete = assignmentsToDelete.size();
//    if (noOfAssignmentsToDelete > 0) {
//
//      final List<Assignment> unassignedAssignmentsToDelete = load.getAssignments().stream()
//          .filter(it -> !assignmentLoadAssignment.contains(it))
//          .filter(assignment -> assignment.getAssignmentStatus().equals(UNASSIGNED))
//          .toList();
//
//      if (unassignedAssignmentsToDelete.size() < noOfAssignmentsToDelete) {
//        throw new ValidationException("number_of_loads", ("%s loads have already been assigned. "
//            + "To reduce the number of loads delete the assignments first.").formatted(noOfAssignmentsToDelete - unassignedAssignmentsToDelete.size()));
//      }
//
//      for (int i = 0; i < noOfAssignmentsToDelete; i++) {
//        final Assignment unassignedAssignmentToDelete = unassignedAssignmentsToDelete.get(i);
//        deleteAssignment(unassignedAssignmentToDelete);
//        //affected load_assignment_ids
//        deletedAssignmentLoadAssignment.add(unassignedAssignmentToDelete);
//      }
//    }

    //delete bookings not in affected ids
//    final List<Assignment> bookingsToDelete = bookings.stream()
//        .filter(it -> !bookingLoadAssignment.contains(it))
//        .toList();
//
//    final int noOfBookingsToDelete = bookingsToDelete.size();
//    if (noOfBookingsToDelete > 0) {
//
//      for (Assignment bookingToDelete : bookingsToDelete) {
//        deleteBooking(bookingToDelete);
//        //affected load_assignment_ids
//        deletedBookingLoadAssignment.add(bookingToDelete);
//      }
//    }

    return Map.of(
        "bookings", bookingLoadAssignment,
        "deleted_bookings", deletedBookingLoadAssignment,
        "assignments", bookingLoadAssignment,
        "deleted_assignments", deletedAssignmentLoadAssignment);
  }

  private void deleteBooking(final Assignment assignment) {
    final Assignment childLoadAssignment = assignment.getChildLoadAssignment();
    final Instant now = Instant.now();
    assignment.setChildLoadAssignment(null);
    assignment.setToDeleted(true);
    assignment.setToDeletedDate(now);
    assignment.setToDeletedByUserId(UserUtil.getUserIdOrThrow());
    assignment.setToDeletedMessage("");

    final Load parentLoad = assignment.getLoad();
    if (isNull(parentLoad)) {
      assignment.setDeleted(true);
      assignment.setDeletedDate(now);
      assignment.setDeletedByUserId(UserUtil.getUserIdOrThrow());
    }
    assignment.setModifiedDate(now);

    if (nonNull(childLoadAssignment)) {
      childLoadAssignment.setParentLoadAssignment(null);
    }
  }

  private void deleteAssignment(final Assignment assignment) {
    final Instant instant = Instant.now();
    assignment.setParentLoadAssignment(null);
    assignment.setDeleted(true);
    assignment.setDeletedDate(instant);
    assignment.setDeletedByUserId(UserUtil.getUserIdOrThrow());
    assignment.setDeletedMessage("");
    assignment.setModifiedDate(instant);
  }

  private void fixAssignments(final LoadData loadData, final Load load) {
    if ((existsAndIsTrue(loadData.getIsManaged()) || existsAndIsTrue(loadData.getIsBroker()))) {

      final List<BlankAssignmentData> assignments = collectBlankAssignmentData(loadData, load);

      if (assignments.size() == load.getNumberOfLoads()) {
        Set<String> existingLaNumbers;
        //generated numbers shouldn't be duplicates within load
        existingLaNumbers = assignments.stream()
            .filter(assignment -> exists(assignment.getLoadAssignmentNumber()))
            .map(assignment -> assignment.getLoadAssignmentNumber().trim())
            .collect(Collectors.toSet());

        for (BlankAssignmentData assignment : assignments) {
          if (isEmpty(assignment.getLoadAssignmentNumber())) {
            String laNumber;
            int uniqueIndex = 1;

            if (existsAndIsNotEmpty(loadData.getLoContractNumber())) {
              laNumber = loadData.getLoContractNumber().get();
              if (assignments.size() != 1) {
                laNumber = "%s-%s".formatted(laNumber, uniqueIndex);
              }
            } else {
              //TODO
              laNumber = nonNull(load.getLoadId()) ? load.getLoadId().toString() : "";
              if (assignments.size() != 1) {
                laNumber = "%s-%s".formatted(laNumber, uniqueIndex);
              }
            }

            while (existingLaNumbers.contains(laNumber)) {
              uniqueIndex++;
              if (exists(loadData.getLoContractNumber())) {
                laNumber = "%s-%s".formatted(loadData.getLoContractNumber().get(), uniqueIndex);
              } else {
                laNumber = "%s-%s".formatted(load.getLoadId(), uniqueIndex);
              }
            }

            assignment.setLoadAssignmentNumber(laNumber);
            existingLaNumbers.add(laNumber);
          }
        }
      }
    }
  }

  private List<BlankAssignmentData> collectBlankAssignmentData(final LoadData loadData, final Load load) {
    List<BlankAssignmentData> assignments;
    final int noOfLoads = load.getNumberOfLoads();
    if (isMissingOrIsEmpty(loadData.getAssignments())) {
      assignments = new ArrayList<>();
      if (nonNull(load.getLoadId())) { //update only
        //populate from existing data
        final List<Assignment> assignmentEntities = assignmentRepository.findAllByLoadIdAndDeletedIsFalse(load.getLoadId(), noOfLoads);
        assignments = assignmentMapper.entitiesToBlankData(assignmentEntities);
        if (assignments.isEmpty()) {
          //load may only have bookings
          final List<Assignment> bookingEntities =
              assignmentRepository.findAllByToLoadIdAndDeletedIsFalseAndIntraCompanyIsFalse(load.getLoadId(), noOfLoads);
          assignments = assignmentMapper.entitiesToBlankData(bookingEntities);
        }
      }

      while (assignments.size() < noOfLoads) {
        assignments.add(buildEmptyAssignment());
      }

      loadData.setAssignments(Optional.of(assignments));
    } else {
      assignments = loadData.getAssignments().get();
    }
    return assignments;
  }

  private void checkForErrors(final Map<String, String> errors) {
    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }
  }

  private BlankAssignmentData buildEmptyAssignment() {
    final BlankAssignmentData assignment = new BlankAssignmentData();
    assignment.setLoadAssignmentNumber("");
    assignment.setPickupNumber("");
    assignment.setDropNumber("");
    assignment.setWorkOrderNumber("");
    return assignment;
  }

  private LoadData mapToData(final LoadRequest request, final Map<String, String> errors) {
    final LoadData loadData = loadMapper.requestToData(request, errors);

    if (exists(request.getLoCommodity()) && !exists(request.getCommodityId())) {
      if (!isEmpty(request.getLoCommodity())) {
        final String commodityName = request.getLoCommodity().get();
        final int userCompanyIdOrThrow = getUserCompanyIdOrThrow();
        final Optional<Commodity> commodityOpt =
            commodityRepository.findByCommodityAndUserCompanyUserCompanyIdAndDeletedFalse(commodityName, userCompanyIdOrThrow);

        if (commodityOpt.isPresent()) {
          loadData.setCommodity(commodityOpt);
        } else {
          final CommodityData commodityData = new CommodityData();
          commodityData.setEquipmentIds(loadData.getEquipmentIds());
          commodityData.setCommodity(Optional.of(commodityName));
          final Result<Commodity> commodityResult = commodityDomainService.create(commodityData);
          if (commodityResult.hasErrors()) {
            errors.putAll(commodityResult.getErrors());
          }
          loadData.setCommodity(Optional.of(commodityResult.getEntity()));
        }
      } else {
        loadData.setCommodity(Optional.empty());
      }
    }

    return loadData;
  }

  private void enrichRequestWithCreateDefaults(final LoadRequest request) {
    request.setIsRootLoad(exists(request.getIsRootLoad()) ? request.getIsRootLoad() : Optional.of(true));
    request.setIsManaged(exists(request.getIsManaged()) ? request.getIsManaged() : Optional.of(false));
    request.setDeferLoadInvoice(exists(request.getDeferLoadInvoice()) ? request.getDeferLoadInvoice() : Optional.of(false));
    request.setDefaultBillWeightUse(exists(request.getDefaultBillWeightUse()) ? request.getDefaultBillWeightUse() : Optional.of(UNLOAD_WEIGHT));
    request.setSendBookingConfirmation(exists(request.getSendBookingConfirmation()) ? request.getSendBookingConfirmation() : Optional.of(false));
    request.setSharedWithHiringCompany(Optional.of(false));
    request.setSendConfirmation(exists(request.getSendConfirmation()) ? request.getSendConfirmation() : Optional.of(false));

    request.setLoRateType(exists(request.getLoRateType()) ? request.getLoRateType() : Optional.of("2000"));
    request.setLoEstimatedWeight(exists(request.getLoEstimatedWeight()) ? request.getLoEstimatedWeight() : Optional.of(BigDecimal.valueOf(52000)));
    request.setLoEstimatedVolume(exists(request.getLoEstimatedVolume()) ? request.getLoEstimatedVolume() : Optional.of(BigDecimal.valueOf(5000)));

    final Optional<String> appName = UserUtil.getAppName();
    if (existsAndIsNotEmpty(appName) && MOBILE.equals(appName.get())) {
      request.setIsManaged(Optional.of(false));
      request.setDeferLoadInvoice(Optional.of(false));
      request.setSendBookingConfirmation(Optional.of(true));
      request.setSharedWithHiringCompany(Optional.of(true));
      if (existsAndIsFalse(request.getAutoInvoice())) {
        request.setDeferLoadInvoice(Optional.of(true));
      }
    }
  }

  //  request - data - entity
  private void enrichRequestWithUpdateDefaults(final LoadRequest request, final Load existingEntity) {

    request.setHiringAbCompanyId(exists(request.getHiringAbCompanyId()) ? request.getHiringAbCompanyId()
        : Optional.ofNullable(existingEntity.getHiringAbCompany()).map(AbCompany::getAbCompanyId));
    request.setIsManaged(exists(request.getIsManaged()) ? request.getIsManaged() : Optional.ofNullable(existingEntity.getIsManaged()));
    request.setIsBroker(exists(request.getIsBroker()) ? request.getIsBroker() : Optional.ofNullable(existingEntity.getIsBroker()));
    request.setIsRootLoad(exists(request.getIsRootLoad()) ? request.getIsRootLoad() : Optional.ofNullable(existingEntity.getIsRootLoad()));
    request.setIsRootLoad(exists(request.getIsRootLoad()) ? request.getIsRootLoad() : Optional.ofNullable(existingEntity.getIsRootLoad()));
    request.setDeferLoadInvoice(exists(request.getDeferLoadInvoice()) ? request.getDeferLoadInvoice() : Optional.of(true));
    request.setSendBookingConfirmation(exists(request.getSendBookingConfirmation()) ? request.getSendBookingConfirmation() : Optional.of(false));
    request.setSharedWithHiringCompany(exists(request.getSharedWithHiringCompany()) ? request.getSharedWithHiringCompany() : Optional.of(false));
    request.setSendConfirmation(exists(request.getSendConfirmation()) ? request.getSendConfirmation() : Optional.of(false));

    request.setLoRate(exists(request.getLoRate()) ? request.getLoRate() : Optional.ofNullable(existingEntity.getLoRate()));
    request.setLoRateType(exists(request.getLoRateType()) ? request.getLoRateType() : Optional.of(existingEntity.getLoRateType().getRateType()));
    request.setLoEstimatedWeight(exists(request.getLoEstimatedWeight()) ? request.getLoEstimatedWeight() : Optional.of(existingEntity.getLoEstimatedWeight()));
    request.setLoEstimatedVolume(exists(request.getLoEstimatedVolume()) ? request.getLoEstimatedVolume() : Optional.of(existingEntity.getLoEstimatedVolume()));
    request.setLoEstMiles(exists(request.getLoEstMiles()) ? request.getLoEstMiles() : Optional.of(existingEntity.getLoEstMiles()));
    request.setLoEstHours(exists(request.getLoEstHours()) ? request.getLoEstHours() : Optional.of(existingEntity.getLoEstHours()));

    final Optional<String> appName = UserUtil.getAppName();
    if (existsAndIsNotEmpty(appName) && MOBILE.equals(appName.get())) {
      request.setDeferLoadInvoice(Optional.of(false));
      request.setSendBookingConfirmation(Optional.of(true));
      if (existsAndIsFalse(request.getAutoInvoice())) {
        request.setDeferLoadInvoice(Optional.of(true));
      }
    }
  }

  private void doActivateLoad(final Load load) {
    load.setActive(true);
    load.setInactiveByUserId(null);
    load.setInactiveDate(null);
    load.setModifiedDate(Instant.now());

    load.registerDomainEvent(new LoadActivatedEvent(load.getLoadId()));
    loadRepository.save(load);
  }

  private void handleLoadAssignments(final Load rootLoad, final RerouteLoadRequest rerouteLoadDto, final AbCompany rerouteAbCompany) {
    for (RerouteLoadAssignment rerouteAssignmentDto : rerouteLoadDto.getAssignments()) {
      handleSingleLoadAssignment(rootLoad, rerouteLoadDto, rerouteAbCompany, rerouteAssignmentDto);
    }

    final LoadReroutedEvent event = new LoadReroutedEvent(rootLoad.getLoadId(), rootLoad.getReroutedAssignments());
    rootLoad.registerDomainEvent(event);
  }

  private void handleSingleLoadAssignment(final Load rootLoad,
                                          final RerouteLoadRequest rerouteLoadDto,
                                          final AbCompany rerouteAbCompany,
                                          final RerouteLoadAssignment rerouteAssignmentDto) {
    final Integer loadAssignmentId = rerouteAssignmentDto.getLoadAssignmentId();

    final Assignment loadAssignment = findLoadAssignmentById(rootLoad, loadAssignmentId);
    final Assignment booking = loadAssignment.getParentLoadAssignment();

    if (nonNull(booking)) {
      rerouteLoadAssignment(rootLoad, booking, rerouteAbCompany, rerouteLoadDto, rerouteAssignmentDto, false);
    }

    rerouteLoadAssignment(rootLoad, loadAssignment, rerouteAbCompany, rerouteLoadDto, rerouteAssignmentDto, true);
  }

  private void rerouteLoadAssignment(final Load rootLoad,
                                     final Assignment assignment,
                                     final AbCompany rerouteAbCompany,
                                     final RerouteLoadRequest rerouteLoadDto,
                                     final RerouteLoadAssignment rerouteAssignmentDto,
                                     final boolean recurse) {

    if (nonNull(assignment.getPreviousRate())) {
      if (isNull(rerouteAssignmentDto.getRerouteRate())) {
        throw new ValidationException(REROUTE_RATE, "Reroute rate is required.");
      }

      if (isNull(rerouteAssignmentDto.getRerouteRateType())) {
        throw new ValidationException(REROUTE_RATE_TYPE, "Reroute rate type is required.");
      }
    }

    final int userId = UserUtil.getUserIdOrThrow();

    final Instant now = Instant.now();

    assignment.setRerouteDate(now);
    assignment.setModifiedDate(now);
    assignment.setRerouteByUserId(userId);
    assignment.setRerouteReason(rerouteLoadDto.getRerouteReason());
    assignment.setReroutePickupDrop(rerouteLoadDto.getReroutePickupDrop());
    Optional.ofNullable(rerouteLoadDto.getRerouteContractId()).ifPresent(assignment::setRerouteContractId);
    assignment.setRerouteContractNumber(rerouteLoadDto.getRerouteContractNumber());

    if (!assignment.getIsRerouted() && !UNASSIGNED.equals(assignment.getAssignmentStatus())) {
      Optional.ofNullable(assignment.getRate()).ifPresent(assignment::setPreviousRate);
      assignment.setRateType(rateTypeRepository.findById(assignment.getRateType().getRateType()).orElse(findRateTypeById(TWO_K)));
      assignment.setPreviousLoadAssignmentNumber(assignment.getLoadAssignmentNumber());
      assignment.setPreviousPickupNumber(assignment.getPickupNumber());
      assignment.setPreviousDropNumber(assignment.getDropNumber());
      assignment.setPreviousWorkOrderNumber(assignment.getWorkOrderNumber());
      Optional.ofNullable(assignment.getBillSubtotal()).ifPresent(assignment::setPreviousBillSubtotal);

      if (isNull(assignment.getPreviousBillSurcharges()) && nonNull(assignment.getBillSurcharges())) {
        assignment.setPreviousBillSurcharges(assignment.getBillSurcharges());
      }

      if (isNull(assignment.getPreviousBillTotal()) && nonNull(assignment.getBillTotal())) {
        assignment.setPreviousBillTotal(assignment.getBillTotal());
      }

      if (isNull(assignment.getPreviousBillRatePerMile()) && nonNull(assignment.getBillRatePerMile())) {
        assignment.setPreviousBillRatePerMile(assignment.getBillRatePerMile());
      }
    }

    assignment.setIsRerouted(true);
    if (nonNull(rerouteAssignmentDto.getRerouteRate())) {
      assignment.setRate(rerouteAssignmentDto.getRerouteRate());
      assignment.setRateType(findRateTypeById(rerouteAssignmentDto.getRerouteRateType()));
    } else {
      assignment.setRateType(findRateTypeById(TWO_K));
    }

    assignment.setLoadAssignmentNumber(rerouteAssignmentDto.getRerouteLoadAssignmentNumber());
    assignment.setPickupNumber(rerouteAssignmentDto.getReroutePickupNumber());
    assignment.setDropNumber(rerouteAssignmentDto.getRerouteDropNumber());
    assignment.setWorkOrderNumber(rerouteAssignmentDto.getRerouteWorkOrderNumber());

    if (nonNull(assignment.getUser())) {
      final User user = assignment.getUser();
      final AbCompany abCompanyReplicate = abCompanyDomainService.replicate(user, rerouteAbCompany);
      assignment.setRerouteAbCompany(abCompanyReplicate);
      //TODO handle websocket update
    }

    if (nonNull(assignment.getToUser())) {
      final User toUser = assignment.getToUser();
      final AbCompany toAbCompanyReplicate = abCompanyDomainService.replicate(toUser, rerouteAbCompany);
      assignment.setRerouteToAbCompany(toAbCompanyReplicate);
      //TODO handle websocket update
    }

    calculateAssignmentFields(assignment);

    rootLoad.addReroutedAssignment(assignment.getLoadAssignmentId());
    //TODO handle event registration
    //registerEvent(rootLoad, assignment);

    //recurse
    if (recurse) {
      final Assignment child = assignment.getChildLoadAssignment();

      if (nonNull(child)) {
        final RerouteLoadAssignment childRerouteLoadAssignmentDto = rerouteAssignmentDto.toBuilder()
            .rerouteRate(child.getRate())
            .rerouteRateType(child.getRateType().getRateType())
            .build();

        rerouteLoadAssignment(rootLoad, child, rerouteAbCompany, rerouteLoadDto, childRerouteLoadAssignmentDto, true);
      }
    }
  }

  public void calculateAssignmentFields(final Assignment assignment) {

    final AbCompany rerouteAbCompany = assignment.getRerouteAbCompany();

    if (nonNull(rerouteAbCompany)) {
      Load load = assignment.getLoad();
      if (load == null) {
        load = assignment.getToLoad();
      }

      final AbCompany pickup;
      final AbCompany drop;
      if (PICKUP.equals(assignment.getReroutePickupDrop())) {
        pickup = assignment.getRerouteAbCompany();
        drop = load.getDropAbCompany();
      } else {
        pickup = load.getPickupAbCompany();
        drop = assignment.getRerouteAbCompany();
      }

      final Location pickupLocation = buildLocation(pickup);
      final Location dropLocation = buildLocation(drop);

      final Optional<RouteDto> routeOpt = routeService.findRoute(pickupLocation, dropLocation);

      if (!UNASSIGNED.equals(assignment.getAssignmentStatus())) {
        if (routeOpt.isPresent()) {
          final RouteDto route = routeOpt.get();
          assignment.setEstMiles(route.getMiles());
          assignment.setBillMiles(route.getMiles());
          if (route.getDuration() != null && route.getDuration() > 0) {
            assignment.setEstHours(BigDecimal.valueOf(Math.round(route.getDuration() / 60.0 / 60 * 10) / 10.0));
            assignment.setBillHours(BigDecimal.valueOf(Math.round(route.getDuration() / 60.0 / 60 * 10) / 10.0));
          } else {
            assignment.setEstHours(null);
            assignment.setBillHours(null);
          }
        } else {
          assignment.setEstMiles(null);
          assignment.setBillMiles(null);
          assignment.setEstHours(null);
          assignment.setBillHours(null);
        }
      }
    }

    final Map<String, String> errors = new HashMap<>();

    if (!UNASSIGNED.equals(assignment.getAssignmentStatus())) {
      if ((nonNull(assignment.getEstWeight()) || nonNull(assignment.getEstMiles()) || nonNull(assignment.getEstHours()))) {
        assignment.setEstQuantity(null);
        if (isNull(assignment.getRate())) {
          if (assignment.getIsIntraCompany() || assignment.getIsDriver()) {
            // driver/internal assignments don't require a rate
          } else {
            errors.put(RATE, "Enter the rate");
          }
        } else if (assignment.getRate().compareTo(BigDecimal.ZERO) < 0 || assignment.getRate().compareTo(BigDecimal.valueOf(31000)) > 0) {
          errors.put(RATE, "Enter a valid number");
        }

        if (isParsable(assignment.getRateType().getRateType())) {
          if (isNull(assignment.getEstWeight()) || assignment.getEstWeight() < 0) {
            errors.put(EST_WEIGHT, "Enter a valid number");
          } else {
            assignment.setEstQuantity(BigDecimal.valueOf(assignment.getEstWeight() / Double.parseDouble(assignment.getRateType().getRateType())));
          }
        } else if (assignment.getRateType().getRateType().equals(MILE)) {
          if (isNull(assignment.getEstMiles()) || assignment.getEstMiles().compareTo(BigDecimal.ZERO) < 0) {
            errors.put(EST_MILES, "Enter a valid number");
          } else {
            assignment.setEstQuantity(assignment.getEstMiles());
          }
        } else if (assignment.getRateType().getRateType().equals(HOUR)) {
          if (assignment.getEstHours() == null || assignment.getEstHours().compareTo(BigDecimal.ZERO) < 0) {
            errors.put(EST_HOURS, "Enter a valid number");
          } else {
            assignment.setEstQuantity(assignment.getEstHours());
          }
        } else if (assignment.getRateType().getRateType().equals(FLAT)) {
          assignment.setEstQuantity(BigDecimal.ONE);
        } else {
          errors.put(RATE_TYPE, "Unknown rate type");
        }

        if (isNull(assignment.getEstWeight()) || assignment.getEstWeight() <= 0) {
          assignment.setEstWeight(52000d);
        }

        if (nonNull(assignment.getEstQuantity()) && nonNull(assignment.getRate())) {
          assignment.setEstSubtotal(BigDecimal.valueOf(Math.round(100.0 * assignment.getRate().doubleValue()
              * assignment.getEstQuantity().doubleValue()) / 100.0));
          calculateEstSurcharges(assignment);
          assignment.setEstTotal(BigDecimal.valueOf(Math.round(assignment.getEstSubtotal().doubleValue() * 100)
              + Math.round(assignment.getEstSurcharges().doubleValue() * 100) / 100.0));
          assignment.setEstRatePerMile(null);
          if (nonNull(assignment.getEstMiles()) && assignment.getEstMiles().compareTo(BigDecimal.ZERO) > 0) {
            assignment.setEstRatePerMile(BigDecimal.valueOf(Math.round(100.0 * assignment.getEstSubtotal().doubleValue()
                / assignment.getEstMiles().doubleValue()) / 100.0));
          }
        }
      }

      if (isNull(assignment.getBillMiles()) && nonNull(assignment.getEstMiles())) {
        assignment.setBillMiles(assignment.getEstMiles());
      }

      if (isNull(assignment.getBillMiles()) && nonNull(assignment.getEstMiles())) {
        assignment.setBillMiles(assignment.getEstMiles());
      }

      if (nonNull(assignment.getBillMiles()) || nonNull(assignment.getBillHours())) {
        assignment.setBillQuantity(null);
        if (isParsable(assignment.getRateType().getRateType())) {
          if (assignment.getBillWeightUse().equals(LOADED_WEIGHT)) {
            if (nonNull(assignment.getLoadedWeight()) && assignment.getLoadedWeight() > 0) {
              assignment.setBillWeight(assignment.getLoadedWeight());
              assignment.setBillQuantity(BigDecimal.valueOf(assignment.getBillWeight() / Double.parseDouble(assignment.getRateType().getRateType())));
            }
          } else {
            if (nonNull(assignment.getUnloadWeight()) && assignment.getUnloadWeight() > 0) {
              assignment.setBillWeight(assignment.getUnloadWeight());
              assignment.setBillQuantity(BigDecimal.valueOf(assignment.getBillWeight() / Double.parseDouble(assignment.getRateType().getRateType())));
            }
          }
        } else if (assignment.getRateType().getRateType().equals(MILE)) {
          if (nonNull(assignment.getBillMiles()) && assignment.getBillMiles().compareTo(BigDecimal.ZERO) > 0) {
            assignment.setBillQuantity(assignment.getBillMiles());
          }
        } else if (assignment.getRateType().getRateType().equals(HOUR)) {
          if (nonNull(assignment.getBillHours()) && assignment.getBillHours().compareTo(BigDecimal.ZERO) > 0) {
            assignment.setBillQuantity(assignment.getBillHours());
          }
        } else if (assignment.getRateType().getRateType().equals(FLAT)) {
          assignment.setBillQuantity(BigDecimal.ONE);
        } else {
          errors.put(RATE_TYPE, "Unknown rate type");
          return;
        }

        if (nonNull(assignment.getBillQuantity()) && nonNull(assignment.getRate())) {
          assignment.setBillSubtotal(BigDecimal.valueOf(Math.round(100.0 * assignment.getRate().doubleValue()
              * assignment.getBillQuantity().doubleValue()) / 100.0));
          assignment.setBillSurcharges(BigDecimal.ZERO);
          calculateBillSurcharges(assignment);
          assignment.setBillTotal(BigDecimal.valueOf((Math.round(assignment.getBillSubtotal().doubleValue() * 100)
              + Math.round(assignment.getBillSurcharges().doubleValue() * 100)) / 100.0));
          assignment.setBillRatePerMile(null);
          if (nonNull(assignment.getBillMiles()) && assignment.getBillMiles().compareTo(BigDecimal.ZERO) > 0) {
            assignment.setBillRatePerMile(BigDecimal.valueOf(Math.round(100.0 * assignment.getBillSubtotal().doubleValue()
                / assignment.getBillMiles().doubleValue()) / 100.0));
          }
        }
      }
    }

    if (!errors.isEmpty()) {
      throw new BulkloadsException("Could not calculate assignment fields because of the following errors:" + errors);
    }
  }

  public void calculateEstSurcharges(final Assignment assignment) {
    List<AssignmentSurcharge> assignmentSurcharges = assignment.getSurcharges();
    double cents = 0;

    for (AssignmentSurcharge assignmentSurcharge : assignmentSurcharges) {
      if (Boolean.TRUE.equals(assignmentSurcharge.getSurchargeType().getIsPercentage())) {
        cents += Math.round(Math.round(assignment.getEstSubtotal().doubleValue() * 100) * (assignmentSurcharge.getSurcharge().doubleValue() / 100.0));
      } else if (Boolean.TRUE.equals(assignmentSurcharge.getSurchargeType().getIsPerMile())) {
        if (assignment.getEstMiles() != null) {
          cents += Math.round(assignment.getEstMiles().doubleValue()) * Math.round(assignmentSurcharge.getSurcharge().doubleValue() * 100);
        }
      } else {
        cents += Math.round(assignmentSurcharge.getSurcharge().doubleValue() * 100);
      }
    }

    assignment.setEstSurcharges(BigDecimal.valueOf(cents / 100.0));
  }

  public void calculateBillSurcharges(final Assignment assignment) {
    List<AssignmentSurcharge> assignmentSurcharges = assignment.getSurcharges();
    double cents = 0;

    for (AssignmentSurcharge assignmentSurcharge : assignmentSurcharges) {
      if (Boolean.TRUE.equals(assignmentSurcharge.getSurchargeType().getIsPercentage())) {
        cents += Math.round(Math.round(assignment.getBillSubtotal().doubleValue() * 100) * (assignmentSurcharge.getSurcharge().doubleValue() / 100.0));
      } else if (Boolean.TRUE.equals(assignmentSurcharge.getSurchargeType().getIsPerMile())) {
        if (assignment.getBillMiles() != null) {
          cents += Math.round(assignment.getBillMiles().doubleValue()) * Math.round(assignmentSurcharge.getSurcharge().doubleValue() * 100);
        }
      } else {
        cents += Math.round(assignmentSurcharge.getSurcharge().doubleValue() * 100);
      }
    }

    assignment.setBillSurcharges(BigDecimal.valueOf(cents / 100.0));
  }

  private Location buildLocation(final AbCompany pickup) {
    return Location.builder()
        .address(pickup.getAddress())
        .city(pickup.getCity())
        .state(pickup.getState())
        .zip(pickup.getZip())
        .coordinates(Coordinates.builder().latitude(pickup.getLatitude()).longitude(pickup.getLongitude()).build())
        .build();
  }

  private void validateRerouteContract(final RerouteLoadRequest dto, final Map<String, String> errors) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final Optional<Contract> rerouteContract;
    final Integer rerouteContractId = dto.getRerouteContractId();
    if (nonNull(rerouteContractId)) {
      rerouteContract = contractRepository.findByContractIdAndUserCompanyUserCompanyId(rerouteContractId, userCompanyId);
      if (rerouteContract.isEmpty()) {
        errors.put(REROUTE_CONTRACT_ID, "The contract was not found.");
      }
    }
  }

  private Assignment findLoadAssignmentById(final Load load, final Integer loadAssignmentId) {
    return load.getAssignments().stream()
        .filter(it -> it.getLoadAssignmentId().equals(loadAssignmentId))
        .findFirst()
        .orElseThrow(() -> new ValidationException(LOAD_ASSIGNMENT_ID, "No assignment found with that load_assignment_id"));
  }

  private Load findLoadById(final int loadId) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return loadRepository.findByLoadIdAndUserCompanyUserCompanyId(loadId, userCompanyId)
        .orElseThrow(() -> new ValidationException(LOAD_ID, "You are not allowed to update this load"));
  }

  private Optional<AbCompany> findRerouteAbCompany(final RerouteLoadRequest dto,
                                                   final Map<String, String> errors) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    Optional<AbCompany> rerouteAbCompany = Optional.empty();
    final Integer rerouteAbCompanyId = dto.getRerouteAbCompanyId();
    if (isNull(rerouteAbCompanyId)) {
      if (dto.getReroutePickupDrop().equals(PICKUP)) {
        errors.put(REROUTE_AB_COMPANY_ID, "The new origin company is required.");
      } else {
        errors.put(REROUTE_AB_COMPANY_ID, "The new destination company is required.");
      }
    } else {
      rerouteAbCompany =
          abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(rerouteAbCompanyId, userCompanyId);
      if (rerouteAbCompany.isEmpty()) {
        errors.put(REROUTE_AB_COMPANY_ID, "The company was not found in your address book.");
      }
    }
    return rerouteAbCompany;
  }

  private RateType findRateTypeById(final String rateTypeId) {
    return rateTypeRepository.findById(rateTypeId)
        .orElseThrow(() -> new ValidationException(RATE_TYPE, "Could not find id %s".formatted(rateTypeId)));
  }
}
