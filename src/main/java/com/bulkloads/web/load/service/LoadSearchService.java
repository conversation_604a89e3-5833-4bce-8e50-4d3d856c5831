package com.bulkloads.web.load.service;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.web.load.domain.LoadSearchDomainService;
import com.bulkloads.web.load.domain.entity.LoadSearch;
import com.bulkloads.web.load.mapper.LoadSearchMapper;
import com.bulkloads.web.load.repository.LoadSearchRepository;
import com.bulkloads.web.load.service.dto.LoadSearchRequest;
import com.bulkloads.web.load.service.dto.LoadSearchResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadSearchService {

  private final LoadSearchDomainService loadSearchDomainService;
  private final LoadSearchRepository loadSearchRepository;
  private final LoadSearchMapper loadSearchMapper;

  public List<LoadSearchResponse> getLoadsSearches(
      Integer searchType,
      Integer limit
  ) {
    int userId = UserUtil.getUserIdOrThrow();
    return loadSearchRepository.getLoadSearches(
        userId,
        1,
        searchType,
        limit
    );
  }

  public boolean create(final LoadSearchRequest request) {

    final LoadSearchData loadSearchData = loadSearchMapper.requestToData(request);
    final Result<LoadSearch> loadSearchResult = loadSearchDomainService.create(loadSearchData);
    LoadSearch entity = loadSearchResult.orElseThrow();

    loadSearchRepository.save(entity);

    return true;
  }


}
