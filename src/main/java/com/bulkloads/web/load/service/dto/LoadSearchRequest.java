package com.bulkloads.web.load.service.dto;

import java.time.LocalDate;
import java.util.List;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class LoadSearchRequest {
  Integer loadId;
  List<Integer> loadIds;
  Integer userId;
  List<Integer> userCompanyIds;
  String originCountry;
  String originState;
  String originCity;
  String originZip;
  Double originLat;
  Double originLong;
  Double originRadius;
  String destinationCountry;
  String destinationState;
  String destinationCity;
  String destinationZip;
  Double destinationLat;
  Double destinationLong;
  Double destinationRadius;
  String equipmentIds;
  Integer days;
  String rateProductCategory;
  LocalDate shipFrom;
  LocalDate shipTo;
  boolean onlyFavoriteLoads;
  boolean onlyOfferedLoads;
  boolean onlyPastOfferedLoads;
  boolean onlyFavoriteLanes;
  boolean onlyFavoriteCompanies;
  boolean excludeMyCompanyLoads;


}
