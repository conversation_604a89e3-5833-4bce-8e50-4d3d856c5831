package com.bulkloads.web.load.service;


import java.time.Instant;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class LoadSearchData {
  Integer userId;
  Integer siteId = 1;
  Integer searchType;
  Instant date;
  String originCountry = "US";
  String originState = "";
  String originCity = "";
  String originZip = "";
  Double originLat;
  Double originLong;
  Integer originRadius;
  String destinationCountry = "US";
  String destinationState = "";
  String destinationCity = "";
  String destinationZip = "";
  Double destinationLat;
  Double destinationInteger;
  Integer destinationRadius;
  String equipmentIds = "";
  Integer days;
  boolean onlyFavoriteLoads = false;
  boolean onlyOfferedLoads = false;
  boolean onlyFavoriteLanes = false;
  boolean onlyFavoriteCompanies = false;
  String flTruckSize = "";
  Integer flNumOfStops;
  String userCompanyIds = "";
}
