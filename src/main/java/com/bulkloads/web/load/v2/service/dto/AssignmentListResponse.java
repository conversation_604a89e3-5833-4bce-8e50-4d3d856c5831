package com.bulkloads.web.load.v2.service.dto;

import java.time.Instant;
import lombok.Data;

@Data
public class AssignmentListResponse {

  private String bolNumber;
  private String confirmationConfirmedDate;
  private String confirmationOpenedDate;
  private String confirmationSentDate;
  private Boolean numberOfFiles;
  private String parentNumberOfFiles;
  private String isRerouted;
  private String reroutePickupDrop;
  private String dropNumber;
  private String rerouteAbCompanyId;
  private String rerouteToAbCompanyId;
  private Double estimatedMiles;
  private Double estRatePerMile;
  private Instant hauledDate;
  private String toFirstName;
  private String toLastName;
  private String parentReadyToInvoice;
  private String parentInvoiced;
  private Integer loadAssignmentNumber;
  private Double loadedWeight;
  private String loadedVolume;
  private String loadingTicketNumber;
  private Integer loadInvoiceId;
  private String loCommodity;
  private String loContractNumber;
  private String parentHiringCompanyName;
  private String billQuantity;
  private String rerouteLocation;
  private String pickupNumber;
}