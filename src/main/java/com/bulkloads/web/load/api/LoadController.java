package com.bulkloads.web.load.api;

import static com.bulkloads.config.AppConstants.UserPermission.CREATE_LOAD;
import static com.bulkloads.config.AppConstants.UserPermission.UPDATE_LOAD;

import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.load.api.dto.LoadApiResponse;
import com.bulkloads.web.load.service.LoadService;
import com.bulkloads.web.load.service.dto.CreateUpdateLoadResponse;
import com.bulkloads.web.load.service.dto.LoadRequest;
import com.bulkloads.web.load.service.dto.RerouteLoadRequest;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Validated
@RequestMapping("/rest/loads")
@Tag(name = "Loads (Shipper)")
@RequiredArgsConstructor
public class LoadController {

  private final LoadService loadService;

  @PreAuthorize("hasAuthority('" + CREATE_LOAD + "')")
  @Operation(summary = "Create Load")
  @PostMapping
  public LoadApiResponse createLoad(@RequestBody final LoadRequest request) {
    final CreateUpdateLoadResponse loadResponse = loadService.createLoad(request);

    return LoadApiResponse.builder()
        .message("Load created")
        .loadAssignmentIds(loadResponse.loadAssignmentIds())
        .loadId(loadResponse.loadId())
        .build();
  }

  @PreAuthorize("hasAuthority('" + UPDATE_LOAD + "')")
  @Operation(summary = "Update Load")
  @PutMapping("/{load_id}")
  public LoadApiResponse updateLoad(
      @PathVariable("load_id")
      @Positive(message = "Load id should be positive") final int loadId,
      @RequestBody final LoadRequest request) {
    final CreateUpdateLoadResponse createLoadResponse = loadService.updateLoad(loadId, request);

    return LoadApiResponse.builder()
        .message("Load Updated")
        .loadAssignmentIds(createLoadResponse.loadAssignmentIds())
        .loadId(createLoadResponse.loadId())
        .build();
  }

  @Operation(summary = "Reroute Load")
  @PostMapping("/{load_id}/reroute")
  public ApiResponse<Void, Void> rerouteLoad(
      @Parameter(name = "load_id", required = true, in = ParameterIn.PATH)
      @PathVariable("load_id")
      @Positive(message = "Load id should be positive") int loadId,
      @RequestBody RerouteLoadRequest dto) {

    loadService.rerouteLoad(loadId, dto);

    return ApiResponse.<Void, Void>builder()
        .message("Load Rerouted")
        .build();
  }

  @Operation(summary = "Activate a Load")
  @PostMapping("/{load_id}/activate")
  public ApiResponse<Void, Void> activateLoad(
      @Parameter(name = "load_id", required = true, in = ParameterIn.PATH)
      @PathVariable("load_id")
      @Positive(message = "Load id should be positive") int loadId) {

    loadService.activateUserLoad(loadId);

    return ApiResponse.<Void, Void>builder()
        .message("Shipment re-opened")
        .build();
  }
}
