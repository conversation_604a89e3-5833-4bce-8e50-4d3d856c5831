package com.bulkloads.web.load.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.load.service.LoadFavoriteService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Validated
@RequestMapping("/rest/loads")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class LoadFavoriteController {

  private final LoadFavoriteService loadFavoriteService;

  @Tag(name = "Loads (Carrier)")
  @Operation(summary = "Set a load as favorite")
  @PostMapping("/{load_id}/set_favorite")
    public ApiResponse<Void, Void> setFavoriteLoad(
        @Parameter(name = "load_id", required = true, in = ParameterIn.PATH)
        @PathVariable("load_id") final int loadId) {
    loadFavoriteService.setLoadFavorite(loadId);
    return ApiResponse.<Void, Void>builder()
        .message("Load set as favorite")
        .build();
  }

  @Tag(name = "Loads (Carrier)")
  @Operation(summary = "Remove a load from favorites")
  @PostMapping("/{load_id}/remove_favorite")
  public ApiResponse<Void, Void> removeFavoriteLoad(
      @Parameter(name = "load_id", required = true, in = ParameterIn.PATH)
      @PathVariable("load_id") final int loadId) {
    loadFavoriteService.removeLoadFavorite(loadId);
    return ApiResponse.<Void, Void>builder()
        .message("Load removed from favorites")
        .build();
  }

}
