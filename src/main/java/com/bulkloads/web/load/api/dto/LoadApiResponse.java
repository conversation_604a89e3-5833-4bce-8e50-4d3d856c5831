package com.bulkloads.web.load.api.dto;

import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class LoadApiResponse {

  @Schema(description = "Load id.", requiredMode = Schema.RequiredMode.REQUIRED)
  int loadId;
  @JsonSerialize(using = CsvSerializer.class)
  @Schema(description = "Load assignment ids.", requiredMode = Schema.RequiredMode.REQUIRED)
  List<Integer> loadAssignmentIds;
  @Schema(description = "Message.", requiredMode = Schema.RequiredMode.REQUIRED)
  String message;
}
