package com.bulkloads.web.load.event;

import static com.bulkloads.config.AppConstants.LoadAction.LOAD_REROUTE;
import java.util.List;
import lombok.Getter;

@Getter
public class LoadReroutedEvent extends LoadEvent {

  private final List<Integer> reroutedAssignmentIds;

  public LoadReroutedEvent(final int loadId, final List<Integer> reroutedAssignmentIds) {
    super(loadId, LOAD_REROUTE);
    this.reroutedAssignmentIds = reroutedAssignmentIds;
  }
}
