package com.bulkloads.web.file.api;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.beans.PropertyEditorSupport;
import java.util.Optional;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.file.service.FileService;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.UploadFileRequest;
import org.springframework.http.MediaType;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.InitBinder;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping(path = "/rest/files")
@Tag(name = "Files")
@RequiredArgsConstructor
public class FileController {

  private final FileService fileService;

  @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public FileResponse create(@ModelAttribute @Valid UploadFileRequest fileRequest) {
    if (fileRequest.getFile().isEmpty()) {
      throw new ValidationException("file", "File must not be null or empty.");
    }

    boolean isUserFile = existsAndIsNotEmpty(fileRequest.getS3Dir());

    if (isUserFile) {
      return fileService.createFromUploadedFile(fileRequest);
    } else {
      return fileService.uploadToTemp(fileRequest);
    }
  }

  @InitBinder
  public void initBinder(WebDataBinder binder) {
    binder.registerCustomEditor(MultipartFile.class, new PropertyEditorSupport() {
      @Override
      public void setAsText(String text) {
        if (text == null || text.trim().isEmpty()) {
          setValue(null);
        } else {
          setValue(null);
        }
      }
    });

    binder.registerCustomEditor(Optional.class, "fileTypeId", new PropertyEditorSupport() {
      @Override
      public void setAsText(String text) {
        if (text == null || text.isEmpty() || "null".equalsIgnoreCase(text)) {
          setValue(Optional.empty());
        } else {
          try {
            setValue(Optional.of(Integer.valueOf(text)));
          } catch (NumberFormatException e) {
            setValue(Optional.empty());
          }
        }
      }
    });

  }

}
