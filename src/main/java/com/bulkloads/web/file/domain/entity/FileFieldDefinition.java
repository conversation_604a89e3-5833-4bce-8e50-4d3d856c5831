package com.bulkloads.web.file.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "file_field_definitions")
@Getter
@Setter
public class FileFieldDefinition {

  @Id
  @Column(name = "field_source_name", nullable = false)
  private String fieldSourceName;

  @Column(name = "field_name", nullable = false)
  private String fieldName;

  @Column(name = "field_type", nullable = false)
  private String fieldType = "string";

  @Column(name = "field_label", nullable = false)
  private String fieldLabel = "";

  @Column(name = "field_description", nullable = false)
  private String fieldDescription = "";

  @Column(name = "field_order", nullable = false)
  private Integer fieldOrder = 999;

  @Column(name = "date")
  private Instant date;

  @Column(name = "grade_id")
  private Integer gradeId;
}