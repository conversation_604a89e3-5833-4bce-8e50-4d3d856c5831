package com.bulkloads.web.file.repository;

import java.util.Optional;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface FileFieldDefinitionRepository extends JpaRepository<FileFieldDefinition, String> {
  
  /**
   * Find a field definition by grade ID
   * @param gradeId The grade ID
   * @return Optional containing the field definition if found
   */
  Optional<FileFieldDefinition> findByGradeId(Integer gradeId);
}