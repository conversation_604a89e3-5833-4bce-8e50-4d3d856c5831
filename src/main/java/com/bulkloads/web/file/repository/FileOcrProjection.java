package com.bulkloads.web.file.repository;

import java.time.Instant;
import org.springframework.beans.factory.annotation.Value;

public interface FileOcrProjection {
  Integer getFileId();

  String getFileUrl();

  String getThumbUrl();

  String getMimeType();

  @Value("#{target.isImage == 1}")
  Boolean getIsImage();

  @Value("#{target.isAudio == 1}")
  Boolean getIsAudio();

  Long getSize();

  String getExtension();

  String getFilename();

  Integer getNumberOfPages();

  String getCaption();

  String getDispatchingCompanyName();

  String getDriverCompanyName();

  String getDriverFirstName();

  String getDriverLastName();

  String getDriverPhone();

  String getCommodity();

  String getOriginFacilityName();

  String getDestinationFacilityName();

  Instant getUploadDate();
}