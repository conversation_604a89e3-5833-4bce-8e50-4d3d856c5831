package com.bulkloads.web.file.util;

import static com.bulkloads.web.file.util.MimeTypes.MIME_TYPE_PDF;
import java.nio.file.Files;
import java.nio.file.Path;
import org.apache.pdfbox.pdmodel.PDDocument;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@UtilityClass
public class PdfUtils {

  public static int getNumberOfPages(Path filePath) {
    try {
      try (PDDocument document = PDDocument.load(filePath.toFile())) {
        return document.getNumberOfPages();
      }
    } catch (Exception e) {
      return 1;
    }
  }

  public static boolean isPdf(Path userFile) {
    try {
      String mimeType = Files.probeContentType(userFile);
      if (mimeType == null) {
        return false;
      }
      return MIME_TYPE_PDF.equals(mimeType);
    } catch (Exception e) {
      return false;
    }
  }

}
