package com.bulkloads.web.file.handler;

import com.bulkloads.web.file.event.FileCreatedEvent;
import com.bulkloads.web.file.service.SplitPdfService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class FileEventHandler {

  private final SplitPdfService splitPdfService;

  @TransactionalEventListener(
      classes = FileCreatedEvent.class,
      phase = TransactionPhase.AFTER_COMMIT)
  public void handleAssignmentUpdate(final FileCreatedEvent event) {
    log.info("HANDLING FILE CREATED EVENT {}", event);

    // Actions: splitPdf Pages

  }

}
