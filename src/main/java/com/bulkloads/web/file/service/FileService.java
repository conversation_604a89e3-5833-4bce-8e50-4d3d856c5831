package com.bulkloads.web.file.service;

import static com.bulkloads.common.UserUtil.getUserCompanyIdOrThrow;
import static com.bulkloads.common.UserUtil.getUserIdOrThrow;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.web.aws.service.AmazonS3Service.UPLOAD_TEMP;
import static com.bulkloads.web.aws.service.AmazonS3Service.USER_FILES;
import static com.bulkloads.web.file.util.FileUtils.deleteFileIfExists;
import static com.bulkloads.web.file.util.FileUtils.saveMultipartFileToDisk;
import static com.bulkloads.web.file.util.ImageUtils.generateThumbnailForFileByFileExtension;
import static com.bulkloads.web.file.util.ImageUtils.generateThumbnailFromImage;
import static com.bulkloads.web.file.util.ImageUtils.isImage;
import static com.bulkloads.web.file.util.ImageUtils.validateThumbnail;
import static com.bulkloads.web.file.util.ImageUtils.validateUserImage;
import static com.bulkloads.web.file.util.PdfUtils.isPdf;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.aws.service.AmazonS3Service;
import com.bulkloads.web.file.domain.FileDomainService;
import com.bulkloads.web.file.domain.data.FileData;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileType;
import com.bulkloads.web.file.domain.entity.UserFile;
import com.bulkloads.web.file.mapper.FileMapper;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.repository.FileTypeRepository;
import com.bulkloads.web.file.service.dto.FileRequest;
import com.bulkloads.web.file.service.dto.FileResponse;
import com.bulkloads.web.file.service.dto.LocalFileRequest;
import com.bulkloads.web.file.service.dto.SplitPdfDto;
import com.bulkloads.web.file.service.dto.UploadFileRequest;
import com.bulkloads.web.file.util.FileUtils;
import com.bulkloads.web.file.util.MimeTypes;
import com.bulkloads.web.file.util.PdfUtils;
import com.bulkloads.web.infra.messaging.consumer.MessageQueueSender;
import org.apache.commons.io.FilenameUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class FileService {

  public static final String FILE_ID_PLACEHOLDER = "[file_id]";

  public static final int LOADING_TICKET_FILE_TYPE_ID = 1;
  public static final int UNLOADING_TICKET_FILE_TYPE_ID = 2;

  private final AmazonS3Service amazonS3Service;
  private final FileRepository fileRepository;
  private final FileTypeRepository fileTypeRepository;
  private final FileMapper fileMapper;
  private final FileDomainService fileDomainService;
  private final MessageQueueSender queueSender;
  private final AppProperties appProperties;

  public FileResponse createFromUploadedFile(UploadFileRequest uploadFileRequest) {
    try {
      final Path filePath = saveMultipartFileToDisk(uploadFileRequest.getFile());

      Path thumbPath = null;
      if (!isEmpty(uploadFileRequest.getThumb())) {
        thumbPath = saveMultipartFileToDisk(uploadFileRequest.getThumb());
      }
      FileData fileData = prepareFile(filePath, thumbPath, USER_FILES);
      if (uploadFileRequest.getFileTypeId().isPresent()) {
        FileType fileType = fileTypeRepository.findById(uploadFileRequest.getFileTypeId().orElseThrow())
            .orElseThrow(() -> new BulkloadsException("The file_type_id was not found. Enter a valid file type."));
        fileData.setFileType(Optional.of(fileType));
      }
      return createFile(fileData, true);

    } catch (IOException e) {

      throw new BulkloadsException("Failed to create user file", e);
    }
  }

  @Transactional
  public FileResponse createFromLocalFile(LocalFileRequest localFileRequest,
                                          boolean splitPdf,
                                          String s3FolderName) throws IOException {

    FileData data = prepareFile(
        localFileRequest.getLocalPath(),
        localFileRequest.getLocalPathThumb(),
        s3FolderName);

    final FileRequest fileRequest = FileRequest.builder()
        .fileUrl(data.getFileUrl().get())
        .thumbUrl(data.getThumbUrl().get())
        .mimeType(data.getMimeType().get())
        .size(data.getSize().get().intValue())
        .extension("pdf")
        .filename(data.getFilename().get())
        .numberOfPages(data.getNumberOfPages().get())
        .caption(Strings.EMPTY)
        .ocrProcessed(localFileRequest.getOcrProcessed())
        .fileTypeId(localFileRequest.getFileTypeId())
        .build();

    FileData fileData = fileMapper.fileRequestToData(fileRequest);
    return createFile(fileData, splitPdf);
  }

  public FileResponse createFile(FileRequest fileRequest, boolean splitPdf) {
    FileData fileData = fileMapper.fileRequestToData(fileRequest);
    return createFile(fileData, splitPdf);
  }

  private FileResponse createFile(FileData fileData, boolean splitPdf) {
    Result<File> result = fileDomainService.create(fileData);

    File fileEntity = result.orElseThrow();
    try {
      fileEntity = fileRepository.saveAndFlush(fileEntity);
    } catch (Exception e) {
      log.error("Error saving file", e);
      throw e;
    }
    fileEntity.setFileUrl(fileEntity.getFileUrl().replace(FILE_ID_PLACEHOLDER, String.valueOf(fileEntity.getFileId())));
    fileRepository.saveAndFlush(fileEntity);

    if (splitPdf && fileEntity.getMimeType().equalsIgnoreCase(MimeTypes.MIME_TYPE_PDF)) {
      emitSplitPdfEvent(fileEntity.getFileId());
    }

    return fileMapper.entityToResponse(fileEntity);
  }

  private void emitSplitPdfEvent(final int fileId) {
    log.debug("Emitting split pdf event for file id: {}", fileId);
    TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
      @Override
      public void afterCommit() {
        queueSender.send(
            appProperties.getSplitPdf().getQueueName(),
            SplitPdfDto.builder().fileId(fileId).build()
        );
      }
    });
  }

  public FileResponse uploadToTemp(UploadFileRequest uploadFileRequest) {
    try {
      final Path filePath = saveMultipartFileToDisk(uploadFileRequest.getFile());

      Path thumbPath = null;
      if (!isEmpty(uploadFileRequest.getThumb())) {
        thumbPath = saveMultipartFileToDisk(uploadFileRequest.getThumb());
      }

      FileData fileData = prepareFile(filePath, thumbPath, UPLOAD_TEMP);
      if (uploadFileRequest.getFileTypeId().isPresent()) {
        FileType fileType = fileTypeRepository.findById(uploadFileRequest.getFileTypeId().orElseThrow())
            .orElseThrow(() -> new BulkloadsException("The file_type_id was not found. Enter a valid file type."));
        fileData.setFileType(Optional.of(fileType));
      }
      return fileMapper.dataToResponse(fileData);

    } catch (IOException e) {
      throw new BulkloadsException("Failed to create user file", e);
    }
  }

  public boolean isFilesOwnershipValid(List<File> files, int newFilesSize) {
    List<UserFile> existingUserFiles = fileRepository.getUserFiles(
        files.stream().map(File::getFileId).toList(),
        getUserCompanyIdOrThrow());
    return existingUserFiles.size() == newFilesSize;
  }

  public FileData prepareFile(final Path providedUserFile, final Path providedThumbnail, final String s3FolderName) throws IOException {

    Objects.requireNonNull(s3FolderName);

    Path userFilePath = providedUserFile;

    if (isImage(userFilePath)) {
      userFilePath = validateUserImage(userFilePath);
    }
    if (isPdf(userFilePath)) {
      // TODO: Better throw a validation error if the extension does not match the file type (not for images)
    } else {
      // TODO: security check for non-image files and non pdf files
    }

    Path thumbPath;

    if (isEmpty(providedThumbnail)) {
      if (isImage(userFilePath)) {
        thumbPath = generateThumbnailFromImage(userFilePath);
      } else {
        thumbPath = generateThumbnailForFileByFileExtension(userFilePath);
      }
    } else {
      if (!isImage(providedThumbnail)) {
        throw new BulkloadsException("Thumbnail is not an image");
      }
      thumbPath = validateThumbnail(providedThumbnail);
    }

    String fileUrl = amazonS3Service.put(userFilePath, s3FolderName);
    String thumbUrl = amazonS3Service.put(thumbPath, s3FolderName + "/thumbs");

    FileData fileData = buildFileDataFromPath(userFilePath, fileUrl, thumbUrl);

    deleteFileIfExists(providedUserFile);
    deleteFileIfExists(userFilePath);
    deleteFileIfExists(thumbPath);
    deleteFileIfExists(providedThumbnail);
    return fileData;
  }

  public FileData prepareSingleFile(Path userFilePath, String s3FolderName) throws IOException {
    Objects.requireNonNull(s3FolderName);

    boolean isUserFileImage = isImage(userFilePath);

    // TODO: Better throw a validation error if the extension does not match the file type (not for images)

    if (isUserFileImage) {
      userFilePath = validateUserImage(userFilePath);
    }
    if (isPdf(userFilePath)) {
      // any validation to do for pdf files?
    } else {
      // do nothing
    }

    String fileUrl = amazonS3Service.put(userFilePath, s3FolderName);
    FileData fileData = buildFileDataFromPath(userFilePath, fileUrl, null);

    deleteFileIfExists(userFilePath);
    return fileData;
  }

  protected static FileData buildFileDataFromPath(Path path, String fileUrl, String thumbUrl) throws IOException {

    FileData fileData = new FileData();
    fileData.setSize(Optional.of(Files.size(path)));
    fileData.setFilename(Optional.of(path.getFileName().toString()));
    fileData.setExtension(Optional.of(FilenameUtils.getExtension(path.getFileName().toString())));
    String mimeType = Files.probeContentType(path);
    fileData.setMimeType(Optional.of(mimeType != null ? mimeType : "application/octet-stream"));
    fileData.setIsImage(Optional.of(isImage(path)));
    fileData.setIsAudio(Optional.of(FileUtils.isAudioFile(path)));
    fileData.setCaption(Optional.of(""));
    fileData.setNumberOfPages(Optional.of(PdfUtils.getNumberOfPages(path)));

    if (!isEmpty(fileUrl)) {
      fileData.setFileUrl(Optional.of(fileUrl));
    }
    if (!isEmpty(thumbUrl)) {
      fileData.setThumbUrl(Optional.of(thumbUrl));
    }

    return fileData;
  }

  public List<FileType> getFileTypes(Integer fileTypeId) {
    int companyId = UserUtil.getUserCompanyIdOrThrow();
    int userId = getUserIdOrThrow();
    return fileTypeRepository.findFileTypes(companyId, userId, fileTypeId);
  }

}
