package com.bulkloads.web.file.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.Paths.TEMP;
import static com.bulkloads.web.aws.service.AmazonS3Service.USER_FILES;
import static com.bulkloads.web.file.util.FileUtils.downloadFile;
import static com.bulkloads.web.file.util.ImageUtils.MAX_USER_IMAGE_HEIGHT;
import static com.bulkloads.web.file.util.ImageUtils.MAX_USER_IMAGE_WIDTH;
import static com.bulkloads.web.file.util.PdfUtils.getNumberOfPages;
import static com.bulkloads.web.file.util.PdfUtils.isPdf;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.file.domain.data.FileData;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FilePage;
import com.bulkloads.web.file.repository.FilePageRepository;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.util.FileUtils;
import com.bulkloads.web.file.util.ImageUtils;
import com.bulkloads.web.file.util.MimeTypes;
import org.apache.commons.io.FilenameUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional
public class SplitPdfService {

  private static final int PDF_PAGE_DPI = 150;
  private static final float PDF_PAGE_THUMBNAIL_QUALITY = 0.9f;

  private final FileService fileService;
  private final FileRepository fileRepository;
  private final FilePageRepository filePageRepository;

  static List<PdfPageAndImagePage> splitPdfOnDisk(Path pdfPath) throws IOException {

    if (!isPdf(pdfPath)) {
      return List.of();
    }
    int numberOfPages = getNumberOfPages(pdfPath);
    if (numberOfPages <= 1) {
      return List.of();
    }

    String filename = pdfPath.getFileName().toString();
    String basename = FilenameUtils.getBaseName(filename);
    String pdfExtension = FilenameUtils.getExtension(filename);

    if (isEmpty(pdfExtension) || !pdfExtension.equalsIgnoreCase("pdf")) {
      pdfExtension = "pdf";
    }

    PDDocument document = PDDocument.load(pdfPath.toFile());
    PDFRenderer pdfRenderer = new PDFRenderer(document);

    List<PdfPageAndImagePage> pages = new ArrayList<>(numberOfPages);

    for (int i = 0; i < document.getNumberOfPages(); i++) {

      String pdfPageFilename = basename + "-" + (i + 1) + "." + pdfExtension;
      String thumbnailPageFilename = basename + "-" + (i + 1) + ".jpeg";

      Path pagePath = TEMP.resolve(pdfPageFilename);

      PDDocument singlePageDocument = new PDDocument();
      singlePageDocument.addPage(document.getPage(i));
      singlePageDocument.save(pagePath.toFile());
      singlePageDocument.close();

      Path thumbPath = TEMP.resolve(thumbnailPageFilename);

      BufferedImage thumbnailImage = pdfRenderer.renderImageWithDPI(i, PDF_PAGE_DPI, ImageType.RGB);

      ImageUtils.resample(thumbnailImage,
                          "jpg",
                          PDF_PAGE_THUMBNAIL_QUALITY, MAX_USER_IMAGE_WIDTH, MAX_USER_IMAGE_HEIGHT,
                          thumbPath);

      pages.add(new PdfPageAndImagePage(pagePath, thumbPath));
    }
    document.close();
    return pages;
  }

  public void splitPdf(int fileId) throws IOException {

    File file = fileRepository.findById(fileId)
        .orElseThrow(() -> new BulkloadsException("Could not find file with id: " + fileId));

    if (!file.getMimeType().equalsIgnoreCase(MimeTypes.MIME_TYPE_PDF)) {
      throw new IllegalArgumentException("File is not a PDF");
    }
    String fileUrl = file.getFileUrl();
    Path localPath = downloadFile(fileUrl);

    try {
      List<PdfPageAndImagePage> processedFiles = splitPdfOnDisk(localPath);

      for (int i = 0; i < processedFiles.size(); i++) {
        PdfPageAndImagePage processedFile = processedFiles.get(i);

        FileData pdfPageData = fileService.prepareSingleFile(processedFile.getPdfPagePath(), USER_FILES);
        FileData imagePageData = fileService.prepareSingleFile(processedFile.getImagePagePath(), USER_FILES);

        FilePage pdfPage = new FilePage();
        pdfPage.setFileUrl(pdfPageData.getFileUrl().get());
        pdfPage.setThumbUrl(imagePageData.getFileUrl().get());
        pdfPage.setMimeType(MimeTypes.MIME_TYPE_PDF);
        pdfPage.setSize(pdfPageData.getSize().get());
        pdfPage.setIsAudio(false);
        pdfPage.setIsImage(false);
        pdfPage.setExtension("pdf");
        pdfPage.setPageNumber(i + 1);
        pdfPage.setFile(file);
        pdfPage.setFilename(pdfPageData.getFilename().get());
        filePageRepository.save(pdfPage);
      }
    } finally {
      FileUtils.deleteFileIfExists(localPath);
    }

  }
}
