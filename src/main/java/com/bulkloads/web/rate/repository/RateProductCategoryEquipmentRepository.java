package com.bulkloads.web.rate.repository;

import java.util.List;
import com.bulkloads.web.rate.domain.entity.RateProductCategoryEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface RateProductCategoryEquipmentRepository extends JpaRepository<RateProductCategoryEquipment, Integer> {

  List<RateProductCategoryEquipment> findAllByIdEquipmentIdIn(final List<String> equipmentIds);

}