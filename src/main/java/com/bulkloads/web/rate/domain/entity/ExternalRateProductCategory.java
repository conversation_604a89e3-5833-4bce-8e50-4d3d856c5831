package com.bulkloads.web.rate.domain.entity;

import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "external_rate_product_categories")
public class ExternalRateProductCategory {

  @EmbeddedId
  private ExternalRateProductCategoryId id;

  @MapsId("userCompanyId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "user_company_id", referencedColumnName = "user_company_id")
  private UserCompany userCompany;

  @Column(name = "rate_product_category_id")
  private Integer rateProductCategoryId;

  @Size(max = 45)
  @NotNull
  @Column(name = "external_rate_product_category")
  private String externalRateProductCategory;

  @Size(max = 45)
  @NotNull
  @Column(name = "external_rate_product_category_type")
  private String externalRateProductCategoryType = "";

}