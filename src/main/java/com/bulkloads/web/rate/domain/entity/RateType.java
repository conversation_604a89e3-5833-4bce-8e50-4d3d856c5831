package com.bulkloads.web.rate.domain.entity;

import static com.bulkloads.common.validation.ValidationUtils.equal;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Setter
@Getter
@Table(name = "rate_types")
public class RateType {

  @Id
  @Size(max = 10)
  @Column(name = "rate_type")
  private String rateType;

  @Size(max = 20)
  @NotNull
  @Column(name = "rate_type_text")
  private String rateTypeText = "";

  @Size(max = 20)
  @NotNull
  @Column(name = "rate_type_text_medium")
  private String rateTypeTextMedium = "";

  @Size(max = 20)
  @NotNull
  @Column(name = "rate_type_text_medium_plural")
  private String rateTypeTextMediumPlural = "";

  @Size(max = 20)
  @NotNull
  @Column(name = "rate_type_text_abbr")
  private String rateTypeTextAbbr = "";

  @NotNull
  @Column(name = "sort_order")
  private Integer sortOrder;

  @NotNull
  @Column(name = "is_weight")
  private Boolean isWeight = true;

  @Size(max = 20)
  @NotNull
  @Column(name = "rate_type_chs")
  private String rateTypeChs;

  public boolean is(String anotherRateType) {
    return equal(rateType, anotherRateType);
  }

  public boolean isWeightType() {
    return
        "100".equals(rateType)
            || "1000".equals(rateType)
            || "2000".equals(rateType)
            || "2204.62".equals(rateType)
            || "32".equals(rateType)
            || "45".equals(rateType)
            || "48".equals(rateType)
            || "50".equals(rateType)
            || "56".equals(rateType)
            || "60".equals(rateType);

  }

  public boolean isVolumeType() {
    return isGallonType() || isLiterType();
  }

  public boolean isFlatType() {
    return equal(rateType, "flat");
  }

  public boolean isGallonType() {
    return equal(rateType, "gallon");
  }

  public boolean isHourType() {
    return equal(rateType, "hour");
  }

  public boolean isLiterType() {
    return equal(rateType, "liter");
  }

  public boolean isMileType() {
    return equal(rateType, "isMile");
  }

}