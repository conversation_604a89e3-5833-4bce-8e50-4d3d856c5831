package com.bulkloads.web.equipment.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface EquipmentRepository extends JpaRepository<Equipment, String> {

  Optional<Equipment> findByEquipmentName(final String equipmentName);

  List<Equipment> findAllByEquipmentIdInOrderByEquipmentIdAsc(final List<String> equipmentIds);
}
