package com.bulkloads.web.assignment.event;

import static com.bulkloads.config.AppConstants.AssignmentAction.ASSIGNMENT_CREATE;
import java.util.List;
import java.util.Optional;
import lombok.Getter;

@Getter
public class AssignmentCreatedEvent extends AssignmentBookingEvent {

  private final Integer bookedFromOfferRecipientId;

  public AssignmentCreatedEvent(final List<Integer> loadAssignmentIds,
                                final Integer bookedFromOfferRecipientId) {
    super(loadAssignmentIds, ASSIGNMENT_CREATE);
    this.bookedFromOfferRecipientId = bookedFromOfferRecipientId;
  }

  public Optional<Integer> getBookedFromOfferRecipientId() {
    return Optional.ofNullable(bookedFromOfferRecipientId);
  }
}
