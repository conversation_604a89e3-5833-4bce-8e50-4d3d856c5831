package com.bulkloads.web.assignment.handler;

import static com.bulkloads.common.StringUtil.parseDouble;
import static com.bulkloads.common.StringUtil.parseLocalDate;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_GROSS_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TARE_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_DATE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_NUMBER;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_VOLUME;
import static java.util.Objects.isNull;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.event.FileFieldsUpdatedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;


/**
 * Event handler that updates Assignment fields when file fields are created or updated.
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileFieldsUpdatedEventHandler {

  private final AssignmentRepository assignmentRepository;

  @EventListener
  public void handleFileFieldsUpdatedEvent(FileFieldsUpdatedEvent event) {
    File file = event.getFile();
    List<FileField> fields = event.getFields();

    if (file == null || fields == null || fields.isEmpty()) {
      return;
    }

    int fileId = file.getFileId();
    log.debug("Processing file fields created event for fileId: {}", fileId);

    // Find assignments related to this file (check both loading and unloading ticket file IDs)
    List<Assignment> loadingAssignments = assignmentRepository.findAllByLoadingTicketFileId(fileId);
    List<Assignment> unloadingAssignments = assignmentRepository.findAllByUnloadingTicketFileId(fileId);

    for (Assignment loadingAssignment : loadingAssignments) {
      updateAssignmentFields(loadingAssignment, fields, true);
    }

    for (Assignment unloadingAssignment : unloadingAssignments) {
      updateAssignmentFields(unloadingAssignment, fields, false);
    }
  }

  private void updateAssignmentFields(Assignment assignment, List<FileField> fields, boolean isLoadingTicket) {

    Double grossWeight = null;
    Double tareWeight = null;
    Double netWeight = null;
    Double volume = null;

    for (FileField field : fields) {
      if (field.getFieldName() == null) {
        continue;
      }

      String fieldName = field.getFieldName().toLowerCase();
      String fieldValue = field.getFieldValue();

      if (fieldName.equalsIgnoreCase(OCR_FIELD_TICKET_NUMBER)) {
        if (isLoadingTicket) {
          assignment.setLoadingTicketNumberOcr(fieldValue);
          if (isEmpty(assignment.getLoadingTicketNumber())) {
            assignment.setLoadingTicketNumber(fieldValue);
          }
        } else {
          assignment.setUnloadingTicketNumberOcr(fieldValue);
          if (isEmpty(assignment.getUnloadingTicketNumber())) {
            assignment.setUnloadingTicketNumber(fieldValue);
          }
        }

      } else if (fieldName.equalsIgnoreCase(OCR_FIELD_TICKET_DATE)) {
        // Handle date field if needed
        if (isLoadingTicket) {
          LocalDate fieldValueDate = parseLocalDate(fieldValue);
          assignment.setHauledDateOcr(fieldValueDate);
          if (isNull(assignment.getHauledDate())) {
            assignment.setHauledDate(fieldValueDate);
          }
        }

      } else if (fieldName.equalsIgnoreCase(OCR_FIELD_GROSS_WEIGHT)) {
        // Handle tare weight if needed
        grossWeight = parseDouble(fieldValue);
      } else if (fieldName.equalsIgnoreCase(OCR_FIELD_TARE_WEIGHT)) {
        tareWeight = parseDouble(fieldValue);
      } else if (fieldName.equalsIgnoreCase(OCR_FIELD_VOLUME)) {
        volume = parseDouble(fieldValue);
      }
    }

    netWeight = (grossWeight == null || tareWeight == null) ? null : grossWeight - tareWeight;

    if (isLoadingTicket) {
      assignment.setLoadedWeightOcr(netWeight);
      if (isNull(assignment.getLoadedWeight())) {
        assignment.setLoadedWeight(netWeight);
      }

      assignment.setLoadedVolumeOcr(volume);
      if (isNull(assignment.getLoadedVolume())) {
        assignment.setLoadedVolume(volume);
      }
    } else {
      assignment.setUnloadWeightOcr(netWeight);
      if (isNull(assignment.getUnloadWeight())) {
        assignment.setUnloadWeight(netWeight);
      }

      assignment.setUnloadVolumeOcr(volume);
      if (isNull(assignment.getUnloadVolume())) {
        assignment.setUnloadVolume(volume);
      }
    }

    assignment.setModifiedDate(Instant.now());

    assignmentRepository.save(assignment);

  }


}