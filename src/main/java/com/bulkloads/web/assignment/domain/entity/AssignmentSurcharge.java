package com.bulkloads.web.assignment.domain.entity;

import java.math.BigDecimal;
import org.hibernate.annotations.OnDelete;
import org.hibernate.annotations.OnDeleteAction;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "load_assignment_surcharges")
public class AssignmentSurcharge {

  @EmbeddedId
  private SurchargeId id;

  @MapsId("loadAssignmentId")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @OnDelete(action = OnDeleteAction.CASCADE)
  @JoinColumn(name = "load_assignment_id")
  private Assignment loadAssignment;

  @MapsId("surchargeTypeId")
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "load_assignment_surcharge_type_id")
  private SurchargeType surchargeType;

  @Column(name = "load_assignment_surcharge")
  private BigDecimal surcharge;
}