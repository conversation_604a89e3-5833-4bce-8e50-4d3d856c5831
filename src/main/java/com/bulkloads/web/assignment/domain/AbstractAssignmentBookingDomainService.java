package com.bulkloads.web.assignment.domain;

import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isValidEmail;
import static com.bulkloads.config.AppConstants.AssignmentStatus.COMPLETED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.DELIVERED;
import static com.bulkloads.config.AppConstants.AssignmentStatus.UNASSIGNED;
import static com.bulkloads.config.AppConstants.RateType.FLAT;
import static com.bulkloads.config.AppConstants.RateType.GALLON;
import static com.bulkloads.config.AppConstants.RateType.HOUR;
import static com.bulkloads.config.AppConstants.RateType.LITER;
import static com.bulkloads.config.AppConstants.RateType.MILE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_GROSS_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TARE_WEIGHT;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_DATE;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_TICKET_NUMBER;
import static com.bulkloads.web.file.service.FileOcrService.OCR_FIELD_VOLUME;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Stream;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.StringUtil;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.web.assignment.domain.data.AssignmentData;
import com.bulkloads.web.assignment.domain.data.AssignmentSurchargeData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentFile;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import com.bulkloads.web.assignment.domain.entity.SurchargeType;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.repository.FileFieldRepository;
import com.bulkloads.web.file.repository.FileRepository;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractAssignmentBookingDomainService extends BaseDomainService<Assignment> {

  public static final String LOAD_ASSIGNMENT_ID = "load_assignment_id";
  public static final String ASSIGNMENTS = "assignments";
  public static final String TO_USER_ID = "to_user_id";
  public static final String TO_AB_USER_ID = "to_ab_user_id";
  public static final String TO_AB_COMPANY_ID = "to_ab_company_id";
  public static final String PAYMENT = "payment";
  public static final String PAID = "paid";
  public static final String NUMBER_OF_LOADS = "number_of_loads";
  public static final String SHARED_WITH_HIRING_COMPANY = "shared_with_hiring_company";
  public static final String LOAD_ID = "load_id";
  public static final String LOGIN = "login";
  public static final String ASSIGNMENT_STATUS = "assignment_status";
  public static final String PICKUP_NUMBER = "pick_number";
  public static final String CONFIRMATION_CC_OTHERS = "confirmation_cc_others";
  public static final String PICKUP_NOTES = "pickup_notes";
  public static final String DROP_NUMBER = "drop_number";
  public static final String DROP_NOTES = "drop_notes";
  public static final String WORK_ORDER_NUMBER = "work_order_number";
  public static final String INSIDE_NOTES = "inside_notes";
  public static final String ORIGINAL_RATE = "original_rate";
  public static final String ORIGINAL_RATE_TYPE = "original_rate_type";
  public static final String ORIGINAL_RATE_PERCENTAGE = "original_rate_percentage";
  public static final String RATE = "rate";
  public static final String RATE_TYPE = "rate_type";
  public static final String EST_WEIGHT = "est_weight";
  public static final String EST_VOLUME = "est_volume";
  public static final String EST_MILES = "est_miles";
  public static final String EST_HOURS = "est_hours";
  public static final String SURCHARGES = "surcharges";
  public static final String LOADED_WEIGHT = "loaded_weight";
  public static final String UNLOAD_WEIGHT = "unload_weight";
  public static final String LOADED_VOLUME = "loaded_volume";
  public static final String UNLOAD_VOLUME = "unload_volume";
  public static final String LOADING_TICKET_NUMBER = "loading_ticket_number";
  public static final String BOL_NUMBER = "bol_number";
  public static final String LOAD_ASSIGNMENT_NUMBER = "load_assignment_number";
  public static final String SCHEDULED_PICKUP_DATE = "scheduled_pickup_date";
  public static final String SCHEDULED_DROP_DATE = "scheduled_drop_date";
  public static final String HAULED_NOTES = "hauled_notes";
  public static final String HAULED_DATE = "hauled_date";
  public static final String BILL_WEIGHT_USE = "bill_weight_use";
  public static final String BILL_MILES = "bill_miles";
  public static final String BILL_HOURS = "bill_hours";
  public static final String CONFIRMATION_TO_AB_USER_IDS = "confirmation_to_ab_user_ids";
  public static final String FILES = "files";
  public static final BigDecimal ONE_HUNDRED = BigDecimal.valueOf(100);
  public static final String BILL_WEIGHT = "bill_weight";
  public static final String BILL_VOLUME = "bill_volume";
  public static final String UNLOADING_TICKET_NUMBER = "unloading_ticket_number";
  public static final String BILL_TO_AB_COMPANY_ID = "bill_to_ab_company_id";
  public static final String TO_PAYMENT = "to_payment";
  public static final String TO_PAYMENT_NOTES = "to_payment_notes";
  public static final String TO_PAID = "to_paid";
  public static final String BILL_TO_AB_USER_ID = "bill_to_ab_user_id";
  public static final String AUTO_INVOICE = "auto_invoice";

  @Autowired
  private FileRepository fileRepository;

  @Autowired
  private FileFieldRepository fileFieldRepository;

  public void validateEntity(final Result<Assignment> result, final Assignment entity) {

    setAssignmentStatus(entity, entity.getAssignmentStatus());

    if (isNull(entity.getEstWeight()) || entity.getEstWeight() < 0) {
      entity.setEstWeight(52000d);
    }

    // Process files and handle OCR data
    processAssignmentFiles(result, entity);

    if (!isEmpty(entity.getConfirmationCcOthers())) {
      //split on , and validate each string is a valid email
      Arrays.stream(entity.getConfirmationCcOthers().split(",")).forEach(email -> {
        if (!isValidEmail(email)) {
          result.addError(CONFIRMATION_CC_OTHERS, "Enter a valid cc email " + email);
        }
      });
    }

    //calculations
    if (nonNull(entity.getRateType()) && nonNull(entity.getAssignmentStatus())
        && !UNASSIGNED.equals(entity.getAssignmentStatus())) {

      //estimated field calculations
      if (nonNull(entity.getEstMiles()) && nonNull(entity.getMileage())) {
        entity.setEstMiles(entity.getMileage().setScale(2, RoundingMode.HALF_UP));
      }

      if (nonNull(entity.getLoad()) && nonNull(entity.getLoad().getLoEstHours())) {
        entity.setEstHours(entity.getLoad().getLoEstHours().setScale(2, RoundingMode.HALF_UP));
      }

      final BigDecimal estQuantity = calculateEstQuantity(result, entity);
      entity.setEstQuantity(estQuantity);

      final BigDecimal estSubtotal = calculateSubtotal(entity.getRate(), estQuantity);
      entity.setEstSubtotal(estSubtotal);

      final BigDecimal estSurcharges = calculateEstSurcharges(entity);
      entity.setEstSurcharges(estSurcharges);

      entity.setEstTotal(estSubtotal.add(estSurcharges).setScale(2, RoundingMode.HALF_UP));

      if (nonNull(entity.getEstMiles()) && entity.getEstMiles().compareTo(BigDecimal.ZERO) > 0) {
        entity.setEstRatePerMile(estSubtotal.divide(entity.getEstMiles(), 2, RoundingMode.HALF_UP));
      }

      if (nonNull(entity.getBillWeightUse())
          && Stream.of(LOADED_WEIGHT, UNLOAD_WEIGHT).noneMatch(s -> s.equals(entity.getBillWeightUse()))) {
        result.addError(BILL_WEIGHT_USE, "Must be either loaded_weight or unload_weight");
      }

      //billing field calculations
      if (isNull(entity.getBillMiles()) && nonNull(entity.getEstMiles())) {
        entity.setBillMiles(entity.getEstMiles());
      }

      if (isNull(entity.getBillHours()) && nonNull(entity.getEstHours())) {
        entity.setBillHours(entity.getEstHours());
      }

      if (nonNull(entity.getBillWeightUse())
          || nonNull(entity.getBillMiles())
          || nonNull(entity.getBillHours())) {

        final BigDecimal billQuantity = calculateBillQuantity(entity, result);
        entity.setBillQuantity(billQuantity);

        final BigDecimal billSubtotal = calculateSubtotal(entity.getRate(), billQuantity);
        entity.setBillSubtotal(billSubtotal);

        final BigDecimal billSurcharges = calculateBillSurcharges(entity);
        entity.setBillSurcharges(billSurcharges);
        entity.setBillTotal(billSubtotal.add(billSurcharges).setScale(2, RoundingMode.HALF_UP));

        if (nonNull(entity.getBillMiles()) && entity.getBillMiles().compareTo(BigDecimal.ZERO) > 0) {
          entity.setBillRatePerMile(billSubtotal.divide(entity.getBillMiles(), 2, RoundingMode.HALF_UP));
        }
      }

      //percentage of original rate validation
      if (nonNull(entity.getOriginalRatePercentage())) {
        final BigDecimal originalRatePercentage = entity.getOriginalRatePercentage();
        if (originalRatePercentage.compareTo(BigDecimal.ZERO) < 0 || originalRatePercentage.compareTo(ONE_HUNDRED) > 0) {
          result.addError(ORIGINAL_RATE_PERCENTAGE, "The % must be between 0-100");
        } else {
          if (nonNull(entity.getOriginalRateType()) && nonNull(entity.getRateType()) && !entity.getOriginalRateType()
              .equals(entity.getRateType())) {
            result.addError(RATE_TYPE, "For a percentage rate, the rate type must be the same as the original");
          }

          if (isNull(entity.getOriginalRate()) || entity.getOriginalRate().compareTo(BigDecimal.ZERO) < 0
              || entity.getOriginalRate().compareTo(BigDecimal.valueOf(31000)) > 0) {
            result.addError(ORIGINAL_RATE, "Enter a valid number");
          } else {
            final BigDecimal originalRate = entity.getOriginalRate();
            final BigDecimal calc = originalRate.multiply(originalRatePercentage).divide(ONE_HUNDRED, 2, RoundingMode.HALF_UP);
            final BigDecimal plusminus = BigDecimal.valueOf(0.02);
            final BigDecimal mincalc = calc.subtract(plusminus);
            final BigDecimal maxcalc = calc.add(plusminus);

            if (nonNull(entity.getRate()) && (entity.getRate().compareTo(mincalc) < 0 || entity.getRate().compareTo(maxcalc) > 0)) {
              result.addError(RATE, "The rate " + entity.getRate() + " is not " + originalRatePercentage + "% of the original rate of " + originalRate
                  + ". Should be " + calc);
            }
          }
        }
      } else {
        entity.setOriginalRate(null);
        entity.setOriginalRateVisible(false);
      }

      if (nonNull(entity.getLoadedWeight())) {
        final double loadedWeight = entity.getLoadedWeight();
        if (loadedWeight < 0) {
          result.addError(LOADED_WEIGHT, "Enter a positive number");
        } else if (loadedWeight > 999999) {
          //TODO
          result.addError(LOADED_WEIGHT, "The origin weight can be up to 999,999 lbs");
        }
      }

      if (nonNull(entity.getUnloadWeight())) {
        final double unloadWeight = entity.getUnloadWeight();
        if (unloadWeight < 0) {
          result.addError(UNLOAD_WEIGHT, "Enter a positive number");
        } else if (unloadWeight > 999999) {
          //TODO
          result.addError(LOADED_WEIGHT, "The origin weight can be up to 999,999 lbs");
        }
      }

      if (nonNull(entity.getLoadedVolume())) {
        final double loadedVolume = entity.getLoadedVolume();
        if (loadedVolume < 0) {
          result.addError(LOADED_VOLUME, "Enter a positive number");
        } else if (loadedVolume > 999999) {
          //TODO
          result.addError(LOADED_WEIGHT, "The origin weight can be up to 999,999 gallons/liters");
        }
      }

      if (nonNull(entity.getUnloadVolume())) {
        final double unloadVolume = entity.getUnloadVolume();
        if (unloadVolume < 0) {
          result.addError(UNLOAD_VOLUME, "Enter a positive number");
        } else if (unloadVolume > 999999) {
          //TODO
          result.addError(LOADED_WEIGHT, "The origin weight can be up to 999,999 gallons/liters");
        }
      }

      if (isEmpty(entity.getAssignmentFiles())
          && entity.getAssignmentFiles().stream().anyMatch(file -> isNull(file.getFile().getFileId()))) {
        result.addError(FILES, "A property file_id must be present in the file object");
      }

    }
  }

  protected void validateAssignmentStatusBacktracking(final Result<Assignment> result, final Assignment booking, final String assignmentStatus) {
    if (Stream.of(DELIVERED, COMPLETED).anyMatch(status -> status.equals(booking.getAssignmentStatus()))
        && Stream.of(DELIVERED, COMPLETED).noneMatch(status -> status.equals(assignmentStatus))) {

      final Assignment topLevelParent = getTopParentLoadAssignment(booking);
      validateAssignmentStatus(result, topLevelParent);
    }
  }

  protected void validateSurcharges(final AssignmentData data,
                                    final Result<Assignment> result) {

    if (isMissingOrIsEmpty(data.getSurcharges())) {
      return;
    }

    for (AssignmentSurchargeData surcharge : data.getSurcharges().get()) {
      if (isNull(surcharge.getSurcharge())) {
        // ignore surcharge if blank
      } else if (surcharge.getSurcharge().compareTo(BigDecimal.ZERO) <= 0) {
        result.addError(SURCHARGES, "Enter a positive number");
      } else if (surcharge.getSurcharge().compareTo(BigDecimal.valueOf(10000)) > 0) {
        result.addError(SURCHARGES, "Up to $10,000");
      } else if (isNull(surcharge.getSurchargeType()) || isNull(surcharge.getSurchargeType().getId())) {
        result.addError("surcharge_type_id", "Select the Additional Fee Type");
      } else {
        final SurchargeType surchargeType = surcharge.getSurchargeType();
        if (Boolean.TRUE.equals(surchargeType.getIsPerMile()) && isMissingOrIsEmpty(data.getEstMiles())) {
          result.addError(SURCHARGES, "Cannot apply a 'per mile' charge because the mileage is unknown.");
        }
      }
    }
  }

  protected Assignment getTopParentLoadAssignment(final Assignment entity) {
    final Assignment parentLoadAssignment = entity.getParentLoadAssignment();
    if (isNull(parentLoadAssignment)) {
      return entity;
    }
    return getTopParentLoadAssignment(parentLoadAssignment);
  }

  protected <T> void checkEditPermission(final Result<Assignment> result,
                                         final Optional<?> dataValue,
                                         final T entityValue,
                                         final String errorFieldName) {
    if (hasChange(entityValue, dataValue)) {
      result.addError(errorFieldName, "You don't have permission to edit the " + errorFieldName);
    }
  }

  protected void validateAssignmentStatus(final Result<Assignment> result,
                                          final Assignment assignment) {
    if (assignment.getAutoInvoice() || nonNull(assignment.getLoadInvoiceId())) {
      result.addError(ASSIGNMENT_STATUS,
          "The load or an associated load has been invoiced so the load status cannot change back from Delivered."
              + " Please delete the invoice first to make this change");
    } else if (nonNull(assignment.getChildLoadAssignment())) {
      validateAssignmentStatus(result, assignment.getChildLoadAssignment());
    }
  }

  private BigDecimal calculateBillSurcharges(final Assignment entity) {

    BigDecimal billMiles = nonNull(entity.getBillMiles()) ? entity.getBillMiles() : BigDecimal.ZERO;
    return calculateSurcharges(entity.getSurcharges(), entity.getBillSubtotal(), billMiles);
  }

  private BigDecimal calculateEstSurcharges(final Assignment entity) {

    BigDecimal estMiles = nonNull(entity.getEstMiles()) ? entity.getEstMiles() : BigDecimal.ZERO;
    return calculateSurcharges(entity.getSurcharges(), entity.getEstSubtotal(), estMiles);
  }

  private BigDecimal calculateSubtotal(final BigDecimal rate,
                                       final BigDecimal estQuantity) {
    BigDecimal estSubtotal = BigDecimal.ZERO;

    if (nonNull(estQuantity) && nonNull(rate)) {
      estSubtotal = rate.multiply(estQuantity);
    }
    return estSubtotal.setScale(2, RoundingMode.HALF_UP);
  }

  private BigDecimal calculateEstQuantity(final Result<Assignment> result,
                                          final Assignment entity) {

    BigDecimal estQuantity = null;

    if (nonNull(entity.getEstWeight()) && nonNull(entity.getEstVolume())
        || nonNull(entity.getEstMiles()) || nonNull(entity.getEstHours())) {

      if (nonNull(entity.getRate())
          && (entity.getRate().compareTo(BigDecimal.ZERO) < 0 || entity.getRate().compareTo(BigDecimal.valueOf(31000)) > 0)) {
        result.addError(RATE, "Enter a valid number");
      }

      if (NumberUtils.isParsable(entity.getRateType().getRateType())) {
        if (isNull(entity.getEstWeight()) || entity.getEstWeight() < 0) {
          result.addError(EST_WEIGHT, "Enter a valid number");
        } else {
          estQuantity = BigDecimal.valueOf(entity.getEstWeight()).divide(new BigDecimal(entity.getRateType().getRateType()),
              6, RoundingMode.HALF_UP); // 6 decimal places
        }
      } else if (MILE.equals(entity.getRateType().getRateType())) {
        if (nonNull(entity.getEstMiles())) {
          if (entity.getEstMiles().compareTo(BigDecimal.ZERO) < 0) {
            result.addError(EST_MILES, "Enter a valid number");
          } else {
            estQuantity = entity.getEstMiles();
          }
        }
      } else if (HOUR.equals(entity.getRateType().getRateType())) {
        if (nonNull(entity.getEstHours())) {
          if (entity.getEstHours().compareTo(BigDecimal.ZERO) < 0) {
            result.addError(EST_HOURS, "Enter a valid number");
          } else {
            estQuantity = entity.getEstHours();
          }
        }
      } else if (FLAT.equals(entity.getRateType().getRateType())) {
        estQuantity = BigDecimal.ONE;
      } else if (GALLON.equals(entity.getRateType().getRateType())) {
        if (nonNull(entity.getEstVolume())) {
          if (entity.getEstVolume() < 0
              || entity.getEstVolume() > 20000) {
            result.addError(EST_VOLUME, "Enter a valid number");
          } else {
            estQuantity = BigDecimal.valueOf(entity.getEstVolume());
          }
        }
      } else if (entity.getRateType().getRateType().equals(LITER)) {
        if (nonNull(entity.getEstVolume())) {
          if (entity.getEstVolume() < 0
              || entity.getEstVolume() > 80000) {
            result.addError(EST_VOLUME, "Enter a valid number");
          } else {
            estQuantity = BigDecimal.valueOf(entity.getEstVolume());
          }
        }
      } else {
        result.addError(RATE_TYPE, "Unknown rate type");
      }

    }
    if (nonNull(estQuantity)) {
      estQuantity = estQuantity.setScale(6, RoundingMode.HALF_UP);
    }
    return estQuantity;
  }

  private BigDecimal calculateSurcharges(final List<AssignmentSurcharge> surcharges,
                                         final BigDecimal subtotal,
                                         final BigDecimal miles) {
    BigDecimal amount = BigDecimal.ZERO;

    if (nonNull(surcharges)) {
      for (AssignmentSurcharge surcharge : surcharges) {
        final SurchargeType surchargeType = surcharge.getSurchargeType();
        if (Boolean.TRUE.equals(surchargeType.getIsPercentage())) {
          final BigDecimal surchargeRounded = subtotal
              .multiply(surcharge.getSurcharge().divide(ONE_HUNDRED))
              .setScale(2, RoundingMode.HALF_UP);
          amount = amount.add(surchargeRounded);
        } else if (Boolean.TRUE.equals(surchargeType.getIsPerMile())) {
          if (nonNull(miles)) {
            final BigDecimal surchargeRounded = miles
                .multiply(surcharge.getSurcharge())
                .setScale(2, RoundingMode.HALF_UP);
            amount = amount.add(surchargeRounded);
          }
        } else {
          amount = amount.add(surcharge.getSurcharge());
        }
      }
    }

    return amount.setScale(2, RoundingMode.HALF_UP);
  }

  private BigDecimal calculateBillQuantity(final Assignment entity,
                                           final Result<Assignment> result) {

    BigDecimal billQuantity = null;

    if (NumberUtils.isParsable(entity.getRateType().getRateType())) {

      if (nonNull(entity.getBillWeightUse())) {
        if (LOADED_WEIGHT.equals(entity.getBillWeightUse())) {
          if (nonNull(entity.getLoadedWeight()) && entity.getLoadedWeight() >= 0) {
            entity.setBillWeight(entity.getLoadedWeight());
            billQuantity = BigDecimal
                .valueOf(entity.getLoadedWeight())
                .divide(new BigDecimal(entity.getRateType().getRateType()), 6, RoundingMode.HALF_UP);
          }
        } else {
          if (nonNull(entity.getUnloadWeight()) && entity.getUnloadWeight() >= 0) {
            entity.setBillWeight(entity.getUnloadWeight());
            billQuantity = BigDecimal
                .valueOf(entity.getUnloadWeight())
                .divide(new BigDecimal(entity.getRateType().getRateType()), 6, RoundingMode.HALF_UP);
          }
        }
      }
    } else if (MILE.equals(entity.getRateType().getRateType())) {
      if (nonNull(entity.getBillMiles()) && entity.getBillMiles().compareTo(BigDecimal.ZERO) >= 0) {
        billQuantity = entity.getBillMiles();
      }
    } else if (HOUR.equals(entity.getRateType().getRateType())) {
      if (nonNull(entity.getBillHours()) && entity.getBillHours().compareTo(BigDecimal.ZERO) >= 0) {
        billQuantity = entity.getBillHours();
      }
    } else if (FLAT.equals(entity.getRateType().getRateType())) {
      billQuantity = BigDecimal.ONE;
    } else if (Stream.of(GALLON, LITER).anyMatch(type -> type.equals(entity.getRateType().getRateType()))) {
      if (nonNull(entity.getBillWeightUse())) {
        if (entity.getBillWeightUse().equals(LOADED_WEIGHT)) {
          if (nonNull(entity.getLoadedVolume()) && entity.getLoadedVolume() >= 0) {
            entity.setBillVolume(entity.getLoadedVolume());
            billQuantity = BigDecimal.valueOf(entity.getLoadedVolume());
          }
        } else {
          if (nonNull(entity.getUnloadVolume()) && entity.getUnloadVolume() >= 0) {
            entity.setBillVolume(entity.getUnloadVolume());
            billQuantity = BigDecimal.valueOf(entity.getUnloadVolume());
          }
        }
      }
    } else {
      result.addError(RATE_TYPE, "Unknown rate type");
    }
    if (nonNull(billQuantity)) {
      billQuantity = billQuantity.setScale(6, RoundingMode.HALF_UP);
    }
    return billQuantity;
  }

  public void setAssignmentStatus(final Assignment entity, final String assignmentStatus) {
    entity.setAssignmentStatus(assignmentStatus);

    final Instant now = Instant.now();
    final Integer userId = UserUtil.getUserId().orElse(null);
    final Integer abUserId = UserUtil.getAbUserId().orElse(null);

    // Assigned status
    if (List.of("Assigned", "Dispatched", "Loading", "En Route", "Unloading", "Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getAssignedDate())) {
        entity.setAssignedDate(now);
      }
      if (isNull(entity.getAssignedByUserId()) && nonNull(userId)) {
        entity.setAssignedByUserId(userId);
      }
    } else {
      entity.setAssignedDate(null);
    }

    // Dispatched status
    if (List.of("Dispatched", "Loading", "En Route", "Unloading", "Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getDispatchedDate())) {
        entity.setDispatchedDate(now);
      }
      if (isNull(entity.getDispatchedByUserId()) && nonNull(userId)) {
        entity.setDispatchedByUserId(userId);
      } else if (isNull(entity.getDispatchedByAbUserId()) && nonNull(abUserId)) {
        entity.setDispatchedByAbUserId(abUserId);
      }
    } else {
      entity.setDispatchedDate(null);
    }

    // Loading status
    if (List.of("Loading", "En Route", "Unloading", "Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getLoadingDate())) {
        entity.setLoadingDate(now);
      }
      if (isNull(entity.getLoadingByUserId()) && nonNull(userId)) {
        entity.setLoadingByUserId(userId);
      } else if (isNull(entity.getLoadingByAbUserId()) && nonNull(abUserId)) {
        entity.setLoadingByAbUserId(abUserId);
      }
    } else {
      entity.setLoadingDate(null);
    }

    // En Route status
    if (List.of("En Route", "Unloading", "Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getEnrouteDate())) {
        entity.setEnrouteDate(now);
      }
      if (isNull(entity.getEnrouteByUserId()) && nonNull(userId)) {
        entity.setEnrouteByUserId(userId);
      } else if (isNull(entity.getEnrouteByAbUserId()) && nonNull(abUserId)) {
        entity.setEnrouteByAbUserId(abUserId);
      }
    } else {
      entity.setEnrouteDate(null);
    }

    // Unloading status
    if (List.of("Unloading", "Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getUnloadingDate())) {
        entity.setUnloadingDate(now);
      }
      if (isNull(entity.getUnloadingByUserId()) && nonNull(userId)) {
        entity.setUnloadingByUserId(userId);
      } else if (isNull(entity.getUnloadingByAbUserId()) && nonNull(abUserId)) {
        entity.setUnloadingByAbUserId(abUserId);
      }
    } else {
      entity.setUnloadingDate(null);
    }

    // Delivered status
    if (List.of("Delivered", "Completed").contains(assignmentStatus)) {
      if (isNull(entity.getDeliveredDate())) {
        entity.setDeliveredDate(now);
      }
      if (isNull(entity.getDeliveredByUserId()) && nonNull(userId)) {
        entity.setDeliveredByUserId(userId);
      } else if (isNull(entity.getDeliveredByAbUserId()) && nonNull(abUserId)) {
        entity.setDeliveredByAbUserId(abUserId);
      }
    } else {
      entity.setDeliveredDate(null);
    }

    // Completed status
    if ("Completed".equals(assignmentStatus)) {
      if (isNull(entity.getCompletedDate())) {
        entity.setCompletedDate(now);
      }
      if (isNull(entity.getCompletedByUserId()) && nonNull(userId)) {
        entity.setCompletedByUserId(userId);
      } else if (isNull(entity.getCompletedByAbUserId()) && nonNull(abUserId)) {
        entity.setCompletedByAbUserId(abUserId);
      }
    } else {
      entity.setCompletedDate(null);
    }
  }

  public void processOcrData(Assignment entity) {
    // Process OCR data from loading and unloading ticket files
    if (entity.getLoadingTicketFileId() != null) {
      File loadingTicketFile = fileRepository.findById(entity.getLoadingTicketFileId()).orElseThrow();
      // Process OCR data from loading ticket file
      if (Boolean.TRUE.equals(loadingTicketFile.getOcrApproved())) {
        List<FileField> fileFields = loadingTicketFile.getFileFields();

        Double grossWeight = null;
        Double tareWeight = null;

        for (FileField field : fileFields) {
          String fieldName = field.getFieldName();
          String fieldValue = field.getFieldValue();
          if (fieldName == null) {
            continue;
          }

          if (OCR_FIELD_TICKET_NUMBER.equalsIgnoreCase(fieldName)) {
            entity.setLoadingTicketNumberOcr(fieldValue);
            if (isEmpty(entity.getLoadingTicketNumber())) {
              entity.setLoadingTicketNumber(fieldValue);
            }
          } else if (OCR_FIELD_TICKET_DATE.equalsIgnoreCase(fieldName)) {
            LocalDate date = StringUtil.parseLocalDate(fieldValue);
            entity.setHauledDateOcr(date);
            if (entity.getHauledDate() == null) {
              entity.setHauledDate(date);
            }
          } else if (OCR_FIELD_VOLUME.equalsIgnoreCase(fieldName)) {
            Double volume = StringUtil.parseDouble(fieldValue);
            entity.setLoadedVolumeOcr(volume);
            if (entity.getLoadedVolume() == null) {
              entity.setLoadedVolume(volume);
            }
          } else if (OCR_FIELD_GROSS_WEIGHT.equalsIgnoreCase(fieldName)) {
            grossWeight = StringUtil.parseDouble(fieldValue);
          } else if (OCR_FIELD_TARE_WEIGHT.equalsIgnoreCase(fieldName)) {
            tareWeight = StringUtil.parseDouble(fieldValue);
          }
        }

        // Calculate net weight if both gross and tare weights are available
        if (grossWeight != null && tareWeight != null) {
          Double netWeight = grossWeight - tareWeight;
          entity.setLoadedWeightOcr(netWeight);
          if (entity.getLoadedWeight() == null) {
            entity.setLoadedWeight(netWeight);
          }
        }
      }

    }

    if (entity.getUnloadingTicketFileId() != null) {
      File unloadingTicketFile = fileRepository.findById(entity.getUnloadingTicketFileId()).orElseThrow();
      // Process OCR data from unloading ticket file
      if (Boolean.TRUE.equals(unloadingTicketFile.getOcrApproved())) {
        List<FileField> fileFields = fileFieldRepository.findByFile(unloadingTicketFile);

        Double grossWeight = null;
        Double tareWeight = null;

        for (FileField field : fileFields) {
          String fieldName = field.getFieldName();
          String fieldValue = field.getFieldValue();
          if (fieldName == null) {
            continue;
          }

          if (OCR_FIELD_TICKET_NUMBER.equalsIgnoreCase(fieldName)) {
            entity.setUnloadingTicketNumberOcr(fieldValue);
            if (isEmpty(entity.getUnloadingTicketNumber())) {
              entity.setUnloadingTicketNumber(fieldValue);
            }
          } else if (OCR_FIELD_VOLUME.equalsIgnoreCase(fieldName)) {
            Double volume = StringUtil.parseDouble(fieldValue);
            entity.setUnloadVolumeOcr(volume);
            if (entity.getUnloadVolume() == null) {
              entity.setUnloadVolume(volume);
            }
          } else if (OCR_FIELD_GROSS_WEIGHT.equalsIgnoreCase(fieldName)) {
            grossWeight = StringUtil.parseDouble(fieldValue);
          } else if (OCR_FIELD_TARE_WEIGHT.equalsIgnoreCase(fieldName)) {
            tareWeight = StringUtil.parseDouble(fieldValue);
          }
        }

        // Calculate net weight if both gross and tare weights are available
        if (grossWeight != null && tareWeight != null) {
          Double netWeight = grossWeight - tareWeight;
          entity.setUnloadWeightOcr(netWeight);
          if (entity.getUnloadWeight() == null) {
            entity.setUnloadWeight(netWeight);
          }
        }
      }
    }
  }

  /**
   * Process assignment files, handling file associations, OCR data extraction, and updates.
   * This is based on the ColdFusion persistLoadAssignmentBookingFiles function.
   */
  protected void processAssignmentFiles(Result<Assignment> result, Assignment entity) {

    // Process OCR data from files

    setLoadingUnloadingTicketFileIds(entity);

    // Update assignment with file-related data
    entity.setNumberOfFiles(entity.getAssignmentFiles().size());

    // Calculate total files size
    Long totalFilesSize = entity.getAssignmentFiles().stream()
        .filter(af -> !af.getDeleted())
        .map(af -> af.getFile().getSize())
        .filter(Objects::nonNull)
        .reduce(0L, Long::sum);

    entity.setTotalFilesSize(Math.toIntExact(totalFilesSize));

    processOcrData(entity);

  }

  /**
   * Process OCR data from loading and unloading ticket files.
   */
  protected void setLoadingUnloadingTicketFileIds(Assignment entity) {
    // Find loading ticket file (file type ID 1)
    File loadingTicketFile = entity.getAssignmentFiles().stream()
        .filter(af -> !af.getDeleted() && af.getFile().getFileType() != null &&
            af.getFile().getFileType().getFileTypeId() == 1)
        .map(AssignmentFile::getFile)
        .findFirst()
        .orElse(null);

    // Find unloading ticket file (file type ID 2)
    File unloadingTicketFile = entity.getAssignmentFiles().stream()
        .filter(af -> !af.getDeleted() && af.getFile().getFileType() != null &&
            af.getFile().getFileType().getFileTypeId() == 2)
        .map(AssignmentFile::getFile)
        .findFirst()
        .orElse(null);

    // Set loading and unloading ticket file IDs
    entity.setLoadingTicketFileId(loadingTicketFile != null ? loadingTicketFile.getFileId() : null);
    entity.setUnloadingTicketFileId(unloadingTicketFile != null ? unloadingTicketFile.getFileId() : null);

  }

}
