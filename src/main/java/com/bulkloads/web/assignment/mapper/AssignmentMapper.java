package com.bulkloads.web.assignment.mapper;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import com.bulkloads.common.StringUtil;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRepository;
import com.bulkloads.web.assignment.api.dto.AssignmentSearchParams;
import com.bulkloads.web.assignment.api.dto.BookingSearchParams;
import com.bulkloads.web.assignment.api.dto.LoadBookingRequest;
import com.bulkloads.web.assignment.domain.data.AssignmentData;
import com.bulkloads.web.assignment.domain.data.BlankAssignmentData;
import com.bulkloads.web.assignment.domain.data.BookingData;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.domain.entity.AssignmentFile;
import com.bulkloads.web.assignment.domain.entity.AssignmentFileId;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import com.bulkloads.web.assignment.domain.entity.SurchargeId;
import com.bulkloads.web.assignment.service.dto.AssignmentSearchRequest;
import com.bulkloads.web.assignment.service.dto.BookingSearchRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentRequest;
import com.bulkloads.web.assignment.service.dto.CreateLoadAssignmentSubRequest;
import com.bulkloads.web.assignment.service.dto.UpdateLoadAssignmentRequest;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentRepository;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.load.domain.data.LoadData;
import com.bulkloads.web.load.domain.template.AssignmentTemplateModel;
import com.bulkloads.web.load.domain.template.DynamicLink;
import com.bulkloads.web.load.domain.template.LoadAssignmentTemplateModel;
import org.mapstruct.AfterMapping;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = {CommonMapper.class, AssignmentFileMapper.class, AssignmentSurchargeMapper.class},
    imports = {StringUtil.class, Optional.class})
public abstract class AssignmentMapper {

  @Autowired
  private AbCompanyRepository abCompanyRepository;

  @Autowired
  private AbUserRepository abUserRepository;

  @Autowired
  private UserCompanyEquipmentRepository userCompanyEquipmentRepository;

  public abstract BookingSearchRequest bookingParamsToRequest(final BookingSearchParams loadSearchParams);

  public List<AssignmentData> createLoadAssignmentRequestToData(final CreateLoadAssignmentRequest request, final Map<String, String> errors) {

    List<CreateLoadAssignmentSubRequest> assignments = existsAndIsNotEmpty(request.getAssignments()) ? request.getAssignments().get() : List.of();

    if (assignments.isEmpty()) {
      assignments = IntStream
          .range(0, request.getNumberOfLoads().orElseThrow())
          .mapToObj(i -> CreateLoadAssignmentSubRequest.builder().build())
          .toList();
    }

    return assignments.stream()
        .map(assignmentRequest -> toAssignmentData(assignmentRequest, request, errors))
        .sorted((o1, o2) -> {
          if (existsAndIsNotEmpty(o1.getLoadAssignmentId()) && existsAndIsNotEmpty(o2.getLoadAssignmentId())) {
            return o1.getLoadAssignmentId().get().compareTo(o2.getLoadAssignmentId().get());
          }
          return 0;
        })
        .toList();
  }

  @Mapping(source = "billToAbCompanyId", target = "billToAbCompany")
  @Mapping(source = "billToAbUserId", target = "billToAbUser")
  @Mapping(target = "assignmentStatus", expression = "java(" +
      "request.getAssignmentStatus() != null ? request.getAssignmentStatus().map(StringUtil::toTitleCase) : null)")
  public abstract BookingData bookingRequestToData(final LoadBookingRequest request, @Context final Map<String, String> errors);

  @Mapping(source = "request.toUserId", target = "toUser")
  @Mapping(source = "request.toAbCompanyId", target = "toAbCompany")
  @Mapping(source = "request.toAbUserId", target = "toAbUser")
  @Mapping(source = "request.confirmationToAbUserIds", target = "confirmationToAbUsers")
  @Mapping(source = "request.truckUserCompanyEquipmentId", target = "truckUserCompanyEquipment")
  @Mapping(source = "request.trailerUserCompanyEquipmentId", target = "trailerUserCompanyEquipment")
  @Mapping(source = "subRequest.loadAssignmentNumber", target = "loadAssignmentNumber")
  @Mapping(source = "subRequest.workOrderNumber", target = "workOrderNumber")
  @Mapping(source = "subRequest.pickupNumber", target = "pickupNumber")
  @Mapping(source = "subRequest.dropNumber", target = "dropNumber")
  @Mapping(source = "subRequest.scheduledPickupDate", target = "scheduledPickupDate")
  @Mapping(source = "subRequest.scheduledDropDate", target = "scheduledDropDate")
  @Mapping(target = "assignmentStatus", expression = "java(Optional.ofNullable(request.getAssignmentStatus()).flatMap(" +
      "s -> s.flatMap(x -> Optional.ofNullable(StringUtil.toTitleCase(x)))))")
  protected abstract AssignmentData toAssignmentData(final CreateLoadAssignmentSubRequest subRequest,
                                                     final CreateLoadAssignmentRequest request,
                                                     @Context final Map<String, String> errors);

  @Mapping(source = "toUserId", target = "toUser")
  @Mapping(source = "toAbCompanyId", target = "toAbCompany")
  @Mapping(source = "toAbUserId", target = "toAbUser")
  @Mapping(source = "confirmationToAbUserIds", target = "confirmationToAbUsers")
  @Mapping(source = "truckUserCompanyEquipmentId", target = "truckUserCompanyEquipment")
  @Mapping(source = "trailerUserCompanyEquipmentId", target = "trailerUserCompanyEquipment")
  public abstract AssignmentData updateLoadAssignmentRequestToData(final UpdateLoadAssignmentRequest request, @Context final Map<String, String> errors);

  @Mapping(source = "confirmationToAbUsers", target = "confirmationToAbUserIds")
  @Mapping(source = "files", target = "assignmentFiles")
  public abstract void assignmentDataToEntity(final AssignmentData data, @MappingTarget final Assignment entity);

  @Mapping(source = "files", target = "assignmentFiles")
  public abstract void bookingDataToEntity(final BookingData data, @MappingTarget final Assignment entity);

  public abstract Assignment blankAssignmentDataToEntity(final BlankAssignmentData data);

  public abstract void blankAssignmentDataToEntity(final BlankAssignmentData blankAssignmentDatum, @MappingTarget final Assignment assignment);

  public abstract AssignmentSearchRequest paramsToRequest(final AssignmentSearchParams loadSearchParams);

  @Mapping(source = "loadData.loRate", target = "rate")
  @Mapping(source = "loadData.loRateType", target = "rateType")
  @Mapping(source = "loadData.loEstimatedWeight", target = "estWeight")
  @Mapping(source = "loadData.loEstimatedVolume", target = "estVolume")
  @Mapping(source = "loadData.loEstMiles", target = "estMiles")
  @Mapping(source = "loadData.loEstHours", target = "estHours")
  @Mapping(source = "blankAssignmentData.loadAssignmentId", target = "loadAssignmentId", ignore = true)
  public abstract BookingData mapToBookingData(final LoadData loadData, final BlankAssignmentData blankAssignmentData);

  public abstract BlankAssignmentData entityToBlankData(final Assignment entity);

  public abstract BlankAssignmentData requestToBlankData(final CreateLoadAssignmentSubRequest request);

  protected abstract List<BlankAssignmentData> requestToBlankData(final List<CreateLoadAssignmentSubRequest> requests);

  public abstract List<BlankAssignmentData> entitiesToBlankData(final List<Assignment> entities);

  public LoadAssignmentTemplateModel bookingsToFmModel(final List<Assignment> assignments,
                                                       final List<DynamicLink> dynamicLinks,
                                                       final String domainUrl) {
    final LoadAssignmentTemplateModel loadReroutedTemplateModel = bookingToLoadReroutedFmModel(assignments.get(0));
    final LoadAssignmentTemplateModel.LoadAssignmentTemplateModelBuilder builder = loadReroutedTemplateModel.toBuilder();
    builder
        .assignments(assignments.stream().map(this::bookingToAssignmentFmModel).toList())
        .links(dynamicLinks)
        .domainUrl(domainUrl);
    return builder.build();
  }

  @Mapping(source = "toUser.cellPhone", target = "cellPhone")
  @Mapping(source = "toUserCompany.companyLogoUrl", target = "companyLogoUrl")
  @Mapping(source = "toUserCompany.companyName", target = "companyName")
  @Mapping(source = "toUser.email", target = "email")
  @Mapping(source = "toUser.firstName", target = "firstName")
  @Mapping(source = "toUser.lastName", target = "lastName")
  @Mapping(source = "toUser.cellPhone", target = "phone1")
  @Mapping(source = "toUser.loadConfirmationFooter", target = "loadConfirmationFooter")
  @Mapping(source = "toLoad.dropAbCompany.address", target = "dropAddress")
  @Mapping(source = "toLoad.dropAbCompany.apptRequired", target = "dropApptRequired")
  @Mapping(source = "toLoad.dropAbCompany.companyName", target = "dropCompanyName")
  @Mapping(source = "toLoad.dropAbCompany.companyNotes", target = "dropCompanyNotes")
  @Mapping(source = "toLoad.dropAbCompany.directions", target = "dropDirections")
  @Mapping(source = "toLoad.dropAbCompany.companyPhone", target = "dropCompanyPhone")
  @Mapping(source = "toLoad.dropAbCompany.location", target = "dropLocation")
  @Mapping(source = "toLoad.dropAbCompany.receivingHours", target = "dropReceivingHours")
  @Mapping(source = "toLoad.dropAbCompany.city", target = "dropCity")
  @Mapping(source = "toLoad.dropAbCompany.state", target = "dropState")
  @Mapping(source = "editDate", target = "editDate")
  @Mapping(source = "toLoad.equipmentNames", target = "equipmentNames")
  @Mapping(source = "toLoad.pickupAbCompany.address", target = "pickupAddress")
  @Mapping(source = "toLoad.pickupAbCompany.apptRequired", target = "pickupApptRequired")
  @Mapping(source = "toLoad.pickupAbCompany.companyName", target = "pickupCompanyName")
  @Mapping(source = "toLoad.pickupAbCompany.companyNotes", target = "pickupCompanyNotes")
  @Mapping(source = "toLoad.pickupAbCompany.companyPhone", target = "pickupCompanyPhone")
  @Mapping(source = "toLoad.pickupAbCompany.directions", target = "pickupDirections")
  @Mapping(source = "toLoad.pickupAbCompany.location", target = "pickupLocation")
  @Mapping(source = "toLoad.pickupAbCompany.city", target = "pickupCity")
  @Mapping(source = "toLoad.pickupAbCompany.state", target = "pickupState")
  @Mapping(source = "toLoad.pickupAbCompany.receivingHours", target = "pickupReceivingHours")
  @Mapping(source = "toLoad.shipFrom", target = "shipFrom")
  @Mapping(source = "toLoad.shipTo", target = "shipTo")
  @Mapping(source = "rerouteAbCompany.apptRequired", target = "rerouteApptRequired")
  @Mapping(source = "rerouteAbCompany.companyNotes", target = "rerouteCompanyNotes")
  @Mapping(source = "rerouteAbCompany.companyName", target = "rerouteCompanyName")
  @Mapping(source = "rerouteAbCompany.companyPhone", target = "rerouteCompanyPhone")
  @Mapping(source = "rerouteAbCompany.address", target = "rerouteAddress")
  @Mapping(source = "rerouteAbCompany.directions", target = "rerouteDirections")
  @Mapping(source = "rerouteAbCompany.location", target = "rerouteLocation")
  @Mapping(source = "rerouteAbCompany.receivingHours", target = "rerouteReceivingHours")
  @Mapping(source = "rerouteAbCompany.city", target = "rerouteCity")
  @Mapping(source = "rerouteAbCompany.state", target = "rerouteState")
  @Mapping(source = "rateType.rateTypeTextMedium", target = "rateTypeTextMedium")
  @Mapping(source = "childLoadAssignment.loadAssignmentId", target = "childLoadAssignmentId")
  @Mapping(source = "load.loadId", target = "loadId")
  @Mapping(source = "toLoad.loadId", target = "toLoadId")
  @Mapping(source = "truckUserCompanyEquipment.", target = "truckUserCompanyEquipment")
  @Mapping(source = "trailerUserCompanyEquipment", target = "trailerUserCompanyEquipment")
  public abstract LoadAssignmentTemplateModel bookingToLoadReroutedFmModel(final Assignment entity);

  @Mapping(source = "toLoad.dropAbCompany.address", target = "dropAddress")
  @Mapping(source = "toLoad.dropAbCompany.apptRequired", target = "dropApptRequired")
  @Mapping(source = "toLoad.estimatedMiles", target = "estimatedMiles")
  @Mapping(source = "toLoad.isHazmat", target = "isHazmat")
  @Mapping(source = "toLoad.loCommodity", target = "loCommodity")
  @Mapping(source = "toLoad.originalsRequired", target = "originalsRequired")
  @Mapping(source = "toLoad.pickupAbCompany.apptRequired", target = "pickupApptRequired")
  @Mapping(source = "toLoad.washoutRequired", target = "washoutRequired")
  @Mapping(source = "confirmationFile.fileUrl", target = "confirmationFileUrl")
  @Mapping(source = "confirmationFile.thumbUrl", target = "confirmationThumbUrl")
  @Mapping(source = "rateType.rateTypeTextAbbr", target = "rateTypeTextAbbr")
  @Mapping(source = "rateType.rateTypeTextMedium", target = "rateTypeTextMedium")
  @Mapping(source = "previousRateType.rateTypeTextAbbr", target = "previousRateTypeTextAbbr")
  @Mapping(source = "previousRateType.rateTypeTextMedium", target = "previousRateTypeTextMedium")
  @Mapping(source = "truckUserCompanyEquipment", target = "truckUserCompanyEquipment")
  @Mapping(source = "trailerUserCompanyEquipment", target = "trailerUserCompanyEquipment")
  public abstract AssignmentTemplateModel bookingToAssignmentFmModel(final Assignment entity);

  public LoadAssignmentTemplateModel assignmentsToFmModel(final List<Assignment> assignments,
                                                          final List<DynamicLink> dynamicLinks,
                                                          final String domainUrl) {
    final LoadAssignmentTemplateModel loadReroutedTemplateModel = assignmentToLoadReroutedFmModel(assignments.get(0));
    final LoadAssignmentTemplateModel.LoadAssignmentTemplateModelBuilder builder = loadReroutedTemplateModel.toBuilder();
    builder
        .assignments(assignments.stream().map(this::assignmentToAssignmentFmModel).toList())
        .links(dynamicLinks)
        .domainUrl(domainUrl);
    return builder.build();
  }

  @Mapping(source = "user.cellPhone", target = "cellPhone")
  @Mapping(source = "userCompany.companyLogoUrl", target = "companyLogoUrl")
  @Mapping(source = "userCompany.companyName", target = "companyName")
  @Mapping(source = "user.email", target = "email")
  @Mapping(source = "user.firstName", target = "firstName")
  @Mapping(source = "user.lastName", target = "lastName")
  @Mapping(source = "user.cellPhone", target = "phone1")
  @Mapping(source = "user.loadConfirmationFooter", target = "loadConfirmationFooter")
  @Mapping(source = "load.dropAbCompany.address", target = "dropAddress")
  @Mapping(source = "load.dropAbCompany.apptRequired", target = "dropApptRequired")
  @Mapping(source = "load.dropAbCompany.companyName", target = "dropCompanyName")
  @Mapping(source = "load.dropAbCompany.companyNotes", target = "dropCompanyNotes")
  @Mapping(source = "load.dropAbCompany.directions", target = "dropDirections")
  @Mapping(source = "load.dropAbCompany.companyPhone", target = "dropCompanyPhone")
  @Mapping(source = "load.dropAbCompany.location", target = "dropLocation")
  @Mapping(source = "load.dropAbCompany.receivingHours", target = "dropReceivingHours")
  @Mapping(source = "load.dropAbCompany.city", target = "dropCity")
  @Mapping(source = "load.dropAbCompany.state", target = "dropState")
  @Mapping(source = "editDate", target = "editDate")
  @Mapping(source = "load.equipmentNames", target = "equipmentNames")
  @Mapping(source = "load.pickupAbCompany.address", target = "pickupAddress")
  @Mapping(source = "load.pickupAbCompany.apptRequired", target = "pickupApptRequired")
  @Mapping(source = "load.pickupAbCompany.companyName", target = "pickupCompanyName")
  @Mapping(source = "load.pickupAbCompany.companyNotes", target = "pickupCompanyNotes")
  @Mapping(source = "load.pickupAbCompany.companyPhone", target = "pickupCompanyPhone")
  @Mapping(source = "load.pickupAbCompany.directions", target = "pickupDirections")
  @Mapping(source = "load.pickupAbCompany.location", target = "pickupLocation")
  @Mapping(source = "load.pickupAbCompany.city", target = "pickupCity")
  @Mapping(source = "load.pickupAbCompany.state", target = "pickupState")
  @Mapping(source = "load.pickupAbCompany.receivingHours", target = "pickupReceivingHours")
  @Mapping(source = "load.shipFrom", target = "shipFrom")
  @Mapping(source = "load.shipTo", target = "shipTo")
  @Mapping(source = "rerouteAbCompany.apptRequired", target = "rerouteApptRequired")
  @Mapping(source = "rerouteAbCompany.companyNotes", target = "rerouteCompanyNotes")
  @Mapping(source = "rerouteAbCompany.companyName", target = "rerouteCompanyName")
  @Mapping(source = "rerouteAbCompany.companyPhone", target = "rerouteCompanyPhone")
  @Mapping(source = "rerouteAbCompany.address", target = "rerouteAddress")
  @Mapping(source = "rerouteAbCompany.directions", target = "rerouteDirections")
  @Mapping(source = "rerouteAbCompany.location", target = "rerouteLocation")
  @Mapping(source = "rerouteAbCompany.receivingHours", target = "rerouteReceivingHours")
  @Mapping(source = "rerouteAbCompany.city", target = "rerouteCity")
  @Mapping(source = "rerouteAbCompany.state", target = "rerouteState")
  @Mapping(source = "rateType.rateTypeTextMedium", target = "rateTypeTextMedium")
  @Mapping(source = "childLoadAssignment.loadAssignmentId", target = "childLoadAssignmentId")
  @Mapping(source = "load.loadId", target = "loadId")
  @Mapping(source = "toLoad.loadId", target = "toLoadId")
  @Mapping(source = "truckUserCompanyEquipment.", target = "truckUserCompanyEquipment")
  @Mapping(source = "trailerUserCompanyEquipment", target = "trailerUserCompanyEquipment")
  public abstract LoadAssignmentTemplateModel assignmentToLoadReroutedFmModel(final Assignment entity);

  @Named("assignmentToAssignmentFmModel")
  @Mapping(source = "load.dropAbCompany.address", target = "dropAddress")
  @Mapping(source = "load.dropAbCompany.apptRequired", target = "dropApptRequired")
  @Mapping(source = "load.estimatedMiles", target = "estimatedMiles")
  @Mapping(source = "load.loCommodity", target = "loCommodity")
  @Mapping(source = "load.originalsRequired", target = "originalsRequired")
  @Mapping(source = "load.pickupAbCompany.apptRequired", target = "pickupApptRequired")
  @Mapping(source = "load.washoutRequired", target = "washoutRequired")
  @Mapping(source = "load.isHazmat", target = "isHazmat")
  @Mapping(source = "confirmationFile.fileUrl", target = "confirmationFileUrl")
  @Mapping(source = "confirmationFile.thumbUrl", target = "confirmationThumbUrl")
  @Mapping(source = "originalRateType.rateTypeTextAbbr", target = "originalRateTypeTextAbbr")
  @Mapping(source = "originalRateType.rateTypeTextMedium", target = "originalRateTypeTextMedium")
  @Mapping(source = "rateType.rateTypeTextAbbr", target = "rateTypeTextAbbr")
  @Mapping(source = "rateType.rateTypeTextMedium", target = "rateTypeTextMedium")
  @Mapping(source = "previousRateType.rateTypeTextAbbr", target = "previousRateTypeTextAbbr")
  @Mapping(source = "previousRateType.rateTypeTextMedium", target = "previousRateTypeTextMedium")
  @Mapping(source = "truckUserCompanyEquipment", target = "truckUserCompanyEquipment")
  @Mapping(source = "trailerUserCompanyEquipment", target = "trailerUserCompanyEquipment")
  public abstract AssignmentTemplateModel assignmentToAssignmentFmModel(final Assignment entity);

  protected Optional<UserCompanyEquipment> mapUserCompanyEquipmentOptById(final Optional<Integer> id) {
    return id.flatMap(userCompanyEquipmentRepository::findById);
  }

  public Optional<List<BlankAssignmentData>> mapToBlankAssignmentDataOpt(final Optional<List<CreateLoadAssignmentSubRequest>> requestsOpt) {
    return requestsOpt.map(this::requestToBlankData);
  }

  protected Optional<AbCompany> mapAbCompanyOptById(final Optional<Integer> id) {
    return id.flatMap(abCompanyRepository::findById);
  }

  protected Optional<AbUser> mapAbUserOptById(final Optional<Integer> id) {
    return id.flatMap(abUserRepository::findById);
  }

  protected Optional<List<AbUser>> mapAbUsersOptByIds(final Optional<List<Integer>> ids) {
    return Optional.of(
        ids.map(integers -> abUserRepository.findAllByAbUserIdInAndUserCompanyUserCompanyIdAndDeletedIsFalse(integers, UserUtil.getUserCompanyIdOrThrow()))
            .orElse(new ArrayList<>()));
  }

  protected List<Integer> mapIdsOptToAbUsers(final Optional<List<AbUser>> abUsersOpt) {

    if (isMissingOrIsEmpty(abUsersOpt)) {
      return new ArrayList<>();
    }

    return abUsersOpt.map(abUsers -> abUsers.stream().map(AbUser::getAbUserId).collect(Collectors.toCollection(ArrayList::new))).orElse(new ArrayList<>());
  }

  protected String mapUserCompanyEquipment(UserCompanyEquipment equipment) {
    if (equipment == null || isEmpty(equipment.getExternalEquipmentId())) {
      return "";
    }

    return String.format("%s - %s %s",
        equipment.getExternalEquipmentId(),
        equipment.getModel(),
        equipment.getMake()
    );
  }

  @AfterMapping
  public void updateFileReferences(@MappingTarget final Assignment entity) {
    final Integer loadAssignmentId = entity.getLoadAssignmentId();
    final List<AssignmentFile> files = entity.getAssignmentFiles();
    if (!files.isEmpty()) {
      IntStream.range(0, files.size())
          .forEach(i -> {
            final AssignmentFile assignmentFile = files.get(i);
            assignmentFile.setFileOrder(i);
            assignmentFile.setDateAdded(Instant.now());
            final AssignmentFileId assignmentFileId = assignmentFile.getId();
            assignmentFileId.setFileId(assignmentFile.getFile().getFileId());
            assignmentFileId.setLoadAssignmentId(loadAssignmentId);
            assignmentFile.setLoadAssignment(entity);
          });
    }
  }

  @AfterMapping
  public void updateSurchargeReferences(@MappingTarget final Assignment entity) {
    final Integer loadAssignmentId = entity.getLoadAssignmentId();
    final List<AssignmentSurcharge> surcharges = entity.getSurcharges();
    if (!surcharges.isEmpty()) {
      surcharges.forEach(surcharge -> {
        SurchargeId surchargeId;
        if (surcharge.getId() == null) {
          surchargeId = new SurchargeId();
          surchargeId.setLoadAssignmentId(loadAssignmentId);
          surcharge.setLoadAssignment(entity);
          surcharge.setId(surchargeId);
        } else {
          surchargeId = surcharge.getId();
        }
        surchargeId.setSurchargeTypeId(surcharge.getSurchargeType().getId());

      });
    }
  }
}
