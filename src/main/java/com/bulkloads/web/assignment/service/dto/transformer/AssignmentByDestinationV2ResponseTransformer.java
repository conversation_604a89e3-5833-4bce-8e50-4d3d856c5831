package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.AssignmentByDestinationV2Response;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AssignmentByDestinationV2ResponseTransformer implements TupleTransformer<AssignmentByDestinationV2Response> {

  @Override
  public AssignmentByDestinationV2Response transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    return AssignmentByDestinationV2Response.builder()
        .abCompanyId(parts.asInteger("ab_company_id"))
        .companyName(parts.asString("company_name"))
        .city(parts.asString("city"))
        .state(parts.asString("state"))
        .numberOfAssignedLoads(parts.asInteger("number_of_assigned_loads"))
        .numberOfLoads(parts.asInteger("number_of_loads"))
        .build();

  }
}
