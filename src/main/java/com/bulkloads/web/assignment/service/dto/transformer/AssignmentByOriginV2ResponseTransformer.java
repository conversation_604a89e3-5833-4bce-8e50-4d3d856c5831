package com.bulkloads.web.assignment.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.assignment.service.dto.AssignmentByOriginV2Response;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AssignmentByOriginV2ResponseTransformer implements TupleTransformer<AssignmentByOriginV2Response> {

  @Override
  public AssignmentByOriginV2Response transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    return AssignmentByOriginV2Response.builder()
        .abCompanyId(parts.asInteger("ab_company_id"))
        .companyName(parts.asString("company_name"))
        .city(parts.asString("city"))
        .state(parts.asString("state"))
        .numberOfAssignedLoads(parts.asInteger("number_of_assigned_loads"))
        .numberOfLoads(parts.asInteger("number_of_loads"))
        .build();

  }
}
