package com.bulkloads.web.assignment.service.dto;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class BookingSearchRequest {
  Integer loadAssignmentId;
  List<Integer> loadAssignmentIds;
  List<Integer> userIds;
  Integer loadId;
  Integer toLoadId;
  Integer confirmationFileId;
  Boolean active;
  Boolean tmsActive;
  Boolean completed;
  Boolean invoiced;
  Boolean archived;
  Integer loadInvoiceId;
  Boolean autoInvoice;
  Boolean readyToInvoice;
  Boolean intraCompany;
  Boolean toPaid;
  Boolean hasChild;
  Double pickupLatitude;
  Double pickupLongitude;
  Double dropLatitude;
  Double dropLongitude;
  Double radius;
  LocalDate shipFrom;
  LocalDate shipTo;
  String loContractNumber;
  String loadAssignmentNumber;
  String searchTerm;
  LocalDateTime lastModifiedDate;
  Boolean includeDeleted;
  Boolean newShares;
}