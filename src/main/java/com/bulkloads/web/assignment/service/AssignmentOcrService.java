package com.bulkloads.web.assignment.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.DROP;
import static com.bulkloads.config.AppConstants.ReroutePickupDrop.PICKUP;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.StringUtil;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.assignment.domain.AssignmentDomainService;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentOcrDataApprovedEvent;
import com.bulkloads.web.assignment.mapper.AssignmentOcrMapper;
import com.bulkloads.web.assignment.repository.AssignmentOcrQueryRepository;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrReadyResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrRequestResponse;
import com.bulkloads.web.assignment.service.dto.AssignmentOcrUpdateResponse;
import com.bulkloads.web.assignment.service.dto.FileFieldComparisonResponse;
import com.bulkloads.web.assignment.service.dto.UnmatchedExternalGradeResponse;
import com.bulkloads.web.file.domain.entity.File;
import com.bulkloads.web.file.domain.entity.FileField;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import com.bulkloads.web.file.mapper.FileFieldMapper;
import com.bulkloads.web.file.repository.FileRepository;
import com.bulkloads.web.file.service.dto.FileFieldResponse;
import com.bulkloads.web.integration.agtrax.AgTraxInternalService;
import com.bulkloads.web.integration.agtrax.domain.entity.ExternalGrade;
import com.bulkloads.web.integration.agtrax.repository.ExternalGradeRepository;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional(readOnly = true)
@RequiredArgsConstructor
public class AssignmentOcrService {

  private final AssignmentRepository assignmentRepository;
  private final FileRepository fileRepository;
  private final FileFieldMapper fileFieldMapper;
  private final ExternalGradeRepository externalGradeRepository;
  private final AgTraxInternalService agTraxInternalService;
  private final AssignmentDomainService assignmentDomainService;
  private final AssignmentOcrQueryRepository assignmentOcrQueryRepository;
  private final AssignmentOcrMapper mapper;

  /**
   * Get assignments with OCR data ready for approval.
   * These are assignments with processed OCR data that hasn't been approved yet.
   *
   * @return List of assignments with OCR data ready for approval
   */
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrReady() {
    return assignmentOcrQueryRepository.getAssignmentsOcrReady();
  }

  /**
   * Get assignments with OCR files that are either missing or unreadable.
   * These are assignments that are waiting for OCR processing.
   *
   * @return List of assignments with OCR files waiting for processing
   */
  public List<AssignmentOcrReadyResponse> getAssignmentsOcrWaiting() {
    return assignmentOcrQueryRepository.getAssignmentsOcrWaiting();
  }

  public AssignmentOcrRequestResponse getAssignmentOcrData(int loadAssignmentId) {
    // List to collect unmatched external grade details
    List<UnmatchedExternalGradeResponse> unmatchedExternalGrades = new ArrayList<>();

    Assignment assignment = assignmentRepository.findById(loadAssignmentId)
        .orElseThrow(() -> new ValidationException("load_assignment_id", "Could not find id %s".formatted(loadAssignmentId)));

    // Get user company ID from the assignment for use with external grades
    int userCompanyId = assignment.getUserCompany().getUserCompanyId();

    // Get dispatching company name early for reuse
    String companyName = Optional.ofNullable(assignment.getLoad())
        .map(Load::getUserCompany)
        .map(UserCompany::getCompanyName)
        .orElse(null);

    // Get loading and unloading ticket files
    File loadingFile = null;
    File unloadingFile = null;

    List<FileField> originFields = new ArrayList<>();
    if (nonNull(assignment.getLoadingTicketFileId())) {
      loadingFile = fileRepository.findById(assignment.getLoadingTicketFileId()).orElseThrow();
      originFields = fileRepository.getFileFields(loadingFile);
    }

    List<FileField> destinationFields = new ArrayList<>();
    if (nonNull(assignment.getUnloadingTicketFileId())) {
      unloadingFile = fileRepository.findById(assignment.getUnloadingTicketFileId()).orElseThrow();
      destinationFields = fileRepository.getFileFields(unloadingFile);
    }

    // Process grade and non-grade data for loading ticket
    List<FileFieldComparisonResponse> generalFields = new ArrayList<>();
    List<FileFieldComparisonResponse> gradeFields = new ArrayList<>();

    // Create a map of destination fields for easy lookup
    Map<String, FileField> destinationFieldMap = new HashMap<>();
    for (FileField field : destinationFields) {
      if (field.getFieldName() != null) {
        destinationFieldMap.put(field.getFieldName().toLowerCase(), field);
      }
    }

    // For each origin field, find matching destination field and create comparison
    for (FileField originField : originFields) {

      String fieldName = originField.getFieldName();
      String fieldNameLower = fieldName.toLowerCase();

      // Find matching destination field
      FileField destinationField = destinationFieldMap.get(fieldNameLower);

      // Calculate difference if both values are numeric
      Double diff = null;
      if (destinationField != null &&
          originField.getFieldValue() != null &&
          destinationField.getFieldValue() != null) {
        try {
          Double originValue = StringUtil.parseDouble(originField.getFieldValue());
          Double destValue = StringUtil.parseDouble(destinationField.getFieldValue());
          if (nonNull(originValue) && nonNull(destValue)) {
            diff = destValue - originValue;
          }
        } catch (NumberFormatException e) {
          // Not numeric values, skip difference calculation
        }
      }

      // Convert FileField to FileFieldResponse using the mapper
      FileFieldResponse originResponse;
      FileFieldResponse destinationResponse;

      originResponse = fileFieldMapper.entityToResponse(originField);

      // Create empty destination response if no matching field exists
      if (destinationField != null) {
        destinationResponse = fileFieldMapper.entityToResponse(destinationField);
      } else {
        // Create an empty destination field with the same name as the origin field
        destinationResponse = FileFieldResponse.builder()
            .fieldName(originField.getFieldName())
            .fieldLabel(originField.getFieldLabel())
            .fieldType("string")
            .fieldValue("")
            .build();
      }

      // Get external grade code if available
      String externalGradeCode = null;
      FileFieldDefinition definition = originField.getFileFieldDefinition();
      if (nonNull(definition)) {
        if (nonNull(definition.getGradeId())) {
          // grade match
          List<ExternalGrade> externalGrades = externalGradeRepository.findByGradeIdAndUserCompanyId(definition.getGradeId(), userCompanyId);
          if (!externalGrades.isEmpty()) {
            // external grade match
            externalGradeCode = externalGrades.get(0).getExternalGradeCode();
          }
          // else General case, no external match
        } else {
          // not a grade field
        }
      } else {
        // no match - collect unmatched external grade with details
        externalGradeCode = originField.getFieldName();

        // Find or create an external grade
        ExternalGrade externalGrade = externalGradeRepository.findByExternalGradeCodeAndUserCompanyId(
                externalGradeCode, userCompanyId)
            .orElse(null);

        Integer externalGradeId = null;
        if (externalGrade != null) {
          externalGradeId = externalGrade.getExternalGradeId();
        }

        String fieldLabel = originField.getFieldLabel() != null ?
            originField.getFieldLabel() : originField.getFieldName();

        unmatchedExternalGrades.add(UnmatchedExternalGradeResponse.builder()
            .externalGradeId(externalGradeId)
            .companyName(companyName)
            .externalGradeCode(externalGradeCode)
            .externalGradeDescription(fieldLabel)
            .build());
      }

      // Create comparison response using the correct structure
      FileFieldComparisonResponse comparison = FileFieldComparisonResponse.builder()
          .externalGradeCode(externalGradeCode)
          .origin(originResponse)
          .destination(destinationResponse)
          .diff(diff)
          .isUnmatchedExternalGrade(isNull(definition))
          .build();

      // if the fieldName exists in FileFieldDefinitions
      if (nonNull(definition) && isNull(definition.getGradeId())) {
        generalFields.add(comparison);
      } else {
        // grade field
        gradeFields.add(comparison);
      }

    }

    // Now build and return the response
    return AssignmentOcrRequestResponse.builder()
        .loadAssignmentId(assignment.getLoadAssignmentId())
        .dispatchedBy(companyName)
        .driverCompany(Optional.ofNullable(assignment.getToUserCompany())
            .map(UserCompany::getCompanyName)
            .orElse(Optional.ofNullable(assignment.getToAbCompany())
                .map(AbCompany::getCompanyName)
                .orElse("")))
        .driverName(Optional.ofNullable(assignment.getToUser())
            .map(user -> user.getFirstName() + " " + user.getLastName())
            .orElse(Optional.ofNullable(assignment.getToAbUser())
                .map(user -> user.getFirstName() + " " + user.getLastName())
                .orElse("")))
        .driverPhone(Optional.ofNullable(assignment.getAssignmentPhone())
            .filter(phone -> !phone.isEmpty())
            .orElse(Optional.ofNullable(assignment.getToAbUser())
                .map(AbUser::getPhone1)
                .filter(phone -> !phone.isEmpty())
                .orElse(Optional.ofNullable(assignment.getToUser())
                    .map(User::getUserPhone1)
                    .orElse(""))))
        .commodity(String.valueOf(Optional.ofNullable(assignment.getLoad())
            .map(Load::getLoCommodity)
            .orElse("")))
        .pickupCompanyName(Optional.ofNullable(assignment.getLoad()) // if isRerouted and reroutePickupDrop is 'pickup', use rerouteAbCompany
            .map(load -> Boolean.TRUE.equals(assignment.getIsRerouted()) && PICKUP.equalsIgnoreCase(assignment.getReroutePickupDrop()) ?
                assignment.getRerouteAbCompany() : load.getPickupAbCompany())
            .map(company -> company.getCompanyName())
            .orElse(null))
        .dropCompanyName(Optional.ofNullable(assignment.getLoad())
            .map(load -> Boolean.TRUE.equals(assignment.getIsRerouted()) && DROP.equalsIgnoreCase(assignment.getReroutePickupDrop()) ?
                assignment.getRerouteAbCompany() : load.getDropAbCompany())
            .map(AbCompany::getCompanyName)
            .orElse(""))
        .loadingTicketFileUrl(Optional.ofNullable(loadingFile)
            .map(File::getFileUrl)
            .orElse(null))
        .unloadingTicketFileUrl(Optional.ofNullable(unloadingFile)
            .map(File::getFileUrl)
            .orElse(null))
        .assignmentStatus(assignment.getAssignmentStatus())
        .createdAt(assignment.getCreatedDate())
        .hasUnmatchedExternalGrades(assignment.getHasUnmatchedExternalGrades())
        .isAgtraxIntegration(assignment.getAgtraxIntegration())
        .generalFields(generalFields)
        .gradeFields(gradeFields)
        .unmatchedExternalGrades(unmatchedExternalGrades)
        .build();
  }

  @Transactional
  public AssignmentOcrUpdateResponse updateAssignmentOcrData(int loadAssignmentId, AssignmentOcrRequestResponse request) {
    // Remove userCompanyId restriction
    final int userId = UserUtil.getUserIdOrThrow();
    final Instant now = Instant.now();

    Assignment assignment = assignmentRepository.getReferenceById(loadAssignmentId);
    if (assignment == null) {
      throw new ValidationException("load_assignment_id", "Assignment not found");
    }

    File loadingTicketFile = fileRepository.getReferenceById(assignment.getLoadingTicketFileId());
    if (loadingTicketFile == null) {
      throw new ValidationException("loading_ticket_file_id", "Loading ticket file not found");
    }

    File unloadingTicketFile = fileRepository.getReferenceById(assignment.getUnloadingTicketFileId());
    if (unloadingTicketFile == null) {
      throw new ValidationException("unloading_ticket_file_id", "Unloading ticket file not found");
    }
    
    // First, handle general fields for both origin and destination
    if (!isEmpty(request.getGeneralFields())) {
      // Update origin fields (loading ticket)
      updateOriginFields(loadingTicketFile, request.getGeneralFields());

      // Update destination fields (unloading ticket)
      updateDestinationFields(unloadingTicketFile, request.getGeneralFields());
    }

    // Then, handle grade fields for both origin and destination
    if (!isEmpty(request.getGradeFields())) {
      // Update origin fields (loading ticket)
      updateOriginFields(loadingTicketFile, request.getGradeFields());

      // Update destination fields (unloading ticket)
      updateDestinationFields(unloadingTicketFile, request.getGradeFields());
    }

    // Update the approved flag in the load_assignment_files table
    updateAssignmentFileApproval(assignment, userId, now);

    // Update the assignment modified_date so the assignment will emit an event
    assignment.setModifiedDate(now);

    assignmentDomainService.processOcrData(assignment);

    // emit an event that the ocr data has been approved
    assignment.registerDomainEvent(new AssignmentOcrDataApprovedEvent(List.of(assignment.getLoadAssignmentId())));
    log.info("Registered AssignmentOcrDataApprovedEvent for assignment: {}", assignment.getLoadAssignmentId());

    // Save the assignment to trigger domain event publishing
    assignmentRepository.save(assignment);

    AssignmentOcrUpdateResponse response = mapper.entityToResponse(assignment, loadingTicketFile, unloadingTicketFile);

    // Mark the transaction for rollback for testing
    // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();

    return response;
  }

  private void updateOriginFields(File file, List<FileFieldComparisonResponse> fieldComparisons) {

    // Get existing fields
    List<FileField> existingFields = file.getFileFields();

    // Create a map for easy lookup
    Map<String, FileField> fieldMap = new HashMap<>();
    for (FileField field : existingFields) {
      if (field.getFieldName() != null) {
        fieldMap.put(field.getFieldName().toLowerCase(), field);
      }
    }

    // Update fields based on the comparisons
    for (FileFieldComparisonResponse comparison : fieldComparisons) {
      if (comparison.getOrigin() != null && comparison.getOrigin().getFieldName() != null) {
        String fieldName = comparison.getOrigin().getFieldName();
        String fieldNameLower = fieldName.toLowerCase();

        // Find the field in the map
        FileField field = fieldMap.get(fieldNameLower);

        // If the field exists, update its value
        if (field != null && comparison.getOrigin().getFieldValue() != null) {
          field.setFieldValue(comparison.getOrigin().getFieldValue());
        }
      }
    }

    // Save the updated fields
    fileRepository.save(file);
  }

  private void updateDestinationFields(File file, List<FileFieldComparisonResponse> fieldComparisons) {

    // Get existing fields
    List<FileField> existingFields = fileRepository.getFileFields(file);

    // Create a map for easy lookup
    Map<String, FileField> fieldMap = new HashMap<>();
    for (FileField field : existingFields) {
      if (field.getFieldName() != null) {
        fieldMap.put(field.getFieldName().toLowerCase(), field);
      }
    }

    // Update fields based on the comparisons
    for (FileFieldComparisonResponse comparison : fieldComparisons) {
      if (comparison.getDestination() != null && comparison.getDestination().getFieldName() != null) {
        String fieldName = comparison.getDestination().getFieldName();
        String fieldNameLower = fieldName.toLowerCase();

        // Find the field in the map
        FileField field = fieldMap.get(fieldNameLower);

        // If the field exists, update its value
        if (field != null && comparison.getDestination().getFieldValue() != null) {
          field.setFieldValue(comparison.getDestination().getFieldValue());
        } else if (field == null) {
          // If the field doesn't exist and createIfMissing is true, create it
          FileField newField = new FileField();
          newField.setFile(file);
          newField.setFieldName(fieldName);
          newField.setFieldLabel(comparison.getDestination().getFieldLabel() != null ?
              comparison.getDestination().getFieldLabel() : fieldName);
          newField.setFieldType("string");
          newField.setFieldValue(comparison.getDestination().getFieldValue() != null ?
              comparison.getDestination().getFieldValue() : "");

          file.getFileFields().add(newField);
        }
      }
    }

    // Save the updated fields
    fileRepository.save(file);
  }

  private void updateAssignmentFileApproval(Assignment assignment, int userId, Instant now) {
    // Get loading and unloading ticket files (file_type_id 1 and 2)
    File loadingFile = fileRepository.findById(assignment.getLoadingTicketFileId()).orElseThrow();
    // Set OCR approved flag
    loadingFile.setOcrApproved(true);
    loadingFile.setOcrApprovedDate(now);

    // Set the user who approved the OCR
    loadingFile.setApprovedByUserId(userId);

    // Save the file
    fileRepository.save(loadingFile);

    File unloadingFile = fileRepository.findById(assignment.getUnloadingTicketFileId()).orElseThrow();
    // Set OCR approved flag
    unloadingFile.setOcrApproved(true);
    unloadingFile.setOcrApprovedDate(now);

    // Set the user who approved the OCR
    unloadingFile.setApprovedByUserId(userId);

    // Save the file
    fileRepository.save(unloadingFile);

  }

}