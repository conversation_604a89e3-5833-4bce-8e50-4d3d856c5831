package com.bulkloads.web.assignment.service.dto;

import java.time.Instant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class CreateLoadAssignmentSubRequest {

  @Schema(name = "load_assignment_id",
      description = "Optionally pass the load_assignment_id to assign to a specific unassigned record",
      requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer loadAssignmentId;
  @Schema(name = "load_assignment_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String loadAssignmentNumber;
  @Schema(name = "pickup_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String pickupNumber;
  @Schema(name = "drop_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String dropNumber;
  @Schema(name = "work_order_number", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  String workOrderNumber;
  @Schema(name = "scheduled_pickup_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Instant scheduledPickupDate;
  @Schema(name = "scheduled_drop_date", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Instant scheduledDropDate;

}
