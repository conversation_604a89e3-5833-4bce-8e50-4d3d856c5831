package com.bulkloads.web.assignment.repository;

import java.util.List;
import com.bulkloads.web.assignment.domain.entity.AssignmentSurcharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AssignmentSurchageRepository extends JpaRepository<AssignmentSurcharge, Integer> {

  /*
  @Query("""
      SELECT s
      FROM AssignmentSurcharge s
      WHERE
        s.loadAssignment.loadAssignmentId = :loadAssignmentId
      """)
  List<AssignmentSurcharge> getSurcharges(@Param("loadAssignmentId") Integer loadAssignmentId);
  */

  // TODO: should we skip deleted?
  List<AssignmentSurcharge> findByLoadAssignmentLoadAssignmentId(Integer loadAssignmentId);

}
