package com.bulkloads.web.quickbooks.api.openapi;

import java.util.List;
import com.bulkloads.web.infra.quickbooks.dto.BillResponse;
import com.bulkloads.web.infra.quickbooks.dto.CompanyInfoDto;
import com.bulkloads.web.infra.quickbooks.dto.CustomerDto;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceResponse;
import com.bulkloads.web.infra.quickbooks.dto.ItemDto;
import com.bulkloads.web.infra.quickbooks.dto.VendorDto;
import com.bulkloads.web.quickbooks.api.dto.QbAbCompanyResponse;
import com.bulkloads.web.quickbooks.api.dto.QbItemSurchargeTypeResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

@SuppressWarnings("unused")
@Tag(name = "QuickBooks")
public interface QbQueryApiDoc {

  @Operation(summary = "Get Quickbooks Company Info")
  CompanyInfoDto getCompanyInfo();

  @Operation(summary = "Get all Quickbooks Customers")
  List<CustomerDto> getCustomers();

  @Operation(summary = "Get all Quickbooks Vendors")
  List<VendorDto> getVendors();

  @Operation(summary = "Get all not Quickbooks posted Invoices")
  List<InvoiceResponse> getNonPostedInvoices();

  @Operation(summary = "Get all not Quickbooks posted Bills")
  List<BillResponse> getNonPostedBills();

  @Operation(summary = "Get all Quickbooks Items")
  List<ItemDto> getItems();

  @Operation(summary = "Get all Quickbooks Customer & Vendor Mappings")
  List<QbAbCompanyResponse> getCustomerVendorMappings();

  @Operation(summary = "Get all Quickbooks Item Mappings")
  List<QbItemSurchargeTypeResponse> getItemMappings();
}
