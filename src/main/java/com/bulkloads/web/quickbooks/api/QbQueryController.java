package com.bulkloads.web.quickbooks.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import java.util.List;
import com.bulkloads.web.infra.quickbooks.dto.BillResponse;
import com.bulkloads.web.infra.quickbooks.dto.CompanyInfoDto;
import com.bulkloads.web.infra.quickbooks.dto.CustomerDto;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceResponse;
import com.bulkloads.web.infra.quickbooks.dto.ItemDto;
import com.bulkloads.web.infra.quickbooks.dto.VendorDto;
import com.bulkloads.web.quickbooks.api.dto.QbAbCompanyResponse;
import com.bulkloads.web.quickbooks.api.dto.QbItemSurchargeTypeResponse;
import com.bulkloads.web.quickbooks.api.openapi.QbQueryApiDoc;
import com.bulkloads.web.quickbooks.service.QbService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/quickbooks")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class QbQueryController implements QbQueryApiDoc {

  private final QbService service;

  @GetMapping("/my-company")
  public CompanyInfoDto getCompanyInfo() {
    return service.findCompanyInfo();
  }

  @GetMapping("/customers")
  public List<CustomerDto> getCustomers() {
    return service.findAllCustomers();
  }

  @GetMapping("/vendors")
  public List<VendorDto> getVendors() {
    return service.findAllVendors();
  }

  @GetMapping("/invoices")
  public List<InvoiceResponse> getNonPostedInvoices() {
    return service.findNonPostedInvoices();
  }

  @GetMapping("/bills")
  public List<BillResponse> getNonPostedBills() {
    return service.findNonPostedBills();
  }

  @GetMapping("/items")
  public List<ItemDto> getItems() {
    return service.findAllItems();
  }

  @GetMapping("/customer-vendors/mappings")
  public List<QbAbCompanyResponse> getCustomerVendorMappings() {
    return service.getQbAbCompanyMappings();
  }

  @GetMapping("/items/mappings")
  public List<QbItemSurchargeTypeResponse> getItemMappings() {
    return service.getQbItemMappings();
  }
}
