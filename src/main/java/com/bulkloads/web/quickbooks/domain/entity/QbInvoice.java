package com.bulkloads.web.quickbooks.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "qb_invoices")
public class QbInvoice {

  @Id
  @Column(name = "load_invoice_id")
  private Integer loadInvoiceId;

  @Column(name = "qb_invoice_id")
  private String qbInvoiceId;
}
