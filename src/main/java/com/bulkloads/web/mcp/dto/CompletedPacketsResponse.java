package com.bulkloads.web.mcp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class CompletedPacketsResponse {

  @JsonProperty("DOTNumber")
  Integer dotNumber;

  @JsonProperty("DocketNumber")
  String docketNumber;

  @JsonProperty("LegalName")
  String legalName;

  @JsonProperty("CompletedDate")
  String completedDate;
}
