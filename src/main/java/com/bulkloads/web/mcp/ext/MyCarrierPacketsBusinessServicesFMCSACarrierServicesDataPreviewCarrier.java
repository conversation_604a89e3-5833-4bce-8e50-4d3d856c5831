package com.bulkloads.web.mcp.ext;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_CARRIER_I_D,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_DOT_NUMBER,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_DOCKET_NUMBER,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_COMPANY_NAME,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_DB_A_NAME,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_STREET,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_CITY,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_STATE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_ZIP_CODE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_COUNTRY,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_PHONE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_STATUS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_IN_PROCESS_STATE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_POSSIBLE_FRAUD,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_DOUBLE_BROKERING,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_FRAUD_CALL_NUMBER,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_HAS_SAFER_WATCH_KEY,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_WATCHDOG_REPORTS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_ON_CURRENT_CUSTOMER_AGREEMENT,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_CARRIER_RATING,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_RISK_ASSESSMENT,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_RISK_ASSESSMENT_DETAILS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_CERT_DATA,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_EMAILS,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_SOURCE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_IS_INTRASTATE_CARRIER,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_IS_MONITORED,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.JSON_PROPERTY_IS_BLOCKED})
@JsonTypeName("MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.PreviewCarrier")

public class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier {

  public static final String JSON_PROPERTY_CARRIER_I_D = "CarrierID";
  public static final String JSON_PROPERTY_DOT_NUMBER = "DotNumber";
  public static final String JSON_PROPERTY_DOCKET_NUMBER = "DocketNumber";
  public static final String JSON_PROPERTY_COMPANY_NAME = "CompanyName";
  public static final String JSON_PROPERTY_DB_A_NAME = "DBAName";
  public static final String JSON_PROPERTY_STREET = "Street";
  public static final String JSON_PROPERTY_CITY = "City";
  public static final String JSON_PROPERTY_STATE = "State";
  public static final String JSON_PROPERTY_ZIP_CODE = "ZipCode";
  public static final String JSON_PROPERTY_COUNTRY = "Country";
  public static final String JSON_PROPERTY_PHONE = "Phone";
  public static final String JSON_PROPERTY_STATUS = "Status";
  public static final String JSON_PROPERTY_IN_PROCESS_STATE = "InProcessState";
  public static final String JSON_PROPERTY_POSSIBLE_FRAUD = "PossibleFraud";
  public static final String JSON_PROPERTY_DOUBLE_BROKERING = "DoubleBrokering";
  public static final String JSON_PROPERTY_FRAUD_CALL_NUMBER = "FraudCallNumber";
  public static final String JSON_PROPERTY_HAS_SAFER_WATCH_KEY = "HasSaferWatchKey";
  public static final String JSON_PROPERTY_WATCHDOG_REPORTS = "WatchdogReports";
  public static final String JSON_PROPERTY_ON_CURRENT_CUSTOMER_AGREEMENT = "OnCurrentCustomerAgreement";
  public static final String JSON_PROPERTY_CARRIER_RATING = "CarrierRating";
  public static final String JSON_PROPERTY_RISK_ASSESSMENT = "RiskAssessment";
  public static final String JSON_PROPERTY_RISK_ASSESSMENT_DETAILS = "RiskAssessmentDetails";
  public static final String JSON_PROPERTY_CERT_DATA = "CertData";
  public static final String JSON_PROPERTY_EMAILS = "Emails";
  public static final String JSON_PROPERTY_SOURCE = "Source";
  public static final String JSON_PROPERTY_IS_INTRASTATE_CARRIER = "IsIntrastateCarrier";
  public static final String JSON_PROPERTY_IS_MONITORED = "IsMonitored";
  public static final String JSON_PROPERTY_IS_BLOCKED = "IsBlocked";
  private Integer carrierID;
  private Integer dotNumber;
  private String docketNumber;
  private String companyName;
  private String dbAName;
  private String street;
  private String city;
  private String state;
  private String zipCode;
  private String country;
  private String phone;
  private String status;
  private String inProcessState;
  private String possibleFraud;
  private String doubleBrokering;
  private String fraudCallNumber;
  private Boolean hasSaferWatchKey;
  private String watchdogReports;
  private Boolean onCurrentCustomerAgreement;
  private MyCarrierPacketsBusinessCarriersCarrierRating carrierRating;
  private MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessment riskAssessment;
  private MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentDetails riskAssessmentDetails;
  private MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData certData;
  private List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCarrierEmail> emails;
  private SourceEnum source;
  private Boolean isIntrastateCarrier;
  private Boolean isMonitored;
  private Boolean isBlocked;

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier() {
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier carrierID(Integer carrierID) {

    this.carrierID = carrierID;
    return this;
  }

  /**
   * Get carrierID
   *
   * @return carrierID
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getCarrierID() {
    return carrierID;
  }

  @JsonProperty(JSON_PROPERTY_CARRIER_I_D)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierID(Integer carrierID) {
    this.carrierID = carrierID;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier dotNumber(Integer dotNumber) {

    this.dotNumber = dotNumber;
    return this;
  }

  /**
   * Get dotNumber
   *
   * @return dotNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDotNumber() {
    return dotNumber;
  }

  @JsonProperty(JSON_PROPERTY_DOT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDotNumber(Integer dotNumber) {
    this.dotNumber = dotNumber;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }

  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier companyName(String companyName) {

    this.companyName = companyName;
    return this;
  }

  /**
   * Get companyName
   *
   * @return companyName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCompanyName() {
    return companyName;
  }

  @JsonProperty(JSON_PROPERTY_COMPANY_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompanyName(String companyName) {
    this.companyName = companyName;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier dbAName(String dbAName) {

    this.dbAName = dbAName;
    return this;
  }

  /**
   * Get dbAName
   *
   * @return dbAName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDbAName() {
    return dbAName;
  }

  @JsonProperty(JSON_PROPERTY_DB_A_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDbAName(String dbAName) {
    this.dbAName = dbAName;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier street(String street) {

    this.street = street;
    return this;
  }

  /**
   * Get street
   *
   * @return street
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStreet() {
    return street;
  }

  @JsonProperty(JSON_PROPERTY_STREET)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStreet(String street) {
    this.street = street;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier city(String city) {

    this.city = city;
    return this;
  }

  /**
   * Get city
   *
   * @return city
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCity() {
    return city;
  }

  @JsonProperty(JSON_PROPERTY_CITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCity(String city) {
    this.city = city;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier state(String state) {

    this.state = state;
    return this;
  }

  /**
   * Get state
   *
   * @return state
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getState() {
    return state;
  }

  @JsonProperty(JSON_PROPERTY_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setState(String state) {
    this.state = state;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier zipCode(String zipCode) {

    this.zipCode = zipCode;
    return this;
  }

  /**
   * Get zipCode
   *
   * @return zipCode
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getZipCode() {
    return zipCode;
  }

  @JsonProperty(JSON_PROPERTY_ZIP_CODE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setZipCode(String zipCode) {
    this.zipCode = zipCode;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier country(String country) {

    this.country = country;
    return this;
  }

  /**
   * Get country
   *
   * @return country
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCountry() {
    return country;
  }

  @JsonProperty(JSON_PROPERTY_COUNTRY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCountry(String country) {
    this.country = country;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier phone(String phone) {

    this.phone = phone;
    return this;
  }

  /**
   * Get phone
   *
   * @return phone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPhone() {
    return phone;
  }

  @JsonProperty(JSON_PROPERTY_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPhone(String phone) {
    this.phone = phone;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier status(String status) {

    this.status = status;
    return this;
  }

  /**
   * Get status
   *
   * @return status
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getStatus() {
    return status;
  }

  @JsonProperty(JSON_PROPERTY_STATUS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setStatus(String status) {
    this.status = status;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier inProcessState(String inProcessState) {

    this.inProcessState = inProcessState;
    return this;
  }

  /**
   * Get inProcessState
   *
   * @return inProcessState
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IN_PROCESS_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInProcessState() {
    return inProcessState;
  }

  @JsonProperty(JSON_PROPERTY_IN_PROCESS_STATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInProcessState(String inProcessState) {
    this.inProcessState = inProcessState;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier possibleFraud(String possibleFraud) {

    this.possibleFraud = possibleFraud;
    return this;
  }

  /**
   * Get possibleFraud
   *
   * @return possibleFraud
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POSSIBLE_FRAUD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPossibleFraud() {
    return possibleFraud;
  }

  @JsonProperty(JSON_PROPERTY_POSSIBLE_FRAUD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPossibleFraud(String possibleFraud) {
    this.possibleFraud = possibleFraud;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier doubleBrokering(String doubleBrokering) {

    this.doubleBrokering = doubleBrokering;
    return this;
  }

  /**
   * Get doubleBrokering
   *
   * @return doubleBrokering
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOUBLE_BROKERING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDoubleBrokering() {
    return doubleBrokering;
  }

  @JsonProperty(JSON_PROPERTY_DOUBLE_BROKERING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoubleBrokering(String doubleBrokering) {
    this.doubleBrokering = doubleBrokering;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier fraudCallNumber(String fraudCallNumber) {

    this.fraudCallNumber = fraudCallNumber;
    return this;
  }

  /**
   * Get fraudCallNumber
   *
   * @return fraudCallNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FRAUD_CALL_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getFraudCallNumber() {
    return fraudCallNumber;
  }

  @JsonProperty(JSON_PROPERTY_FRAUD_CALL_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFraudCallNumber(String fraudCallNumber) {
    this.fraudCallNumber = fraudCallNumber;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier hasSaferWatchKey(Boolean hasSaferWatchKey) {

    this.hasSaferWatchKey = hasSaferWatchKey;
    return this;
  }

  /**
   * Get hasSaferWatchKey
   *
   * @return hasSaferWatchKey
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAS_SAFER_WATCH_KEY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHasSaferWatchKey() {
    return hasSaferWatchKey;
  }

  @JsonProperty(JSON_PROPERTY_HAS_SAFER_WATCH_KEY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHasSaferWatchKey(Boolean hasSaferWatchKey) {
    this.hasSaferWatchKey = hasSaferWatchKey;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier watchdogReports(String watchdogReports) {

    this.watchdogReports = watchdogReports;
    return this;
  }

  /**
   * Get watchdogReports
   *
   * @return watchdogReports
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_WATCHDOG_REPORTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getWatchdogReports() {
    return watchdogReports;
  }

  @JsonProperty(JSON_PROPERTY_WATCHDOG_REPORTS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setWatchdogReports(String watchdogReports) {
    this.watchdogReports = watchdogReports;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier onCurrentCustomerAgreement(Boolean onCurrentCustomerAgreement) {

    this.onCurrentCustomerAgreement = onCurrentCustomerAgreement;
    return this;
  }

  /**
   * Get onCurrentCustomerAgreement
   *
   * @return onCurrentCustomerAgreement
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_ON_CURRENT_CUSTOMER_AGREEMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOnCurrentCustomerAgreement() {
    return onCurrentCustomerAgreement;
  }

  @JsonProperty(JSON_PROPERTY_ON_CURRENT_CUSTOMER_AGREEMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOnCurrentCustomerAgreement(Boolean onCurrentCustomerAgreement) {
    this.onCurrentCustomerAgreement = onCurrentCustomerAgreement;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier carrierRating(MyCarrierPacketsBusinessCarriersCarrierRating carrierRating) {

    this.carrierRating = carrierRating;
    return this;
  }

  /**
   * Get carrierRating
   *
   * @return carrierRating
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsBusinessCarriersCarrierRating getCarrierRating() {
    return carrierRating;
  }

  @JsonProperty(JSON_PROPERTY_CARRIER_RATING)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierRating(MyCarrierPacketsBusinessCarriersCarrierRating carrierRating) {
    this.carrierRating = carrierRating;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier riskAssessment(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessment riskAssessment) {

    this.riskAssessment = riskAssessment;
    return this;
  }

  /**
   * Get riskAssessment
   *
   * @return riskAssessment
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessment getRiskAssessment() {
    return riskAssessment;
  }

  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskAssessment(MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessment riskAssessment) {
    this.riskAssessment = riskAssessment;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier riskAssessmentDetails(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentDetails riskAssessmentDetails) {

    this.riskAssessmentDetails = riskAssessmentDetails;
    return this;
  }

  /**
   * Get riskAssessmentDetails
   *
   * @return riskAssessmentDetails
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentDetails getRiskAssessmentDetails() {
    return riskAssessmentDetails;
  }

  @JsonProperty(JSON_PROPERTY_RISK_ASSESSMENT_DETAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRiskAssessmentDetails(MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataRiskAssessmentDetails riskAssessmentDetails) {
    this.riskAssessmentDetails = riskAssessmentDetails;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier certData(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData certData) {

    this.certData = certData;
    return this;
  }

  /**
   * Get certData
   *
   * @return certData
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CERT_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData getCertData() {
    return certData;
  }

  @JsonProperty(JSON_PROPERTY_CERT_DATA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCertData(MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCertData certData) {
    this.certData = certData;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier emails(
      List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCarrierEmail> emails) {

    this.emails = emails;
    return this;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier addEmailsItem(
      MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCarrierEmail emailsItem) {
    if (this.emails == null) {
      this.emails = new ArrayList<>();
    }
    this.emails.add(emailsItem);
    return this;
  }

  /**
   * Get emails
   *
   * @return emails
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EMAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCarrierEmail> getEmails() {
    return emails;
  }

  @JsonProperty(JSON_PROPERTY_EMAILS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setEmails(List<MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCarrierEmail> emails) {
    this.emails = emails;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier source(SourceEnum source) {

    this.source = source;
    return this;
  }

  /**
   * Get source
   *
   * @return source
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public SourceEnum getSource() {
    return source;
  }

  @JsonProperty(JSON_PROPERTY_SOURCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSource(SourceEnum source) {
    this.source = source;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier isIntrastateCarrier(Boolean isIntrastateCarrier) {

    this.isIntrastateCarrier = isIntrastateCarrier;
    return this;
  }

  /**
   * Get isIntrastateCarrier
   *
   * @return isIntrastateCarrier
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_INTRASTATE_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsIntrastateCarrier() {
    return isIntrastateCarrier;
  }

  @JsonProperty(JSON_PROPERTY_IS_INTRASTATE_CARRIER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsIntrastateCarrier(Boolean isIntrastateCarrier) {
    this.isIntrastateCarrier = isIntrastateCarrier;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier isMonitored(Boolean isMonitored) {

    this.isMonitored = isMonitored;
    return this;
  }

  /**
   * Get isMonitored
   *
   * @return isMonitored
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_MONITORED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsMonitored() {
    return isMonitored;
  }

  @JsonProperty(JSON_PROPERTY_IS_MONITORED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsMonitored(Boolean isMonitored) {
    this.isMonitored = isMonitored;
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier isBlocked(Boolean isBlocked) {

    this.isBlocked = isBlocked;
    return this;
  }

  /**
   * Get isBlocked
   *
   * @return isBlocked
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_IS_BLOCKED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIsBlocked() {
    return isBlocked;
  }

  @JsonProperty(JSON_PROPERTY_IS_BLOCKED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIsBlocked(Boolean isBlocked) {
    this.isBlocked = isBlocked;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier =
        (MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier) o;
    return Objects.equals(this.carrierID, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.carrierID) && Objects.equals(this.dotNumber,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.dotNumber) && Objects.equals(this.docketNumber,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.docketNumber) && Objects.equals(this.companyName,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.companyName) && Objects.equals(this.dbAName,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.dbAName) && Objects.equals(this.street,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.street) && Objects.equals(this.city,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.city) && Objects.equals(this.state,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.state) && Objects.equals(this.zipCode,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.zipCode) && Objects.equals(this.country,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.country) && Objects.equals(this.phone,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.phone) && Objects.equals(this.status,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.status) && Objects.equals(this.inProcessState,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.inProcessState) && Objects.equals(this.possibleFraud,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.possibleFraud) && Objects.equals(this.doubleBrokering,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.doubleBrokering) && Objects.equals(this.fraudCallNumber,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.fraudCallNumber) && Objects.equals(this.hasSaferWatchKey,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.hasSaferWatchKey) && Objects.equals(this.watchdogReports,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.watchdogReports) && Objects.equals(this.onCurrentCustomerAgreement,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.onCurrentCustomerAgreement) && Objects.equals(this.carrierRating,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.carrierRating) && Objects.equals(this.riskAssessment,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.riskAssessment) && Objects.equals(this.riskAssessmentDetails,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.riskAssessmentDetails) && Objects.equals(this.certData,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.certData) && Objects.equals(this.emails,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.emails) && Objects.equals(this.source,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.source) && Objects.equals(this.isIntrastateCarrier,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.isIntrastateCarrier) && Objects.equals(this.isMonitored,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.isMonitored) && Objects.equals(this.isBlocked,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier.isBlocked);
  }

  @Override
  public int hashCode() {
    return Objects.hash(carrierID, dotNumber, docketNumber, companyName, dbAName, street, city, state, zipCode, country, phone, status, inProcessState,
        possibleFraud, doubleBrokering, fraudCallNumber, hasSaferWatchKey, watchdogReports, onCurrentCustomerAgreement, carrierRating, riskAssessment,
        riskAssessmentDetails, certData, emails, source, isIntrastateCarrier, isMonitored, isBlocked);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataPreviewCarrier {\n");
    sb.append("    carrierID: ").append(toIndentedString(carrierID)).append("\n");
    sb.append("    dotNumber: ").append(toIndentedString(dotNumber)).append("\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    companyName: ").append(toIndentedString(companyName)).append("\n");
    sb.append("    dbAName: ").append(toIndentedString(dbAName)).append("\n");
    sb.append("    street: ").append(toIndentedString(street)).append("\n");
    sb.append("    city: ").append(toIndentedString(city)).append("\n");
    sb.append("    state: ").append(toIndentedString(state)).append("\n");
    sb.append("    zipCode: ").append(toIndentedString(zipCode)).append("\n");
    sb.append("    country: ").append(toIndentedString(country)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    status: ").append(toIndentedString(status)).append("\n");
    sb.append("    inProcessState: ").append(toIndentedString(inProcessState)).append("\n");
    sb.append("    possibleFraud: ").append(toIndentedString(possibleFraud)).append("\n");
    sb.append("    doubleBrokering: ").append(toIndentedString(doubleBrokering)).append("\n");
    sb.append("    fraudCallNumber: ").append(toIndentedString(fraudCallNumber)).append("\n");
    sb.append("    hasSaferWatchKey: ").append(toIndentedString(hasSaferWatchKey)).append("\n");
    sb.append("    watchdogReports: ").append(toIndentedString(watchdogReports)).append("\n");
    sb.append("    onCurrentCustomerAgreement: ").append(toIndentedString(onCurrentCustomerAgreement)).append("\n");
    sb.append("    carrierRating: ").append(toIndentedString(carrierRating)).append("\n");
    sb.append("    riskAssessment: ").append(toIndentedString(riskAssessment)).append("\n");
    sb.append("    riskAssessmentDetails: ").append(toIndentedString(riskAssessmentDetails)).append("\n");
    sb.append("    certData: ").append(toIndentedString(certData)).append("\n");
    sb.append("    emails: ").append(toIndentedString(emails)).append("\n");
    sb.append("    source: ").append(toIndentedString(source)).append("\n");
    sb.append("    isIntrastateCarrier: ").append(toIndentedString(isIntrastateCarrier)).append("\n");
    sb.append("    isMonitored: ").append(toIndentedString(isMonitored)).append("\n");
    sb.append("    isBlocked: ").append(toIndentedString(isBlocked)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

  /**
   * Gets or Sets source
   */
  public enum SourceEnum {
    NUMBER_0(0),

    NUMBER_1(1),

    NUMBER_2(2),

    NUMBER_3(3);

    private Integer value;

    SourceEnum(Integer value) {
      this.value = value;
    }

    @JsonCreator
    public static SourceEnum fromValue(Integer value) {
      for (SourceEnum b : SourceEnum.values()) {
        if (b.value.equals(value)) {
          return b;
        }
      }
      throw new IllegalArgumentException("Unexpected value '" + value + "'");
    }

    @JsonValue
    public Integer getValue() {
      return value;
    }

    @Override
    public String toString() {
      return String.valueOf(value);
    }
  }

}

