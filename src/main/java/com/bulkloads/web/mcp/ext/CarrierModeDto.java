package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierModeDto
 */
@JsonPropertyOrder({CarrierModeDto.JSON_PROPERTY_LESS_THAN_TRUCK_LOAD, CarrierModeDto.JSON_PROPERTY_PARTIAL, CarrierModeDto.JSON_PROPERTY_TRUCKLOAD,
    CarrierModeDto.JSON_PROPERTY_RAIL, CarrierModeDto.JSON_PROPERTY_INTERMODAL, CarrierModeDto.JSON_PROPERTY_AIR, CarrierModeDto.JSON_PROPERTY_EXPEDITE,
    CarrierModeDto.JSON_PROPERTY_OCEAN, CarrierModeDto.JSON_PROPERTY_DRAYAGE})

public class CarrierModeDto {

  public static final String JSON_PROPERTY_LESS_THAN_TRUCK_LOAD = "LessThanTruckLoad";
  public static final String JSON_PROPERTY_PARTIAL = "Partial";
  public static final String JSON_PROPERTY_TRUCKLOAD = "Truckload";
  public static final String JSON_PROPERTY_RAIL = "Rail";
  public static final String JSON_PROPERTY_INTERMODAL = "Intermodal";
  public static final String JSON_PROPERTY_AIR = "Air";
  public static final String JSON_PROPERTY_EXPEDITE = "Expedite";
  public static final String JSON_PROPERTY_OCEAN = "Ocean";
  public static final String JSON_PROPERTY_DRAYAGE = "Drayage";
  private Boolean lessThanTruckLoad;
  private Boolean partial;
  private Boolean truckload;
  private Boolean rail;
  private Boolean intermodal;
  private Boolean air;
  private Boolean expedite;
  private Boolean ocean;
  private Boolean drayage;

  public CarrierModeDto() {
  }

  public CarrierModeDto lessThanTruckLoad(Boolean lessThanTruckLoad) {

    this.lessThanTruckLoad = lessThanTruckLoad;
    return this;
  }

  /**
   * Get lessThanTruckLoad
   *
   * @return lessThanTruckLoad
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LESS_THAN_TRUCK_LOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getLessThanTruckLoad() {
    return lessThanTruckLoad;
  }


  @JsonProperty(JSON_PROPERTY_LESS_THAN_TRUCK_LOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLessThanTruckLoad(Boolean lessThanTruckLoad) {
    this.lessThanTruckLoad = lessThanTruckLoad;
  }


  public CarrierModeDto partial(Boolean partial) {

    this.partial = partial;
    return this;
  }

  /**
   * Get partial
   *
   * @return partial
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PARTIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getPartial() {
    return partial;
  }


  @JsonProperty(JSON_PROPERTY_PARTIAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPartial(Boolean partial) {
    this.partial = partial;
  }


  public CarrierModeDto truckload(Boolean truckload) {

    this.truckload = truckload;
    return this;
  }

  /**
   * Get truckload
   *
   * @return truckload
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TRUCKLOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTruckload() {
    return truckload;
  }


  @JsonProperty(JSON_PROPERTY_TRUCKLOAD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTruckload(Boolean truckload) {
    this.truckload = truckload;
  }


  public CarrierModeDto rail(Boolean rail) {

    this.rail = rail;
    return this;
  }

  /**
   * Get rail
   *
   * @return rail
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_RAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getRail() {
    return rail;
  }


  @JsonProperty(JSON_PROPERTY_RAIL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setRail(Boolean rail) {
    this.rail = rail;
  }


  public CarrierModeDto intermodal(Boolean intermodal) {

    this.intermodal = intermodal;
    return this;
  }

  /**
   * Get intermodal
   *
   * @return intermodal
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getIntermodal() {
    return intermodal;
  }


  @JsonProperty(JSON_PROPERTY_INTERMODAL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setIntermodal(Boolean intermodal) {
    this.intermodal = intermodal;
  }


  public CarrierModeDto air(Boolean air) {

    this.air = air;
    return this;
  }

  /**
   * Get air
   *
   * @return air
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AIR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getAir() {
    return air;
  }


  @JsonProperty(JSON_PROPERTY_AIR)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAir(Boolean air) {
    this.air = air;
  }


  public CarrierModeDto expedite(Boolean expedite) {

    this.expedite = expedite;
    return this;
  }

  /**
   * Get expedite
   *
   * @return expedite
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXPEDITE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getExpedite() {
    return expedite;
  }


  @JsonProperty(JSON_PROPERTY_EXPEDITE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExpedite(Boolean expedite) {
    this.expedite = expedite;
  }


  public CarrierModeDto ocean(Boolean ocean) {

    this.ocean = ocean;
    return this;
  }

  /**
   * Get ocean
   *
   * @return ocean
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OCEAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getOcean() {
    return ocean;
  }


  @JsonProperty(JSON_PROPERTY_OCEAN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOcean(Boolean ocean) {
    this.ocean = ocean;
  }


  public CarrierModeDto drayage(Boolean drayage) {

    this.drayage = drayage;
    return this;
  }

  /**
   * Get drayage
   *
   * @return drayage
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRAYAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDrayage() {
    return drayage;
  }


  @JsonProperty(JSON_PROPERTY_DRAYAGE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDrayage(Boolean drayage) {
    this.drayage = drayage;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierModeDto carrierModeDto = (CarrierModeDto) o;
    return Objects.equals(this.lessThanTruckLoad, carrierModeDto.lessThanTruckLoad) && Objects.equals(this.partial, carrierModeDto.partial) && Objects.equals(
        this.truckload, carrierModeDto.truckload) && Objects.equals(this.rail, carrierModeDto.rail) && Objects.equals(this.intermodal,
        carrierModeDto.intermodal) && Objects.equals(this.air, carrierModeDto.air) && Objects.equals(this.expedite, carrierModeDto.expedite) && Objects.equals(
        this.ocean, carrierModeDto.ocean) && Objects.equals(this.drayage, carrierModeDto.drayage);
  }

  @Override
  public int hashCode() {
    return Objects.hash(lessThanTruckLoad, partial, truckload, rail, intermodal, air, expedite, ocean, drayage);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierModeDto {\n");
    sb.append("    lessThanTruckLoad: ").append(toIndentedString(lessThanTruckLoad)).append("\n");
    sb.append("    partial: ").append(toIndentedString(partial)).append("\n");
    sb.append("    truckload: ").append(toIndentedString(truckload)).append("\n");
    sb.append("    rail: ").append(toIndentedString(rail)).append("\n");
    sb.append("    intermodal: ").append(toIndentedString(intermodal)).append("\n");
    sb.append("    air: ").append(toIndentedString(air)).append("\n");
    sb.append("    expedite: ").append(toIndentedString(expedite)).append("\n");
    sb.append("    ocean: ").append(toIndentedString(ocean)).append("\n");
    sb.append("    drayage: ").append(toIndentedString(drayage)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

