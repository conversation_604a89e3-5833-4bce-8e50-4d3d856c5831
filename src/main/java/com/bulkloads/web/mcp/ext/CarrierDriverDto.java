package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierDriverDto
 */
@JsonPropertyOrder({CarrierDriverDto.JSON_PROPERTY_DRIVER_NAME, CarrierDriverDto.JSON_PROPERTY_CELL_PHONE, CarrierDriverDto.JSON_PROPERTY_COM_CHECK,
    CarrierDriverDto.JSON_PROPERTY_FUEL_ADVANCE})

public class CarrierDriverDto {

  public static final String JSON_PROPERTY_DRIVER_NAME = "DriverName";
  public static final String JSON_PROPERTY_CELL_PHONE = "CellPhone";
  public static final String JSON_PROPERTY_COM_CHECK = "ComCheck";
  public static final String JSON_PROPERTY_FUEL_ADVANCE = "FuelAdvance";
  private String driverName;
  private String cellPhone;
  private Boolean comCheck;
  private Boolean fuelAdvance;

  public CarrierDriverDto() {
  }

  public CarrierDriverDto driverName(String driverName) {

    this.driverName = driverName;
    return this;
  }

  /**
   * Get driverName
   *
   * @return driverName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRIVER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDriverName() {
    return driverName;
  }


  @JsonProperty(JSON_PROPERTY_DRIVER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDriverName(String driverName) {
    this.driverName = driverName;
  }


  public CarrierDriverDto cellPhone(String cellPhone) {

    this.cellPhone = cellPhone;
    return this;
  }

  /**
   * Get cellPhone
   *
   * @return cellPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCellPhone() {
    return cellPhone;
  }


  @JsonProperty(JSON_PROPERTY_CELL_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCellPhone(String cellPhone) {
    this.cellPhone = cellPhone;
  }


  public CarrierDriverDto comCheck(Boolean comCheck) {

    this.comCheck = comCheck;
    return this;
  }

  /**
   * Get comCheck
   *
   * @return comCheck
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COM_CHECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getComCheck() {
    return comCheck;
  }


  @JsonProperty(JSON_PROPERTY_COM_CHECK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setComCheck(Boolean comCheck) {
    this.comCheck = comCheck;
  }


  public CarrierDriverDto fuelAdvance(Boolean fuelAdvance) {

    this.fuelAdvance = fuelAdvance;
    return this;
  }

  /**
   * Get fuelAdvance
   *
   * @return fuelAdvance
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FUEL_ADVANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFuelAdvance() {
    return fuelAdvance;
  }


  @JsonProperty(JSON_PROPERTY_FUEL_ADVANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFuelAdvance(Boolean fuelAdvance) {
    this.fuelAdvance = fuelAdvance;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierDriverDto carrierDriverDto = (CarrierDriverDto) o;
    return Objects.equals(this.driverName, carrierDriverDto.driverName) && Objects.equals(this.cellPhone, carrierDriverDto.cellPhone) && Objects.equals(
        this.comCheck, carrierDriverDto.comCheck) && Objects.equals(this.fuelAdvance, carrierDriverDto.fuelAdvance);
  }

  @Override
  public int hashCode() {
    return Objects.hash(driverName, cellPhone, comCheck, fuelAdvance);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierDriverDto {\n");
    sb.append("    driverName: ").append(toIndentedString(driverName)).append("\n");
    sb.append("    cellPhone: ").append(toIndentedString(cellPhone)).append("\n");
    sb.append("    comCheck: ").append(toIndentedString(comCheck)).append("\n");
    sb.append("    fuelAdvance: ").append(toIndentedString(fuelAdvance)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

