package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsApiFMCSARiskAssessment
 */
@JsonPropertyOrder({MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_OVERALL, MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_AUTHORITY,
    MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_INSURANCE, MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_SAFETY,
    MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_OPERATION, MyCarrierPacketsApiFMCSARiskAssessment.JSON_PROPERTY_OTHER})
@JsonTypeName("MyCarrierPacketsApi.FMCSA.RiskAssessment")

public class MyCarrierPacketsApiFMCSARiskAssessment {

  public static final String JSON_PROPERTY_OVERALL = "Overall";
  public static final String JSON_PROPERTY_AUTHORITY = "Authority";
  public static final String JSON_PROPERTY_INSURANCE = "Insurance";
  public static final String JSON_PROPERTY_SAFETY = "Safety";
  public static final String JSON_PROPERTY_OPERATION = "Operation";
  public static final String JSON_PROPERTY_OTHER = "Other";
  private String overall;
  private String authority;
  private String insurance;
  private String safety;
  private String operation;
  private String other;

  public MyCarrierPacketsApiFMCSARiskAssessment() {
  }

  public MyCarrierPacketsApiFMCSARiskAssessment overall(String overall) {

    this.overall = overall;
    return this;
  }

  /**
   * Get overall
   *
   * @return overall
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OVERALL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOverall() {
    return overall;
  }


  @JsonProperty(JSON_PROPERTY_OVERALL)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOverall(String overall) {
    this.overall = overall;
  }


  public MyCarrierPacketsApiFMCSARiskAssessment authority(String authority) {

    this.authority = authority;
    return this;
  }

  /**
   * Get authority
   *
   * @return authority
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getAuthority() {
    return authority;
  }


  @JsonProperty(JSON_PROPERTY_AUTHORITY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setAuthority(String authority) {
    this.authority = authority;
  }


  public MyCarrierPacketsApiFMCSARiskAssessment insurance(String insurance) {

    this.insurance = insurance;
    return this;
  }

  /**
   * Get insurance
   *
   * @return insurance
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInsurance() {
    return insurance;
  }


  @JsonProperty(JSON_PROPERTY_INSURANCE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsurance(String insurance) {
    this.insurance = insurance;
  }


  public MyCarrierPacketsApiFMCSARiskAssessment safety(String safety) {

    this.safety = safety;
    return this;
  }

  /**
   * Get safety
   *
   * @return safety
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getSafety() {
    return safety;
  }


  @JsonProperty(JSON_PROPERTY_SAFETY)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setSafety(String safety) {
    this.safety = safety;
  }


  public MyCarrierPacketsApiFMCSARiskAssessment operation(String operation) {

    this.operation = operation;
    return this;
  }

  /**
   * Get operation
   *
   * @return operation
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOperation() {
    return operation;
  }


  @JsonProperty(JSON_PROPERTY_OPERATION)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOperation(String operation) {
    this.operation = operation;
  }


  public MyCarrierPacketsApiFMCSARiskAssessment other(String other) {

    this.other = other;
    return this;
  }

  /**
   * Get other
   *
   * @return other
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(String other) {
    this.other = other;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsApiFMCSARiskAssessment myCarrierPacketsApiFMCSARiskAssessment = (MyCarrierPacketsApiFMCSARiskAssessment) o;
    return Objects.equals(this.overall, myCarrierPacketsApiFMCSARiskAssessment.overall) && Objects.equals(this.authority,
        myCarrierPacketsApiFMCSARiskAssessment.authority) && Objects.equals(this.insurance, myCarrierPacketsApiFMCSARiskAssessment.insurance) && Objects.equals(
        this.safety, myCarrierPacketsApiFMCSARiskAssessment.safety) && Objects.equals(this.operation, myCarrierPacketsApiFMCSARiskAssessment.operation)
           && Objects.equals(this.other, myCarrierPacketsApiFMCSARiskAssessment.other);
  }

  @Override
  public int hashCode() {
    return Objects.hash(overall, authority, insurance, safety, operation, other);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsApiFMCSARiskAssessment {\n");
    sb.append("    overall: ").append(toIndentedString(overall)).append("\n");
    sb.append("    authority: ").append(toIndentedString(authority)).append("\n");
    sb.append("    insurance: ").append(toIndentedString(insurance)).append("\n");
    sb.append("    safety: ").append(toIndentedString(safety)).append("\n");
    sb.append("    operation: ").append(toIndentedString(operation)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

