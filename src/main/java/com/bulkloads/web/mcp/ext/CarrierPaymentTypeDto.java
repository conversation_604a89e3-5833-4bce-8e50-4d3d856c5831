package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierPaymentTypeDto
 */
@JsonPropertyOrder({CarrierPaymentTypeDto.JSON_PROPERTY_PAYMENT_TYPE})

public class CarrierPaymentTypeDto {

  public static final String JSON_PROPERTY_PAYMENT_TYPE = "PaymentType";
  private CustomerPaymentTypeDto paymentType;

  public CarrierPaymentTypeDto() {
  }

  public CarrierPaymentTypeDto paymentType(CustomerPaymentTypeDto paymentType) {

    this.paymentType = paymentType;
    return this;
  }

  /**
   * Get paymentType
   *
   * @return paymentType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public CustomerPaymentTypeDto getPaymentType() {
    return paymentType;
  }


  @JsonProperty(JSON_PROPERTY_PAYMENT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPaymentType(CustomerPaymentTypeDto paymentType) {
    this.paymentType = paymentType;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierPaymentTypeDto carrierPaymentTypeDto = (CarrierPaymentTypeDto) o;
    return Objects.equals(this.paymentType, carrierPaymentTypeDto.paymentType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(paymentType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierPaymentTypeDto {\n");
    sb.append("    paymentType: ").append(toIndentedString(paymentType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

