package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

/**
 * MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage
 */
@JsonPropertyOrder({MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_INSURER_NAME,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_TYPE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_POLICY_NUMBER,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_EXPIRATION_DATE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_COVERAGE_LIMIT,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_DEDUCTIBLE,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_REFER_BREAKDOWN,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_REFER_BREAK_DEDUCT,
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.JSON_PROPERTY_CANCELLATION_DATE})
@JsonTypeName("MyCarrierPacketsBusiness.Services.FMCSACarrierServices.Data.Coverage")

public class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage {

  public static final String JSON_PROPERTY_INSURER_NAME = "InsurerName";
  public static final String JSON_PROPERTY_TYPE = "Type";
  public static final String JSON_PROPERTY_POLICY_NUMBER = "PolicyNumber";
  public static final String JSON_PROPERTY_EXPIRATION_DATE = "ExpirationDate";
  public static final String JSON_PROPERTY_COVERAGE_LIMIT = "CoverageLimit";
  public static final String JSON_PROPERTY_DEDUCTIBLE = "Deductible";
  public static final String JSON_PROPERTY_REFER_BREAKDOWN = "ReferBreakdown";
  public static final String JSON_PROPERTY_REFER_BREAK_DEDUCT = "ReferBreakDeduct";
  public static final String JSON_PROPERTY_CANCELLATION_DATE = "CancellationDate";
  private String insurerName;
  private String type;
  private String policyNumber;
  private String expirationDate;
  private String coverageLimit;
  private String deductible;
  private String referBreakdown;
  private String referBreakDeduct;
  private String cancellationDate;

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage() {
  }

  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage insurerName(String insurerName) {

    this.insurerName = insurerName;
    return this;
  }

  /**
   * Get insurerName
   *
   * @return insurerName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_INSURER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getInsurerName() {
    return insurerName;
  }


  @JsonProperty(JSON_PROPERTY_INSURER_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setInsurerName(String insurerName) {
    this.insurerName = insurerName;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage type(String type) {

    this.type = type;
    return this;
  }

  /**
   * Get type
   *
   * @return type
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getType() {
    return type;
  }


  @JsonProperty(JSON_PROPERTY_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setType(String type) {
    this.type = type;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage policyNumber(String policyNumber) {

    this.policyNumber = policyNumber;
    return this;
  }

  /**
   * Get policyNumber
   *
   * @return policyNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getPolicyNumber() {
    return policyNumber;
  }


  @JsonProperty(JSON_PROPERTY_POLICY_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setPolicyNumber(String policyNumber) {
    this.policyNumber = policyNumber;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage expirationDate(String expirationDate) {

    this.expirationDate = expirationDate;
    return this;
  }

  /**
   * Get expirationDate
   *
   * @return expirationDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_EXPIRATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getExpirationDate() {
    return expirationDate;
  }


  @JsonProperty(JSON_PROPERTY_EXPIRATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setExpirationDate(String expirationDate) {
    this.expirationDate = expirationDate;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage coverageLimit(String coverageLimit) {

    this.coverageLimit = coverageLimit;
    return this;
  }

  /**
   * Get coverageLimit
   *
   * @return coverageLimit
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COVERAGE_LIMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCoverageLimit() {
    return coverageLimit;
  }


  @JsonProperty(JSON_PROPERTY_COVERAGE_LIMIT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCoverageLimit(String coverageLimit) {
    this.coverageLimit = coverageLimit;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage deductible(String deductible) {

    this.deductible = deductible;
    return this;
  }

  /**
   * Get deductible
   *
   * @return deductible
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DEDUCTIBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDeductible() {
    return deductible;
  }


  @JsonProperty(JSON_PROPERTY_DEDUCTIBLE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDeductible(String deductible) {
    this.deductible = deductible;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage referBreakdown(String referBreakdown) {

    this.referBreakdown = referBreakdown;
    return this;
  }

  /**
   * Get referBreakdown
   *
   * @return referBreakdown
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFER_BREAKDOWN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferBreakdown() {
    return referBreakdown;
  }


  @JsonProperty(JSON_PROPERTY_REFER_BREAKDOWN)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferBreakdown(String referBreakdown) {
    this.referBreakdown = referBreakdown;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage referBreakDeduct(String referBreakDeduct) {

    this.referBreakDeduct = referBreakDeduct;
    return this;
  }

  /**
   * Get referBreakDeduct
   *
   * @return referBreakDeduct
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REFER_BREAK_DEDUCT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getReferBreakDeduct() {
    return referBreakDeduct;
  }


  @JsonProperty(JSON_PROPERTY_REFER_BREAK_DEDUCT)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReferBreakDeduct(String referBreakDeduct) {
    this.referBreakDeduct = referBreakDeduct;
  }


  public MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage cancellationDate(String cancellationDate) {

    this.cancellationDate = cancellationDate;
    return this;
  }

  /**
   * Get cancellationDate
   *
   * @return cancellationDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CANCELLATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCancellationDate() {
    return cancellationDate;
  }


  @JsonProperty(JSON_PROPERTY_CANCELLATION_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCancellationDate(String cancellationDate) {
    this.cancellationDate = cancellationDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage =
        (MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage) o;
    return Objects.equals(this.insurerName, myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.insurerName) && Objects.equals(this.type,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.type) && Objects.equals(this.policyNumber,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.policyNumber) && Objects.equals(this.expirationDate,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.expirationDate) && Objects.equals(this.coverageLimit,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.coverageLimit) && Objects.equals(this.deductible,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.deductible) && Objects.equals(this.referBreakdown,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.referBreakdown) && Objects.equals(this.referBreakDeduct,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.referBreakDeduct) && Objects.equals(this.cancellationDate,
        myCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage.cancellationDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(insurerName, type, policyNumber, expirationDate, coverageLimit, deductible, referBreakdown, referBreakDeduct, cancellationDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class MyCarrierPacketsBusinessServicesFMCSACarrierServicesDataCoverage {\n");
    sb.append("    insurerName: ").append(toIndentedString(insurerName)).append("\n");
    sb.append("    type: ").append(toIndentedString(type)).append("\n");
    sb.append("    policyNumber: ").append(toIndentedString(policyNumber)).append("\n");
    sb.append("    expirationDate: ").append(toIndentedString(expirationDate)).append("\n");
    sb.append("    coverageLimit: ").append(toIndentedString(coverageLimit)).append("\n");
    sb.append("    deductible: ").append(toIndentedString(deductible)).append("\n");
    sb.append("    referBreakdown: ").append(toIndentedString(referBreakdown)).append("\n");
    sb.append("    referBreakDeduct: ").append(toIndentedString(referBreakDeduct)).append("\n");
    sb.append("    cancellationDate: ").append(toIndentedString(cancellationDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

