package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierBankDto
 */
@JsonPropertyOrder({CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_ROUTING_NUMBER, CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NUMBER,
    CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NAME, CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_NAME, CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_ADDRESS,
    CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_PHONE, CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_FAX, CarrierBankDto.JSON_PROPERTY_CARRIER_BANK_ACCOUNT_TYPE})

public class CarrierBankDto {

  public static final String JSON_PROPERTY_CARRIER_BANK_ROUTING_NUMBER = "CarrierBankRoutingNumber";
  public static final String JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NUMBER = "CarrierBankAccountNumber";
  public static final String JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NAME = "CarrierBankAccountName";
  public static final String JSON_PROPERTY_CARRIER_BANK_NAME = "CarrierBankName";
  public static final String JSON_PROPERTY_CARRIER_BANK_ADDRESS = "CarrierBankAddress";
  public static final String JSON_PROPERTY_CARRIER_BANK_PHONE = "CarrierBankPhone";
  public static final String JSON_PROPERTY_CARRIER_BANK_FAX = "CarrierBankFax";
  public static final String JSON_PROPERTY_CARRIER_BANK_ACCOUNT_TYPE = "CarrierBankAccountType";
  private String carrierBankRoutingNumber;
  private String carrierBankAccountNumber;
  private String carrierBankAccountName;
  private String carrierBankName;
  private String carrierBankAddress;
  private String carrierBankPhone;
  private String carrierBankFax;
  private String carrierBankAccountType;

  public CarrierBankDto() {
  }

  public CarrierBankDto carrierBankRoutingNumber(String carrierBankRoutingNumber) {

    this.carrierBankRoutingNumber = carrierBankRoutingNumber;
    return this;
  }

  /**
   * Get carrierBankRoutingNumber
   *
   * @return carrierBankRoutingNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ROUTING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankRoutingNumber() {
    return carrierBankRoutingNumber;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ROUTING_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankRoutingNumber(String carrierBankRoutingNumber) {
    this.carrierBankRoutingNumber = carrierBankRoutingNumber;
  }


  public CarrierBankDto carrierBankAccountNumber(String carrierBankAccountNumber) {

    this.carrierBankAccountNumber = carrierBankAccountNumber;
    return this;
  }

  /**
   * Get carrierBankAccountNumber
   *
   * @return carrierBankAccountNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankAccountNumber() {
    return carrierBankAccountNumber;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankAccountNumber(String carrierBankAccountNumber) {
    this.carrierBankAccountNumber = carrierBankAccountNumber;
  }


  public CarrierBankDto carrierBankAccountName(String carrierBankAccountName) {

    this.carrierBankAccountName = carrierBankAccountName;
    return this;
  }

  /**
   * Get carrierBankAccountName
   *
   * @return carrierBankAccountName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankAccountName() {
    return carrierBankAccountName;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankAccountName(String carrierBankAccountName) {
    this.carrierBankAccountName = carrierBankAccountName;
  }


  public CarrierBankDto carrierBankName(String carrierBankName) {

    this.carrierBankName = carrierBankName;
    return this;
  }

  /**
   * Get carrierBankName
   *
   * @return carrierBankName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankName() {
    return carrierBankName;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankName(String carrierBankName) {
    this.carrierBankName = carrierBankName;
  }


  public CarrierBankDto carrierBankAddress(String carrierBankAddress) {

    this.carrierBankAddress = carrierBankAddress;
    return this;
  }

  /**
   * Get carrierBankAddress
   *
   * @return carrierBankAddress
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankAddress() {
    return carrierBankAddress;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ADDRESS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankAddress(String carrierBankAddress) {
    this.carrierBankAddress = carrierBankAddress;
  }


  public CarrierBankDto carrierBankPhone(String carrierBankPhone) {

    this.carrierBankPhone = carrierBankPhone;
    return this;
  }

  /**
   * Get carrierBankPhone
   *
   * @return carrierBankPhone
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankPhone() {
    return carrierBankPhone;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_PHONE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankPhone(String carrierBankPhone) {
    this.carrierBankPhone = carrierBankPhone;
  }


  public CarrierBankDto carrierBankFax(String carrierBankFax) {

    this.carrierBankFax = carrierBankFax;
    return this;
  }

  /**
   * Get carrierBankFax
   *
   * @return carrierBankFax
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankFax() {
    return carrierBankFax;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_FAX)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankFax(String carrierBankFax) {
    this.carrierBankFax = carrierBankFax;
  }


  public CarrierBankDto carrierBankAccountType(String carrierBankAccountType) {

    this.carrierBankAccountType = carrierBankAccountType;
    return this;
  }

  /**
   * Get carrierBankAccountType
   *
   * @return carrierBankAccountType
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getCarrierBankAccountType() {
    return carrierBankAccountType;
  }


  @JsonProperty(JSON_PROPERTY_CARRIER_BANK_ACCOUNT_TYPE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCarrierBankAccountType(String carrierBankAccountType) {
    this.carrierBankAccountType = carrierBankAccountType;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierBankDto carrierBankDto = (CarrierBankDto) o;
    return Objects.equals(this.carrierBankRoutingNumber, carrierBankDto.carrierBankRoutingNumber) && Objects.equals(this.carrierBankAccountNumber,
        carrierBankDto.carrierBankAccountNumber) && Objects.equals(this.carrierBankAccountName, carrierBankDto.carrierBankAccountName) && Objects.equals(
        this.carrierBankName, carrierBankDto.carrierBankName) && Objects.equals(this.carrierBankAddress, carrierBankDto.carrierBankAddress) && Objects.equals(
        this.carrierBankPhone, carrierBankDto.carrierBankPhone) && Objects.equals(this.carrierBankFax, carrierBankDto.carrierBankFax) && Objects.equals(
        this.carrierBankAccountType, carrierBankDto.carrierBankAccountType);
  }

  @Override
  public int hashCode() {
    return Objects.hash(carrierBankRoutingNumber, carrierBankAccountNumber, carrierBankAccountName, carrierBankName, carrierBankAddress, carrierBankPhone,
        carrierBankFax, carrierBankAccountType);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierBankDto {\n");
    sb.append("    carrierBankRoutingNumber: ").append(toIndentedString(carrierBankRoutingNumber)).append("\n");
    sb.append("    carrierBankAccountNumber: ").append(toIndentedString(carrierBankAccountNumber)).append("\n");
    sb.append("    carrierBankAccountName: ").append(toIndentedString(carrierBankAccountName)).append("\n");
    sb.append("    carrierBankName: ").append(toIndentedString(carrierBankName)).append("\n");
    sb.append("    carrierBankAddress: ").append(toIndentedString(carrierBankAddress)).append("\n");
    sb.append("    carrierBankPhone: ").append(toIndentedString(carrierBankPhone)).append("\n");
    sb.append("    carrierBankFax: ").append(toIndentedString(carrierBankFax)).append("\n");
    sb.append("    carrierBankAccountType: ").append(toIndentedString(carrierBankAccountType)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

