package com.bulkloads.web.mcp.ext;

import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CarrierTruckClassDto
 */
@JsonPropertyOrder({CarrierTruckClassDto.JSON_PROPERTY_CONESTOGA, CarrierTruckClassDto.JSON_PROPERTY_CONTAINERS,
    CarrierTruckClassDto.JSON_PROPERTY_DECKS_SPECIALIZED, CarrierTruckClassDto.JSON_PROPERTY_DECKS_STANDARD, CarrierTruckClassDto.JSON_PROPERTY_DRY_BULK,
    CarrierTruckClassDto.JSON_PROPERTY_FLATBEDS, CarrierTruckClassDto.JSON_PROPERTY_HAZARDOUS_MATERIALS, CarrierTruckClassDto.JSON_PROPERTY_REEFERS,
    CarrierTruckClassDto.JSON_PROPERTY_TANKERS, CarrierTruckClassDto.JSON_PROPERTY_VANS_SPECIALIZED, CarrierTruckClassDto.JSON_PROPERTY_VANS_STANDARD,
    CarrierTruckClassDto.JSON_PROPERTY_OTHER})

public class CarrierTruckClassDto {

  public static final String JSON_PROPERTY_CONESTOGA = "Conestoga";
  public static final String JSON_PROPERTY_CONTAINERS = "Containers";
  public static final String JSON_PROPERTY_DECKS_SPECIALIZED = "DecksSpecialized";
  public static final String JSON_PROPERTY_DECKS_STANDARD = "DecksStandard";
  public static final String JSON_PROPERTY_DRY_BULK = "DryBulk";
  public static final String JSON_PROPERTY_FLATBEDS = "Flatbeds";
  public static final String JSON_PROPERTY_HAZARDOUS_MATERIALS = "HazardousMaterials";
  public static final String JSON_PROPERTY_REEFERS = "Reefers";
  public static final String JSON_PROPERTY_TANKERS = "Tankers";
  public static final String JSON_PROPERTY_VANS_SPECIALIZED = "VansSpecialized";
  public static final String JSON_PROPERTY_VANS_STANDARD = "VansStandard";
  public static final String JSON_PROPERTY_OTHER = "Other";
  private Boolean conestoga;
  private Boolean containers;
  private Boolean decksSpecialized;
  private Boolean decksStandard;
  private Boolean dryBulk;
  private Boolean flatbeds;
  private Boolean hazardousMaterials;
  private Boolean reefers;
  private Boolean tankers;
  private Boolean vansSpecialized;
  private Boolean vansStandard;
  private String other;

  public CarrierTruckClassDto() {
  }

  public CarrierTruckClassDto conestoga(Boolean conestoga) {

    this.conestoga = conestoga;
    return this;
  }

  /**
   * Get conestoga
   *
   * @return conestoga
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getConestoga() {
    return conestoga;
  }


  @JsonProperty(JSON_PROPERTY_CONESTOGA)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setConestoga(Boolean conestoga) {
    this.conestoga = conestoga;
  }


  public CarrierTruckClassDto containers(Boolean containers) {

    this.containers = containers;
    return this;
  }

  /**
   * Get containers
   *
   * @return containers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getContainers() {
    return containers;
  }


  @JsonProperty(JSON_PROPERTY_CONTAINERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setContainers(Boolean containers) {
    this.containers = containers;
  }


  public CarrierTruckClassDto decksSpecialized(Boolean decksSpecialized) {

    this.decksSpecialized = decksSpecialized;
    return this;
  }

  /**
   * Get decksSpecialized
   *
   * @return decksSpecialized
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECKS_SPECIALIZED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDecksSpecialized() {
    return decksSpecialized;
  }


  @JsonProperty(JSON_PROPERTY_DECKS_SPECIALIZED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecksSpecialized(Boolean decksSpecialized) {
    this.decksSpecialized = decksSpecialized;
  }


  public CarrierTruckClassDto decksStandard(Boolean decksStandard) {

    this.decksStandard = decksStandard;
    return this;
  }

  /**
   * Get decksStandard
   *
   * @return decksStandard
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DECKS_STANDARD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDecksStandard() {
    return decksStandard;
  }


  @JsonProperty(JSON_PROPERTY_DECKS_STANDARD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDecksStandard(Boolean decksStandard) {
    this.decksStandard = decksStandard;
  }


  public CarrierTruckClassDto dryBulk(Boolean dryBulk) {

    this.dryBulk = dryBulk;
    return this;
  }

  /**
   * Get dryBulk
   *
   * @return dryBulk
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getDryBulk() {
    return dryBulk;
  }


  @JsonProperty(JSON_PROPERTY_DRY_BULK)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDryBulk(Boolean dryBulk) {
    this.dryBulk = dryBulk;
  }


  public CarrierTruckClassDto flatbeds(Boolean flatbeds) {

    this.flatbeds = flatbeds;
    return this;
  }

  /**
   * Get flatbeds
   *
   * @return flatbeds
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_FLATBEDS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getFlatbeds() {
    return flatbeds;
  }


  @JsonProperty(JSON_PROPERTY_FLATBEDS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setFlatbeds(Boolean flatbeds) {
    this.flatbeds = flatbeds;
  }


  public CarrierTruckClassDto hazardousMaterials(Boolean hazardousMaterials) {

    this.hazardousMaterials = hazardousMaterials;
    return this;
  }

  /**
   * Get hazardousMaterials
   *
   * @return hazardousMaterials
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_HAZARDOUS_MATERIALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getHazardousMaterials() {
    return hazardousMaterials;
  }


  @JsonProperty(JSON_PROPERTY_HAZARDOUS_MATERIALS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setHazardousMaterials(Boolean hazardousMaterials) {
    this.hazardousMaterials = hazardousMaterials;
  }


  public CarrierTruckClassDto reefers(Boolean reefers) {

    this.reefers = reefers;
    return this;
  }

  /**
   * Get reefers
   *
   * @return reefers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_REEFERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getReefers() {
    return reefers;
  }


  @JsonProperty(JSON_PROPERTY_REEFERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setReefers(Boolean reefers) {
    this.reefers = reefers;
  }


  public CarrierTruckClassDto tankers(Boolean tankers) {

    this.tankers = tankers;
    return this;
  }

  /**
   * Get tankers
   *
   * @return tankers
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_TANKERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getTankers() {
    return tankers;
  }


  @JsonProperty(JSON_PROPERTY_TANKERS)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setTankers(Boolean tankers) {
    this.tankers = tankers;
  }


  public CarrierTruckClassDto vansSpecialized(Boolean vansSpecialized) {

    this.vansSpecialized = vansSpecialized;
    return this;
  }

  /**
   * Get vansSpecialized
   *
   * @return vansSpecialized
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VANS_SPECIALIZED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVansSpecialized() {
    return vansSpecialized;
  }


  @JsonProperty(JSON_PROPERTY_VANS_SPECIALIZED)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVansSpecialized(Boolean vansSpecialized) {
    this.vansSpecialized = vansSpecialized;
  }


  public CarrierTruckClassDto vansStandard(Boolean vansStandard) {

    this.vansStandard = vansStandard;
    return this;
  }

  /**
   * Get vansStandard
   *
   * @return vansStandard
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_VANS_STANDARD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Boolean getVansStandard() {
    return vansStandard;
  }


  @JsonProperty(JSON_PROPERTY_VANS_STANDARD)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setVansStandard(Boolean vansStandard) {
    this.vansStandard = vansStandard;
  }


  public CarrierTruckClassDto other(String other) {

    this.other = other;
    return this;
  }

  /**
   * Get other
   *
   * @return other
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getOther() {
    return other;
  }


  @JsonProperty(JSON_PROPERTY_OTHER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setOther(String other) {
    this.other = other;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CarrierTruckClassDto carrierTruckClassDto = (CarrierTruckClassDto) o;
    return Objects.equals(this.conestoga, carrierTruckClassDto.conestoga) && Objects.equals(this.containers, carrierTruckClassDto.containers) && Objects.equals(
        this.decksSpecialized, carrierTruckClassDto.decksSpecialized) && Objects.equals(this.decksStandard, carrierTruckClassDto.decksStandard)
           && Objects.equals(this.dryBulk, carrierTruckClassDto.dryBulk) && Objects.equals(this.flatbeds, carrierTruckClassDto.flatbeds) && Objects.equals(
        this.hazardousMaterials, carrierTruckClassDto.hazardousMaterials) && Objects.equals(this.reefers, carrierTruckClassDto.reefers) && Objects.equals(
        this.tankers, carrierTruckClassDto.tankers) && Objects.equals(this.vansSpecialized, carrierTruckClassDto.vansSpecialized) && Objects.equals(
        this.vansStandard, carrierTruckClassDto.vansStandard) && Objects.equals(this.other, carrierTruckClassDto.other);
  }

  @Override
  public int hashCode() {
    return Objects.hash(conestoga, containers, decksSpecialized, decksStandard, dryBulk, flatbeds, hazardousMaterials, reefers, tankers, vansSpecialized,
        vansStandard, other);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CarrierTruckClassDto {\n");
    sb.append("    conestoga: ").append(toIndentedString(conestoga)).append("\n");
    sb.append("    containers: ").append(toIndentedString(containers)).append("\n");
    sb.append("    decksSpecialized: ").append(toIndentedString(decksSpecialized)).append("\n");
    sb.append("    decksStandard: ").append(toIndentedString(decksStandard)).append("\n");
    sb.append("    dryBulk: ").append(toIndentedString(dryBulk)).append("\n");
    sb.append("    flatbeds: ").append(toIndentedString(flatbeds)).append("\n");
    sb.append("    hazardousMaterials: ").append(toIndentedString(hazardousMaterials)).append("\n");
    sb.append("    reefers: ").append(toIndentedString(reefers)).append("\n");
    sb.append("    tankers: ").append(toIndentedString(tankers)).append("\n");
    sb.append("    vansSpecialized: ").append(toIndentedString(vansSpecialized)).append("\n");
    sb.append("    vansStandard: ").append(toIndentedString(vansStandard)).append("\n");
    sb.append("    other: ").append(toIndentedString(other)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

