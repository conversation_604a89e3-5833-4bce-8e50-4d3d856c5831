package com.bulkloads.web.mcp.ext;

import java.time.LocalDateTime;
import java.util.Objects;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

/**
 * CompletedPacketDto
 */
@JsonPropertyOrder({CompletedPacketDto.JSON_PROPERTY_DO_T_NUMBER, CompletedPacketDto.JSON_PROPERTY_DOCKET_NUMBER, CompletedPacketDto.JSON_PROPERTY_LEGAL_NAME,
    CompletedPacketDto.JSON_PROPERTY_COMPLETED_DATE})

public class CompletedPacketDto {

  public static final String JSON_PROPERTY_DO_T_NUMBER = "DOTNumber";
  public static final String JSON_PROPERTY_DOCKET_NUMBER = "DocketNumber";
  public static final String JSON_PROPERTY_LEGAL_NAME = "LegalName";
  public static final String JSON_PROPERTY_COMPLETED_DATE = "CompletedDate";
  private Integer doTNumber;
  private String docketNumber;
  private String legalName;
  private LocalDateTime completedDate;

  public CompletedPacketDto() {
  }

  public CompletedPacketDto doTNumber(Integer doTNumber) {

    this.doTNumber = doTNumber;
    return this;
  }

  /**
   * Get doTNumber
   *
   * @return doTNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public Integer getDoTNumber() {
    return doTNumber;
  }


  @JsonProperty(JSON_PROPERTY_DO_T_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDoTNumber(Integer doTNumber) {
    this.doTNumber = doTNumber;
  }


  public CompletedPacketDto docketNumber(String docketNumber) {

    this.docketNumber = docketNumber;
    return this;
  }

  /**
   * Get docketNumber
   *
   * @return docketNumber
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getDocketNumber() {
    return docketNumber;
  }


  @JsonProperty(JSON_PROPERTY_DOCKET_NUMBER)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setDocketNumber(String docketNumber) {
    this.docketNumber = docketNumber;
  }


  public CompletedPacketDto legalName(String legalName) {

    this.legalName = legalName;
    return this;
  }

  /**
   * Get legalName
   *
   * @return legalName
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public String getLegalName() {
    return legalName;
  }


  @JsonProperty(JSON_PROPERTY_LEGAL_NAME)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setLegalName(String legalName) {
    this.legalName = legalName;
  }


  public CompletedPacketDto completedDate(LocalDateTime completedDate) {

    this.completedDate = completedDate;
    return this;
  }

  /**
   * Get completedDate
   *
   * @return completedDate
   **/
  @javax.annotation.Nullable
  @JsonProperty(JSON_PROPERTY_COMPLETED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)

  public LocalDateTime getCompletedDate() {
    return completedDate;
  }


  @JsonProperty(JSON_PROPERTY_COMPLETED_DATE)
  @JsonInclude(value = JsonInclude.Include.USE_DEFAULTS)
  public void setCompletedDate(LocalDateTime completedDate) {
    this.completedDate = completedDate;
  }

  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    CompletedPacketDto completedPacketDto = (CompletedPacketDto) o;
    return Objects.equals(this.doTNumber, completedPacketDto.doTNumber) && Objects.equals(this.docketNumber, completedPacketDto.docketNumber) && Objects.equals(
        this.legalName, completedPacketDto.legalName) && Objects.equals(this.completedDate, completedPacketDto.completedDate);
  }

  @Override
  public int hashCode() {
    return Objects.hash(doTNumber, docketNumber, legalName, completedDate);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class CompletedPacketDto {\n");
    sb.append("    doTNumber: ").append(toIndentedString(doTNumber)).append("\n");
    sb.append("    docketNumber: ").append(toIndentedString(docketNumber)).append("\n");
    sb.append("    legalName: ").append(toIndentedString(legalName)).append("\n");
    sb.append("    completedDate: ").append(toIndentedString(completedDate)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

}

