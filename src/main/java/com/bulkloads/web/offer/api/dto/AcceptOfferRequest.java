package com.bulkloads.web.offer.api.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class AcceptOfferRequest {
  @Schema(description = "Number of loads to accept")
  @Min(value = 1, message = "At least one load must be accepted")
  Integer numberOfLoadsAccepted;
}