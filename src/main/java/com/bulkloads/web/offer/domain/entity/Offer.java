package com.bulkloads.web.offer.domain.entity;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import org.hibernate.annotations.DynamicUpdate;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@DynamicUpdate
@Table(name = "offers")
public class Offer extends AbstractAggregateRoot<Offer> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "offer_id")
  private Integer offerId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull(message = "Τhe load_id is required")
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "load_id")
  private Load load;

  @Column(name = "message")
  private String message = "";

  @Column(name = "offer_rate")
  private BigDecimal offerRate;

  @NotNull(message = "Enter a valid rate")
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "offer_rate_type")
  private RateType offerRateType;

  @Column(name = "offer_rate_per_mile")
  private BigDecimal offerRatePerMile;

  @NotNull
  @Column(name = "offer_date")
  private Instant offerDate;

  @Column(name = "allow_auto_booking")
  private Boolean allowAutoBooking = false;

  @NotNull
  @Size(max = 75)
  @Column(name = "replyto_email")
  private String replytoEmail = "";

  @NotNull
  @Size(max = 500, message = "The subject must be less than 500 characters")
  @Column(name = "subject")
  private String subject = "";

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "offer", fetch = FetchType.LAZY)
  private List<OfferRecipient> recipients = new ArrayList<>();

  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }
}