package com.bulkloads.web.requestlog;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "request_log")
@Getter
@Setter
public class RequestLog {

  @Id
  @Column(name = "request_log_id")
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private int id;

  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "method")
  private String method;

  @Column(name = "url")
  private String url;

  @Column(name = "ip_address")
  private String ipAddress = "";

  @Column(name = "user_agent")
  private String userAgent = "";

  @Column(name = "date")
  private Instant date;

  @Column(name = "api_key_id")
  private Integer apiKeyId;

  @Column(name = "body")
  private String body = "";

  @Column(name = "response_status")
  private int responseStatus;

  @Column(name = "response")
  private String response = "";
}