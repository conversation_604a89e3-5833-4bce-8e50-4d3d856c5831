package com.bulkloads.web.facility.repository;

import java.util.List;
import com.bulkloads.web.facility.domain.entity.FacilityWaitTime;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface FacilityWaitTimeRepository extends JpaRepository<FacilityWaitTime, Integer>, FacilityWaitTimeQueryRepository {

  List<FacilityWaitTime> findByFacilityFacilityId(final int facilityId);

  @Query("SELECT MIN(f.waitDurationMinutes) FROM FacilityWaitTime f WHERE f.facility.facilityId = :facilityId")
  Integer findMinWaitTimeByFacilityId(int facilityId);

  @Query("SELECT MAX(f.waitDurationMinutes) FROM FacilityWaitTime f WHERE f.facility.facilityId = :facilityId")
  Integer findMaxWaitTimeByFacilityId(int facilityId);

  @Query("SELECT MIN(f.waitDurationMinutes) FROM FacilityWaitTime f WHERE f.facility.facilityId = :facilityId AND DATE(f.addedDate) = CURRENT_DATE")
  Integer findMinTodayWaitTimeByFacilityId(int facilityId);

  @Query("SELECT MAX(f.waitDurationMinutes) FROM FacilityWaitTime f WHERE f.facility.facilityId = :facilityId AND DATE(f.addedDate) = CURRENT_DATE")
  Integer findMaxTodayWaitTimeByFacilityId(int facilityId);

}
