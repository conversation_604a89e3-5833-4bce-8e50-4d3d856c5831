package com.bulkloads.web.facility.repository;

import java.time.Instant;

public interface FacilityProjection {
  Integer getFacilityId();

  String getName();

  String getLocation();

  String getCity();

  String getState();

  String getAddress();

  String getZip();

  String getCountry();

  Double getLatitude();

  Double getLongitude();

  String getWebsite();

  String getPhone1();

  String getEmail();

  Integer getTotalReviews();

  Double getAvgRating();

  Integer getViewCount();

  Boolean getPublicRestrooms();

  Boolean getOvernightParking();

  Boolean getDriverLounge();

  Boolean getOnsiteScale();

  Boolean getAppointmentRequired();

  Boolean getWashoutRequired();

  String getHoursOfOperation();

  String getEquipmentIds();

  String getEquipmentNames();

  Integer getWaitTimeCount();

  Integer getMinWaitTime();

  Integer getMaxWaitTime();

  Integer getMinTodayWaitTime();

  Integer getMaxTodayWaitTime();

  Boolean getApproved();

  Instant getUpdatedDate();
}
