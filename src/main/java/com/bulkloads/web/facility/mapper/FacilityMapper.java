package com.bulkloads.web.facility.mapper;

import java.util.List;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.facility.domain.data.FacilityData;
import com.bulkloads.web.facility.domain.entity.Facility;
import com.bulkloads.web.facility.domain.entity.FacilityReview;
import com.bulkloads.web.facility.repository.FacilityFileProjection;
import com.bulkloads.web.facility.repository.FacilityListProjection;
import com.bulkloads.web.facility.repository.FacilityProjection;
import com.bulkloads.web.facility.service.dto.FacilityFileResponse;
import com.bulkloads.web.facility.service.dto.FacilityListResponse;
import com.bulkloads.web.facility.service.dto.FacilityRequest;
import com.bulkloads.web.facility.service.dto.FacilityResponse;
import com.bulkloads.web.facility.service.dto.FacilityReviewResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class FacilityMapper {
  public abstract FacilityData requestToData(final FacilityRequest request);

  public abstract void dataToEntity(final FacilityData data, @MappingTarget final Facility facility);

  public abstract FacilityResponse entityToResponse(final Facility facility);

  public abstract FacilityListResponse mapToListResponse(FacilityListProjection projection);
    
  public abstract List<FacilityListResponse> mapToListResponse(List<FacilityListProjection> projections);

  public abstract FacilityResponse mapToResponse(FacilityProjection projection);

  public abstract FacilityReviewResponse mapToReviewResponse(FacilityReview review);

  public abstract List<FacilityReviewResponse> mapToReviewResponseList(List<FacilityReview> reviews);

  public abstract List<FacilityFileResponse> mapToFileResponseList(List<FacilityFileProjection> files);

  @Mapping(target = "fileId", source = "fileId")
  @Mapping(target = "fileUrl", source = "fileUrl")
  @Mapping(target = "thumbUrl", source = "thumbUrl")
  public abstract FacilityFileResponse mapToFileResponse(FacilityFileProjection file);

}

