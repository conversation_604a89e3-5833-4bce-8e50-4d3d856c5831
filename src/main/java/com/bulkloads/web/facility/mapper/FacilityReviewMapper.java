package com.bulkloads.web.facility.mapper;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.facility.domain.data.FacilityReviewData;
import com.bulkloads.web.facility.domain.entity.Facility;
import com.bulkloads.web.facility.domain.entity.FacilityReview;
import com.bulkloads.web.facility.repository.FacilityRepository;
import com.bulkloads.web.facility.service.dto.FacilityReviewRequest;
import com.bulkloads.web.facility.service.dto.FacilityReviewResponse;
import org.mapstruct.Context;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class FacilityReviewMapper {

  @Autowired
  private FacilityRepository facilityRepository;

  @Mapping(target = "facility", source = "facilityId")
  @Mapping(target = "review", source = "request.review")
  @Mapping(target = "rating", source = "request.rating")
  @Mapping(target = "reviewDate", expression = "java(java.time.Instant.now())")
  public abstract FacilityReviewData requestToData(final int facilityId, final FacilityReviewRequest request, @Context final Map<String, String> errors);

  public abstract void dataToEntity(final FacilityReviewData data, @MappingTarget final FacilityReview facilityReview);

  @Mapping(target = "facilityId", source = "facility.facilityId")
  @Mapping(target = "userId", source = "user.userId")
  @Mapping(target = "firstName", source = "user.firstName")
  @Mapping(target = "lastName", source = "user.lastName")
  public abstract FacilityReviewResponse entityToResponse(final FacilityReview facilityReview);

  public abstract List<FacilityReviewResponse> mapToListResponse(List<FacilityReview> facilityReviews);

  public Facility mapFacilityIdToFacility(final int facilityId, @Context final Map<String, String> errors) {

    final Optional<Facility> facilityOpt = facilityRepository.findById(facilityId);
    if (facilityOpt.isEmpty()) {
      errors.put("facilityId", "The facility with id " + facilityId + " does not exist");
      return null;
    }
    return facilityOpt.get();
  }

}

