package com.bulkloads.web.facility.domain.entity;

import java.time.Instant;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "facility_wait_times")
@Getter
@Setter
public class FacilityWaitTime {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "facility_wait_time_id")
  private Integer facilityWaitTimeId;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "rate_product_category_id")
  private RateProductCategory rateProductCategory;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "facility_id")
  private Facility facility;

  @NotNull(message = "Load or unload type must be specified")
  @Column(name = "load_or_unload")
  private String loadOrUnload;

  @NotNull(message = "Start time cannot be null")
  @Column(name = "start_time")
  private Instant startTime;

  @NotNull(message = "End time cannot be null")
  @Column(name = "end_time")
  private Instant endTime;

  @Column(name = "wait_duration_minutes")
  private Integer waitDurationMinutes;

  @NotNull(message = "Used timer flag cannot be null")
  @Column(name = "used_timer")
  private Boolean usedTimer;

  @NotNull(message = "By appointment flag cannot be null")
  @Column(name = "by_appointment")
  private Boolean byAppointment;

  @NotNull(message = "Comment cannot be null")
  @Column(name = "comment")
  private String comment;

  @Column(name = "added_date")
  private Instant addedDate;

  @Column(name = "added_by_user_id")
  private Integer addedByUserId;

}
