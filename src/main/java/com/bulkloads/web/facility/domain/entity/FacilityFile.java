package com.bulkloads.web.facility.domain.entity;

import com.bulkloads.web.file.domain.entity.File;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "facility_files")
public class FacilityFile {

  @EmbeddedId
  private FacilityFileId id;

  @MapsId("facilityId")
  @ManyToOne
  @JoinColumn(name = "facility_id", nullable = false)
  private Facility facility;

  @MapsId("fileId")
  @ManyToOne
  @JoinColumn(name = "file_id", nullable = false)
  private File file;

  @Column(name = "approved", nullable = false)
  private Boolean approved = false;
}