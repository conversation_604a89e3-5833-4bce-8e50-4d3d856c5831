package com.bulkloads.web.facility.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "facility_suggest_an_edit")
@Getter
@Setter
public class FacilitySuggestAnEdit {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "facility_suggest_an_edit_id")
  private Integer facilitySuggestAnEditId;

  @Column(name = "user_id", nullable = false)
  private Integer userId;

  @Column(name = "facility_id", nullable = false)
  private Integer facilityId;

  @Column(name = "comment", nullable = false, columnDefinition = "TEXT")
  private String comment;

  @Column(name = "date_added", nullable = false)
  private Instant dateAdded;

  @Column(name = "deleted", nullable = false)
  private Boolean deleted = false;
}