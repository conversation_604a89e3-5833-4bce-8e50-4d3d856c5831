package com.bulkloads.web.facility.domain;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.facility.domain.data.FacilityWaitTimeData;
import com.bulkloads.web.facility.domain.entity.FacilityWaitTime;
import com.bulkloads.web.facility.mapper.FacilityWaitTimeMapper;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class FacilityWaitTimeDomainService extends BaseDomainService<FacilityWaitTime> {

  private final FacilityWaitTimeMapper facilityWaitTimeMapper;
  private final UserService userService;

  public Result<FacilityWaitTime> create(FacilityWaitTimeData data) {
    final FacilityWaitTime facilityWaitTime = new FacilityWaitTime();
    
    // Calculate wait duration in minutes from start and end time
    if (data.getStartTime() != null && data.getEndTime() != null) {
      long waitDurationMinutes = ChronoUnit.MINUTES.between(data.getStartTime(), data.getEndTime());
      data.setWaitDurationMinutes((int) waitDurationMinutes);
    }

    return super.validate(facilityWaitTime, null, data, ValidationMethod.CREATE);
  }

  @Override
  public void validateDataAndMapToEntity(Result<FacilityWaitTime> result, FacilityWaitTime entity,
                                         FacilityWaitTime existing, Object facilityWaitTimeData, ValidationMethod method) {
    FacilityWaitTimeData data = (FacilityWaitTimeData) facilityWaitTimeData;

    final User user = userService.getLoggedInUser();
    final Instant now = Instant.now();
    entity.setAddedByUserId(user.getUserId());
    entity.setAddedDate(now);

  }

  public void mapToEntityAuto(Object data, FacilityWaitTime entity) {
    facilityWaitTimeMapper.dataToEntity((FacilityWaitTimeData) data, entity);
  }

  @Override
  public void validateEntity(Result<FacilityWaitTime> result, FacilityWaitTime entity) {
    if (entity.getStartTime() != null && entity.getEndTime() != null) {
      if (entity.getEndTime().isBefore(entity.getStartTime())) {
        result.addError("end_time", "End time cannot be before start time");
      }
    }
  }


}
