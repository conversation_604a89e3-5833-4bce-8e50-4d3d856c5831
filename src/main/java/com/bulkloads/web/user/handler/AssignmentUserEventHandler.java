package com.bulkloads.web.user.handler;

import static java.util.Objects.nonNull;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingEvent;
import com.bulkloads.web.assignment.event.AssignmentCreatedEvent;
import com.bulkloads.web.assignment.event.AssignmentUpdatedEvent;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.repository.UserRepository;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AssignmentUserEventHandler {

  private final UserRepository userRepository;
  private final AssignmentRepository assignmentRepository;

  @TransactionalEventListener(classes = {
      AssignmentCreatedEvent.class,
      AssignmentUpdatedEvent.class
  }, phase = TransactionPhase.BEFORE_COMMIT)
  public void handleCompanyEquipmentAssignmentEvent(final AssignmentBookingEvent event) {
    final Assignment assignment = assignmentRepository.getReferenceById(event.getLoadAssignmentIds().get(0));
    final User toUser = assignment.getToUser();
    if (nonNull(toUser)) {
      final int userId = toUser.getUserId();
      final User user = userRepository.getReferenceById(userId);

      user.setLastTrailerUserCompanyEquipment(assignment.getTrailerUserCompanyEquipment());
      user.setLastTruckUserCompanyEquipment(assignment.getTruckUserCompanyEquipment());
      userRepository.save(user);
    }
  }
}
