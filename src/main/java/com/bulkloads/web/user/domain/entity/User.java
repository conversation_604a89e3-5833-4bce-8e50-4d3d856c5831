package com.bulkloads.web.user.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.load.domain.entity.Load;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Entity
@Table(name = "user_info")
@Getter
@Setter
public class User {

  public static final String DEFAULT_AVATAR_FILE_URL = "https://s3.amazonaws.com/cdn.bulkloads.com/user_files/profile/thumbs/default.png";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "user_id")
  private Integer userId;

  @Column(name = "external_user_id")
  private String externalUserId = "";

  @Column(name = "google_id")
  private String googleId = "";

  @Column(name = "google_email")
  private String googleEmail = "";

  @Column(name = "facebook_id")
  private String facebookId = "";

  @Column(name = "facebook_email")
  private String facebookEmail = "";

  @Column(name = "first_name")
  private String firstName = "";

  @Column(name = "last_name")
  private String lastName = "";

  @Column(name = "cell_phone")
  private String cellPhone = "";

  @Column(name = "cell_phone_verified")
  private Boolean cellPhoneVerified = false;

  @Column(name = "cell_phone_hidden")
  private Boolean cellPhoneHidden = false;

  @Column(name = "user_phone_1")
  private String userPhone1 = "";

  @Column(name = "user_phone_1_type")
  private String userPhone1Type = "";

  @Column(name = "user_phone_1_hidden")
  private Boolean userPhone1Hidden = false;

  @Column(name = "user_phone_2")
  private String userPhone2 = "";

  @Column(name = "user_phone_2_type")
  private String userPhone2Type = "";

  @Column(name = "user_phone_2_hidden")
  private Boolean userPhone2Hidden = false;

  @Column(name = "phone_1")
  private String phone1 = "";

  @Column(name = "phone_1_type")
  private String phone1Type = "";

  @Column(name = "phone_1_hidden")
  private Boolean phone1Hidden = false;

  @Column(name = "phone_2")
  private String phone2 = "";

  @Column(name = "phone_2_type")
  private String phone2Type = "";

  @Column(name = "phone_2_hidden")
  private Boolean phone2Hidden = false;

  @Column(name = "fax")
  private String fax = "";

  @Column(name = "fax_hidden")
  private Boolean faxHidden = false;

  @Column(name = "email")
  private String email = "";

  @Column(name = "email_verified")
  private Boolean emailVerified = false;

  @Column(name = "username")
  private String username = "";

  @Column(name = "old_username")
  private String oldUsername = "";

  @Column(name = "password_hash")
  private String passwordHash = "";

  @Column(name = "password_lowercase")
  private Boolean passwordLowercase = false;

  @Column(name = "sign_up_date")
  private Instant signUpDate = Instant.now();

  @Column(name = "ratings_private")
  private String ratingsPrivate = "0";

  @Column(name = "chat_name")
  private String chatName = "";

  @Column(name = "avatar_small")
  private String avatarSmall = "default.png";

  @Column(name = "avatar_large")
  private String avatarLarge = "";

  @Column(name = "avatar_size")
  private Integer avatarSize;

  @Column(name = "avatar_extension")
  private String avatarExtension = "";

  @Column(name = "avatar_mime_type")
  private String avatarMimeType = "";

  @Column(name = "avatar_width")
  private int avatarWidth = 0;

  @Column(name = "avatar_height")
  private int avatarHeight = 0;

  @Column(name = "avatar_updated")
  private Instant avatarUpdated;

  @Column(name = "heard_about_us")
  private String heardAboutUs = "";

  @Column(name = "deletion_date")
  private Instant deletionDate;

  @Column(name = "country")
  private String country = "";

  @Column(name = "state")
  private String state = "";

  @Column(name = "city")
  private String city = "";

  @Column(name = "address")
  private String address = "";

  @Column(name = "zip")
  private String zip = "";

  @Column(name = "longitude")
  private Double longitude;

  @Column(name = "latitude")
  private Double latitude;

  @Column(name = "dst")
  private Boolean dst;

  @Column(name = "timezone_offset")
  private Integer timezoneOffset;

  @Column(name = "timezone_id")
  private String timezoneId = "";

  @Column(name = "current_city")
  private String currentCity = "";

  @Column(name = "current_state")
  private String currentState = "";

  @Column(name = "current_zip")
  private String currentZip = "";

  @Column(name = "current_country")
  private String currentCountry = "";

  @Column(name = "current_address")
  private String currentAddress = "";

  @Column(name = "current_latitude")
  private Double currentLatitude;

  @Column(name = "current_longitude")
  private Double currentLongitude;

  @Column(name = "current_dst")
  private Boolean currentDst;

  @Column(name = "geo_latitude")
  private Double geoLatitude;

  @Column(name = "geo_longitude")
  private Double geoLongitude;

  @Column(name = "geo_accuracy")
  private Double geoAccuracy;

  @Column(name = "geo_speed")
  private Double geoSpeed;

  @Column(name = "geo_heading")
  private Double geoHeading;

  @Column(name = "geo_altitude")
  private Double geoAltitude;

  @Column(name = "geo_updated_date")
  private Instant geoUpdatedDate;

  @Column(name = "current_timezone_offset")
  private Integer currentTimezoneOffset;

  @Column(name = "current_timezone_id")
  private String currentTimezoneId = "";

  @Column(name = "mailing_address")
  private String mailingAddress = "";

  @Column(name = "company_notes")
  private String companyNotes = "";

  @Column(name = "website")
  private String website = "";

  @Column(name = "sign_up_site_id")
  private int signUpSiteId = 1;

  @Column(name = "comment_count")
  private Integer commentCount = 0;

  @Column(name = "sb_old_member_id")
  private Integer sbOldMemberId;

  @Column(name = "sb_email_sent")
  private Integer sbEmailSent = 0;

  @Column(name = "source")
  private String source = "";

  @Column(name = "source_id")
  private String sourceId = "";

  @Column(name = "bad_email_date")
  private Instant badEmailDate;

  @Column(name = "bad_email_reason")
  private String badEmailReason = "";

  @Column(name = "bad_email_drop_count")
  private Integer badEmailDropCount;

  @Column(name = "bad_email_latest_drop_date")
  private Instant badEmailLatestDropDate;

  @Column(name = "bad_email_marked_spam_date")
  private Instant badEmailMarkedSpamDate;

  @Column(name = "trip_length")
  private String tripLength = "";

  @Column(name = "haul_out_of_state")
  private Boolean haulOutOfState;

  @Column(name = "commercially_licensed")
  private Boolean commerciallyLicensed;

  @Column(name = "hazmat_license")
  private Boolean hazmatLicense;

  @Column(name = "haul_in_canada")
  private Boolean haulInCanada;

  @Column(name = "cc_avatar")
  private String ccAvatar = DEFAULT_AVATAR_FILE_URL;

  @Column(name = "password_changed_date")
  private Instant passwordChangedDate;

  // TODO: maybe better would be to populate them, and add a hook on save

  //  @CsvListSize(max = 100)
  //  @Convert(converter = CsvIntegerListConverter.class)
  //  @Column(name = "user_role_ids")
  //  private List<Integer> userRoleIds = "";
  //
  //  @CsvListSize(max = 200)
  //  @Convert(converter = CsvStringListConverter.class)
  //  @Column(name = "user_roles")
  //  private List<String> userRoles = "";

  @Column(name = "default_driver_rate")
  private Double defaultDriverRate;

  @Column(name = "default_driver_rate_type")
  private String defaultDriverRateType = "";

  @Column(name = "invoice_footer_notes")
  private String invoiceFooterNotes = "";

  @CsvListSize(max = 100)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "user_group_ids")
  private List<Integer> userGroupIds = new ArrayList<>();

  @CsvListSize(max = 200)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "user_groups")
  private List<String> userGroups = new ArrayList<>();

  @Column(name = "geo_tracking_until")
  private Instant geoTrackingUntil;

  @Column(name = "geo_tracking_load_assignment_id")
  private Integer geoTrackingLoadAssignmentId;

  @Column(name = "sff_id")
  private String sffId = "";

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "default_bill_to_ab_company_id")
  private AbCompany defaultBillToAbCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "default_bill_to_ab_user_id")
  private AbUser defaultBillToAbUser;

  @Column(name = "accounting_email")
  private String accountingEmail = "";

  @Column(name = "accounting_email_verified")
  private Boolean accountingEmailVerified = false;

  @Column(name = "accounting_email_cc_me")
  private Boolean accountingEmailCcMe = true;

  @Column(name = "load_confirmation_footer")
  private String loadConfirmationFooter = "";

  @ManyToMany(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.PERSIST, CascadeType.REFRESH})
  @JoinTable(name = "user_roles_ref",
      joinColumns = @JoinColumn(name = "user_id"),
      inverseJoinColumns = @JoinColumn(name = "user_role_id"))
  private List<UserRole> userRoles;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "user", fetch = FetchType.LAZY)
  private List<AbUser> abUsers = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "user", fetch = FetchType.LAZY)
  private List<AbCompany> abCompanies = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "defaultAssignedUser", fetch = FetchType.LAZY)
  private List<UserCompanyEquipment> userCompanyEquipments = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "user", fetch = FetchType.LAZY)
  private List<Load> loads = new ArrayList<>();

  @OneToOne(cascade = CascadeType.ALL, mappedBy = "user")
  private BlUserSettings blUserSettings;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "last_truck_user_company_equipment_id")
  private UserCompanyEquipment lastTruckUserCompanyEquipment;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "last_trailer_user_company_equipment_id")
  private UserCompanyEquipment lastTrailerUserCompanyEquipment;

  public boolean isSiteAdmin() {
    final UserCompany comp = getUserCompany();

    if (comp == null) {
      throw new IllegalStateException("UserCompany should not be missing");
    }
    return comp.isSiteAdmin();
  }

  public boolean hasType(String type) {
    UserCompany comp = getUserCompany();

    if (comp == null) {
      throw new IllegalStateException("UserCompany should not be missing");
    }
    return comp.hasType(type);
  }

  public boolean hasRole(String role) {
    if (getUserRoles() == null) {
      return false;
    }
    return getUserRoles().stream().anyMatch(ur -> ur.getUserRole().equalsIgnoreCase(role));
  }

  public Integer getDefaultBillToCompanyId() {
    var billAbUser = getDefaultBillToAbUser();
    if (billAbUser != null) {
      User blUser = billAbUser.getBlUser();
      if (blUser != null) {
        return blUser.getUserCompany().getUserCompanyId();
      }
    }
    return null;
  }

  public String getLocation() {
    String location = String.format("%s, %s %s", city, state, zip).trim();
    if (!country.equalsIgnoreCase("US") && !country.equalsIgnoreCase("USA")) {
      location = String.format("%s, %s", location, country).trim();
    }
    return location;
  }

  public boolean isPro() {
    if (userCompany == null || userCompany.getUserCompanySettings() == null) {
      return false;
    }
    return userCompany.getUserCompanySettings().isPro();
  }
}
