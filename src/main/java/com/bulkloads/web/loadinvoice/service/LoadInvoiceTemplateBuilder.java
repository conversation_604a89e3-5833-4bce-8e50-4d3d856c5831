package com.bulkloads.web.loadinvoice.service;

import static com.bulkloads.common.validation.ValidationUtils.isEmpty;
import static com.bulkloads.config.AppConstants.Templates.LOAD_INVOICE_TEMPLATE;
import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.web.infra.template.TemplateService;
import com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceItemTemplateModel;
import com.bulkloads.web.loadinvoice.domain.template.LoadInvoiceTemplateModel;
import org.springframework.stereotype.Service;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import jakarta.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoadInvoiceTemplateBuilder {

  private final Validator validator;
  private final TemplateService templateService;

  public void validate(final LoadInvoiceTemplateModel invoice) {
    Objects.requireNonNull(invoice, "LoadInvoiceTemplateModel must not be null");
    Set<ConstraintViolation<LoadInvoiceTemplateModel>> violations = validator.validate(invoice);

    if (!violations.isEmpty()) {
      String message = violations.stream()
          .map(violation -> "Invalid value '" + violation.getInvalidValue()
                            + "' for " + violation.getPropertyPath()
                            + ": " + violation.getMessage())
          .collect(Collectors.joining("\n"));

      throw new ConstraintViolationException("Validation failed for LoadInvoiceTemplateModel:\n" + message, violations);
    }
  }

  protected String getBasicTemplateContent(
      final LoadInvoiceTemplateModel invoice,
      String header,
      String footer,
      boolean isVoid,
      boolean isEmailContent,
      String message) {

    Map<String, Object> modelParams = new HashMap<>();
    modelParams.put("invoice", invoice);
    modelParams.put("header", header);
    modelParams.put("footer", footer);
    modelParams.put("isVoid", isVoid);
    modelParams.put("isEmailContent", isEmailContent);
    modelParams.put("message", message);
    return templateService.processFromTemplateFile(LOAD_INVOICE_TEMPLATE, modelParams);
  }

  public String getLoadInvoiceTemplate(final LoadInvoiceTemplateModel invoice,
                                       String header,
                                       String footer,
                                       boolean isVoid) {
    validate(invoice);
    return getBasicTemplateContent(invoice, header, footer, isVoid, false, null);
  }

  public String getLoadInvoiceEmailNotificationTitle(final LoadInvoiceTemplateModel invoice, boolean isVoid) {
    validate(invoice);

    List<LoadInvoiceItemTemplateModel> items = invoice.getLoadInvoiceItems();

    long loadAssignmentCount = items
        .stream()
        .filter(item -> !item.getIsSurcharge())
        .distinct()
        .count();

    long uniquePickups = items
        .stream()
        .filter(e -> !isEmpty(e.getPickupCompanyName()) || !isEmpty(e.getPickupCity()))
        .map(LoadInvoiceItemTemplateModel::getPickupLocationHash)
        .distinct()
        .count();

    String pickupLocation;
    if (uniquePickups > 1) {
      pickupLocation = "multiple origins";
    } else {
      LoadInvoiceItemTemplateModel firstItem = items.get(0);
      if (isEmpty(firstItem.getPickupCity())) {
        pickupLocation = firstItem.getPickupCompanyName();
      } else {
        pickupLocation = firstItem.getPickupCity() + ", " + firstItem.getPickupState();
      }
    }

    long uniqueDrops = items.stream()
        .filter(item -> !isEmpty(item.getDropCompanyName()) || !isEmpty(item.getDropCity()))
        .map(LoadInvoiceItemTemplateModel::getDropLocationHash)
        .distinct()
        .count();

    String dropLocation;
    if (uniqueDrops > 1) {
      dropLocation = "multiple destinations";
    } else {
      LoadInvoiceItemTemplateModel firstItem = items.get(0);
      if (isEmpty(firstItem.getDropCity())) {
        dropLocation = firstItem.getDropCompanyName();
      } else {
        dropLocation = firstItem.getDropCity() + ", " + firstItem.getDropState();
      }
    }

    String location = pickupLocation + " to " + dropLocation;
    String numberOfLoads = loadAssignmentCount > 1 ? loadAssignmentCount + " loads, " : "";
    String voided = isVoid ? " VOIDED" : "";

    String hauledDateStr = "";
    if (invoice.getLoadInvoiceItems().get(0).getHauledDate() != null) {
      DateTimeFormatter formatter = DateTimeFormatter.ofPattern("M/d");
      hauledDateStr = items.get(0).getHauledDate().format(formatter);
    }

    String emailTitle = "Inv #" + invoice.getLoadInvoiceId() + voided + ": " + numberOfLoads + location + ", " + hauledDateStr;

    log.trace("Invoice email/notification title: {}", emailTitle);
    return emailTitle;
  }

  public String getLoadInvoiceEmailContent(final LoadInvoiceTemplateModel invoice, String message, boolean isVoid) {
    validate(invoice);

    String invoiceContent = getBasicTemplateContent(invoice, null, null, isVoid, true, message);

    log.trace("Invoice email content: {}", invoiceContent);
    return invoiceContent;
  }

  public String getLoadInvoiceNotificationContent(final LoadInvoiceTemplateModel invoice, String message, boolean isVoid) {
    validate(invoice);

    String notificationContent = "Load Invoice #" + invoice.getLoadInvoiceId() + ",";

    Integer revisedFromLoadInvoiceId = invoice.getRevisedFromLoadInvoiceId();
    Instant emailStatusDate = invoice.getEmailStatusDate();

    if (revisedFromLoadInvoiceId != null && emailStatusDate != null) {
      notificationContent += " (Revised from #" + revisedFromLoadInvoiceId + "),";
    }

    notificationContent += " " + invoice.getFirstName() + invoice.getLastName() + " (" + invoice.getCompanyName() + ")";

    log.trace("Invoice notification content: {}", notificationContent);
    return notificationContent;
  }
}
