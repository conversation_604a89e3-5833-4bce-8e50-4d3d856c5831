package com.bulkloads.web.loadinvoice.service.dto;

import java.math.BigDecimal;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class LoadInvoiceReceivableByCompanyResponse {
  Integer billToAbCompanyId;
  Integer billToUserCompanyId;
  String billToCompanyName;
  BigDecimal billAmount;
  String loadInvoiceIds;
  BigDecimal claimedPay;
  BigDecimal verifiedPay;
  Boolean sent;
  Boolean paid;
}