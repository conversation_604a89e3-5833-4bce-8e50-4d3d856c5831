package com.bulkloads.web.loadinvoice.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceReceivableByCompanyResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadInvoiceReceivableByCompanyResponseTransformer implements TupleTransformer<LoadInvoiceReceivableByCompanyResponse> {

  @Override
  public LoadInvoiceReceivableByCompanyResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return LoadInvoiceReceivableByCompanyResponse.builder()
        .billToAbCompanyId(parts.asInteger("bill_to_ab_company_id"))
        .billToUserCompanyId(parts.asInteger("bill_to_user_company_id"))
        .billToCompanyName(parts.asString("bill_to_company_name"))
        .billAmount(parts.asBigDecimal("bill_amount"))
        .loadInvoiceIds(parts.asString("load_invoice_ids"))
        .claimedPay(parts.asBigDecimal("claimed_pay"))
        .verifiedPay(parts.asBigDecimal("verified_pay"))
        .sent(parts.asBoolean("sent"))
        .paid(parts.asBoolean("paid"))
        .build();
  }
}
