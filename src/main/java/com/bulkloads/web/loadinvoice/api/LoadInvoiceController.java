package com.bulkloads.web.loadinvoice.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceRequest;
import com.bulkloads.web.loadinvoice.service.dto.LoadInvoiceResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import lombok.Value;

@RestController
@RequestMapping("/rest/loads/invoices")
@Tag(name = "Address Book")
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class LoadInvoiceController {

  private final LoadInvoiceService loadInvoiceService;

  @Operation(summary = "Create Load Invoice")
  @PostMapping
  public LoadInvoiceApiResponse create(@Valid @RequestBody LoadInvoiceRequest loadInvoiceRequest) {

    LoadInvoiceResponse loadInvoiceResponse = loadInvoiceService.create(loadInvoiceRequest);

    return LoadInvoiceApiResponse.builder()
        .message("Invoice created")
        .invoiceFileUrl(loadInvoiceResponse.getLoadInvoiceFileUrl())
        .loadInvoiceNumber(String.valueOf(loadInvoiceResponse.getLoadInvoiceId()))
        .build();
  }

  @Value
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class LoadInvoiceApiResponse {

    @Schema(name = "message", requiredMode = Schema.RequiredMode.REQUIRED)
    String message;

    @Schema(name = "invoice_file_url", requiredMode = Schema.RequiredMode.REQUIRED)
    String invoiceFileUrl;

    @Schema(name = "load_invoice_number", requiredMode = Schema.RequiredMode.REQUIRED)
    String loadInvoiceNumber;

  }


  @Operation(summary = "Preview Load Invoice")
  @PostMapping("/preview")
  public LoadInvoicePreviewApiResponse preview(@Valid @RequestBody LoadInvoiceRequest loadInvoiceRequest) {
    String content = loadInvoiceService.preview(loadInvoiceRequest);
    return LoadInvoicePreviewApiResponse.builder()
        .message("Invoice preview")
        .content(content)
        .build();
  }

  @Value
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class LoadInvoicePreviewApiResponse {

    @Schema(name = "message", requiredMode = Schema.RequiredMode.REQUIRED)
    String message;

    @Schema(name = "content", requiredMode = Schema.RequiredMode.REQUIRED)
    String content;
  }


}
