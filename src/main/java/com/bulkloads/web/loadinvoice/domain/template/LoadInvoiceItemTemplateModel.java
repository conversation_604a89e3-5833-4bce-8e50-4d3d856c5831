package com.bulkloads.web.loadinvoice.domain.template;

import static com.bulkloads.common.StringUtil.hoursFormat;
import static com.bulkloads.common.StringUtil.milesFormat;
import static com.bulkloads.common.StringUtil.numberFormat;
import static com.bulkloads.common.StringUtil.volumeFormat;
import static com.bulkloads.common.StringUtil.weightFormat;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import com.bulkloads.web.load.domain.template.AssignmentTemplateModel;
import com.bulkloads.web.rate.domain.template.RateTypeTemplateModel;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;

@Value
@Builder(toBuilder = true)
public class LoadInvoiceItemTemplateModel {

  @NotNull
  AssignmentTemplateModel loadAssignment;

  @NotNull
  String pickupCompanyName;

  String pickupCity;

  String pickupState;

  @NotNull
  String dropCompanyName;

  String dropCity;

  String dropState;

  @NotNull
  String loadAssignmentNumber;

  @NotNull
  String commodity;

  Double loadedWeight;

  Double loadedVolume;

  Double unloadWeight;

  Double unloadVolume;

  String dropNumber;

  String bolNumber;

  String workOrderNumber;

  boolean rerouted;

  Double billWeight;

  Double billVolume;

  BigDecimal billHours;

  BigDecimal billMiles;

  BigDecimal billRate;

  @NotNull
  RateTypeTemplateModel billRateType;

  @NotNull
  String billRateMessage;

  @NotNull
  LocalDate hauledDate;

  String hauledNotes;

  @NotNull
  String itemDescription;

  @NotNull
  BigDecimal itemAmount;

  Boolean isSurcharge;

  String pickupNumber;

  String loadingTicketNumber;

  String unloadingTicketNumber;

  String rerouteCompanyName;

  String rerouteCity;

  String rerouteState;

  String reroutePickupDrop;

  String previousLoadAssignmentNumber;

  String previousBillRateMessage;

  @NotNull
  BigDecimal previousItemAmount;

  public String getPickupLocationHash() {
    String companyName = getPickupCompanyName() != null ? getPickupCompanyName() : "";
    String city = getPickupCity() != null ? getPickupCity() : "";
    String state = getPickupState() != null ? getPickupState() : "";
    return companyName + "|" + city + "|" + state;
  }

  public String getDropLocationHash() {
    String companyName = getDropCompanyName() != null ? getDropCompanyName() : "";
    String city = getDropCity() != null ? getDropCity() : "";
    String state = getDropState() != null ? getDropState() : "";
    return companyName + "|" + city + "|" + state;
  }

  public String calculateQuantityText(Double weight, Double volume) {

    if (weight != null && billRateType.getIsWeight()) {

      BigDecimal q = BigDecimal.valueOf(weight)
          .divide(new BigDecimal(billRateType.getRateType()),
              6, RoundingMode.HALF_UP);
      return numberFormat(q) + " " + billRateType.getRateTypeTextMediumPlural();

    } else if (volume != null && (
        "gallon".equals(billRateType.getRateType())
            || "liter".equals(billRateType.getRateType()))) {
      return numberFormat(BigDecimal.valueOf(volume)) + " " + billRateType.getRateTypeTextMediumPlural();

    } else {
      BigDecimal q = BigDecimal.valueOf(weight);
      return numberFormat(q);
    }
  }

  // TODO: this could belong in the Billable

  public String getBillWeightRateValue() {

    var rt = billRateType.getRateType();
    if (billRateType.getIsWeight()) {
      String units = rt.equals("1000") ? "kg" : "lbs";
      return weightFormat(billWeight) + " " + units;

    } else if (rt.equals("gallon")) {
      return volumeFormat(billVolume) + " " + "gal";

    } else if (rt.equals("liter")) {
      return volumeFormat(billVolume) + " " + "lt";

    } else if (rt.equals("mile")) {
      return milesFormat(billMiles) + " " + "mi";

    } else if (rt.equals("hour")) {
      return hoursFormat(billHours) + " " + "hrs";
    }
    return "";
  }
}
