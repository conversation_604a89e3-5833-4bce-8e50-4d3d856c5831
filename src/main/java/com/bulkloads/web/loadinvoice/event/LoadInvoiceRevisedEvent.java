package com.bulkloads.web.loadinvoice.event;

import static com.bulkloads.config.AppConstants.InvoiceAction.LOAD_INVOICE_REGENERATE;

import java.util.List;
import lombok.Getter;

@Getter
public class LoadInvoiceRevisedEvent extends LoadInvoiceEvent {

  private final String fileUrl;
  private final List<Integer> loadAssignmentIds;
  private final boolean sendLoadInvoice;
  private final String message;
  private final int revisedFromLoadInvoiceId;

  public LoadInvoiceRevisedEvent(final int loadInvoiceId,
                                 final String fileUrl,
                                 final List<Integer> loadAssignmentIds,
                                 final boolean sendLoadInvoice,
                                 final String message,
                                 final int revisedFromLoadInvoiceId) {
    super(loadInvoiceId, LOAD_INVOICE_REGENERATE);
    this.fileUrl = fileUrl;
    this.loadAssignmentIds = loadAssignmentIds;
    this.sendLoadInvoice = sendLoadInvoice;
    this.message = message;
    this.revisedFromLoadInvoiceId = revisedFromLoadInvoiceId;
  }
}
