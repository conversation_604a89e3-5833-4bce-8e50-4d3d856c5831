package com.bulkloads.web.loadinvoice.handler;

import java.util.List;
import com.bulkloads.web.addressbook.abuser.event.AbUserEmailUpdatedEvent;
import com.bulkloads.web.loadinvoice.repository.LoadInvoiceRepository;
import com.bulkloads.web.loadinvoice.service.LoadInvoiceService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AbUserLoadInvoiceEventHandler {

  private final LoadInvoiceRepository loadInvoiceRepository;
  private final LoadInvoiceService loadInvoiceService;

  @TransactionalEventListener(
      classes = AbUserEmailUpdatedEvent.class,
      phase = TransactionPhase.BEFORE_COMMIT
  )
  public void handleAbUserEmailUpdatedEvent(final AbUserEmailUpdatedEvent event) {
    log.info("{}", event);

    List<Integer> invoiceIds = loadInvoiceRepository.findRecentDroppedInvoices(
        event.getUserCompanyId(),
        event.getAbUser());

    invoiceIds.forEach(invoiceId -> {
      log.trace("Updating load_invoice_id={}, setting billToEmail={} ", invoiceId, event.getEmail());

      loadInvoiceRepository.updateBillToEmail(event.getEmail(), invoiceId);
      loadInvoiceService.sendLoadInvoice(invoiceId, "", false);
    });
  }
}
