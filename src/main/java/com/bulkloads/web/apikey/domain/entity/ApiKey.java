package com.bulkloads.web.apikey.domain.entity;

import java.time.Instant;
import java.util.List;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "api_keys")
public class ApiKey {

  @Id
  @Column(name = "api_key_id")
  private Integer id;

  @Size(max = 90)
  @NotNull
  @Column(name = "api_key")
  private String apiKey;

  @Size(max = 45)
  @NotNull
  @Column(name = "app_name")
  private String appName;

  @NotNull
  @Column(name = "enabled")
  private Boolean enabled = false;

  @NotNull
  @Column(name = "quota")
  private Integer quota;

  @NotNull
  @Column(name = "num_of_requests")
  private Integer numOfRequests;

  @Column(name = "valid_until")
  private Instant validUntil;

  @ManyToMany(fetch = FetchType.LAZY)
  @JoinTable(name = "api_key_endpoints",
      joinColumns = @JoinColumn(name = "api_key_id"),
      inverseJoinColumns = @JoinColumn(name = "api_endpoint_id"))
  private List<ApiEndpoint> apiEndpoints;

}