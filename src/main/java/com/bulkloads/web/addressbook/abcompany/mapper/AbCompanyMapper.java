package com.bulkloads.web.addressbook.abcompany.mapper;

import com.bulkloads.web.addressbook.abcompany.domain.data.AbCompanyData;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class AbCompanyMapper {

  public abstract AbCompanyData requestToData(final AbCompanyRequest req);

  @Mapping(target = "abCompanyFiles", ignore = true)
  @Mapping(target = "abUsers", ignore = true)
  public abstract void dataToEntity(final AbCompanyData data, @MappingTarget final AbCompany abCompany);


  // TODO: this might be wrong
  @Mapping(target = "files", ignore = true)
  @Mapping(target = "validateLocation", expression = "java(false)")
  public abstract AbCompanyData entityToData(final AbCompany abCompany);


  @Mapping(target = "abCompanyId", ignore = true)
  @Mapping(target = "user", ignore = true)
  @Mapping(target = "userCompany", ignore = true)
  @Mapping(target = "privateNotes", ignore = true)
  @Mapping(target = "modifiedDate", ignore = true)
  @Mapping(target = "dateAdded", ignore = true)
  public abstract AbCompany clone(final AbCompany abCompany);


}

