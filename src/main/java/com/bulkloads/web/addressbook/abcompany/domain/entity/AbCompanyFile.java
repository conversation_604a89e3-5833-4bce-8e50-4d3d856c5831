package com.bulkloads.web.addressbook.abcompany.domain.entity;

import java.time.Instant;
import com.bulkloads.web.file.domain.entity.File;
import jakarta.persistence.Column;
import jakarta.persistence.EmbeddedId;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MapsId;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "ab_company_files")
public class AbCompanyFile {

  @EmbeddedId
  private AbCompanyFileId abCompanyFileId;

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @Column(name = "file_order")
  private int fileOrder = 1;

  @MapsId("abCompanyId")
  @ManyToOne
  @JoinColumn(name = "ab_company_id")
  private AbCompany abCompany;

  @MapsId("fileId")
  @ManyToOne
  @JoinColumn(name = "file_id")
  private File file;

}

