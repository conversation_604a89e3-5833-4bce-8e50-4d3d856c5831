package com.bulkloads.web.addressbook.abcompany.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.common.jackson.CsvSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class AbCompanyListResponse {

  private Integer abCompanyId;
  private String externalAbCompanyId;
  private String externalAbCompanyType;
  private String authCode;
  private String companyName;
  private String companyCode;


  @Schema(type = "string", example = "20,30,40")
  @JsonSerialize(using = CsvSerializer.class)
  private List<Integer> userTypeIds;

  @Schema(type = "string", example = "Carrier,Broker,Shipper")
  @JsonSerialize(using = CsvSerializer.class)
  private List<String> userTypes;

  private Integer userCompanyId;
  private Integer facilityId;
  private Integer censusNum;
  private String mcNum;
  private String companyPhone;
  private String companyEmail;
  private String address;
  private String location;
  private String city;
  private String state;
  private String zip;
  private String country;
  private Double longitude;
  private Double latitude;
  private Boolean mailingSameAsPhysical;
  private String mailingAddress;
  private String mailingLocation;
  private String mailingCity;
  private String mailingState;
  private String mailingZip;
  private String mailingCountry;
  private String insuranceInfo;
  private String companyNotes;
  private Boolean apptRequired;
  private String receivingHours;
  private String directions = "";
  private String privateNotes;
  private String insLiabCompany;
  private String insLiabPolicy;
  private LocalDate insLiabExpDate;
  private String insLiabPhone;
  private String insLiabContact;
  private Integer insLiabAmount;
  private String insLiabNotes;
  private Boolean insWorkSameAsLiab;
  private String insWorkCompany;
  private String insWorkPolicy;
  private LocalDate insWorkExpDate;
  private String insWorkPhone;
  private String insWorkContact;
  private Integer insWorkAmount;
  private String insWorkNotes;
  private Boolean insCargoSameAsLiab;
  private String insCargoCompany;
  private String insCargoPolicy;
  private LocalDate insCargoExpDate;
  private String insCargoPhone;
  private String insCargoContact;
  private Integer insCargoAmount;
  private String insCargoNotes;
  private Integer numberOfFiles;
  private String riskAssessmentOverall;
  private Boolean mcpMonitored;
  private String mcpDetailsUrl;
  private Instant mcpSyncDate;
  private Instant modifiedDate;
  private Boolean active;
  private Boolean deleted;

  private Integer blUserCompanyId;

}
