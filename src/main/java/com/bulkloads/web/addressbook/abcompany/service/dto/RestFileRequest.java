package com.bulkloads.web.addressbook.abcompany.service.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class RestFileRequest {

  @NotNull
  @Schema(name = "file_id", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  Integer fileId;
}
