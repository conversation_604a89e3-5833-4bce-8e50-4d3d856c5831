package com.bulkloads.web.addressbook.abuser.service.dto.transformers;


import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserAutoResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class AbUserAutoResponseTransformer implements TupleTransformer<AbUserAutoResponse> {

  @Override
  public AbUserAutoResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    AbUserAutoResponse response = new AbUserAutoResponse();
    response.setAbCompanyId(parts.asInteger("ab_company_id"));
    response.setCompanyName(parts.asString("company_name"));
    response.setCity(parts.asString("city"));
    response.setState(parts.asString("state"));
    response.setLocation(parts.asString("location"));
    response.setApptRequired(parts.asBoolean("appt_required"));
    response.setAbUserId(parts.asInteger("ab_user_id"));
    response.setFirstName(parts.asString("first_name"));
    response.setLastName(parts.asString("last_name"));
    response.setPhone1(parts.asString("phone_1"));
    response.setEmail(parts.asString("email"));
    response.setPreferredContactMethod(parts.asString("preferred_contact_method"));
    response.setBadEmailDate(parts.asLocalDate("bad_email_date"));
    response.setBadEmailReason(parts.asString("bad_email_reason"));
    response.setBlUserId(parts.asInteger("bl_user_id"));
    response.setAbUserRoleIds(parts.asIntegerListFromCsv("ab_user_role_ids"));
    response.setAbUserRoles(parts.asStringListFromCsv("ab_user_roles"));
    response.setUserCompanyId(parts.asInteger("user_company_id"));
    return response;
  }

}
