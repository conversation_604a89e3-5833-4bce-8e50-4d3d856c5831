package com.bulkloads.web.addressbook.abuser.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import com.bulkloads.web.addressbook.abcompany.service.AbCompanyService;
import com.bulkloads.web.addressbook.abuser.service.AbUserService;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserRequest;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@RestController
@RequiredArgsConstructor
@RequestMapping("/rest/address_book/users")
@Tag(name = "Address Book")
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class AbUserController {

  private final AbUserService abUserService;
  private final AbCompanyService abCompanyService;

  @Data
  @Builder
  @JsonInclude(JsonInclude.Include.NON_NULL)
  public static class AbUserApiResponse {

    String message;
    Integer abUserId;
    Integer abCompanyId;
    Integer key;
    AbUserResponse data;
  }

  @PostMapping
  public AbUserApiResponse create(@RequestBody @Valid AbUserRequest request) {
    AbUserResponse abUserResponse = abUserService.create(request);

    return AbUserApiResponse.builder()
        .key(abUserResponse.getAbUserId())
        .abUserId(abUserResponse.getAbUserId())
        .data(abUserResponse)
        .message("Created successfully")
        .build();
  }

  @PutMapping("{ab_user_id}")
  public AbUserApiResponse update(@PathVariable("ab_user_id") int abUserId, @RequestBody AbUserRequest request) {
    AbUserResponse abUserResponse = abUserService.update(abUserId, request);

    return AbUserApiResponse.builder()
        .key(abUserResponse.getAbUserId())
        .abUserId(abUserResponse.getAbUserId())
        .data(abUserResponse)
        .message("User details updated")
        .build();
  }

  @DeleteMapping("{ab_user_id}")
  public AbUserApiResponse remove(@PathVariable("ab_user_id") int abUserId) {
    abUserService.remove(abUserId);

    return AbUserApiResponse.builder()
        .message("User deleted")
        .build();
  }

  @PostMapping("from_user/{user_id}")
  public AbUserApiResponse createAbUserFromUser(@PathVariable("user_id") int userIdToReplicate) {

    AbUserResponse abUserResponse = abCompanyService.createAbCompanyAndAbUserFromUser(userIdToReplicate);

    return AbUserApiResponse.builder()
        .key(abUserResponse.getAbUserId())
        .abUserId(abUserResponse.getAbUserId())
        .abCompanyId(abUserResponse.getAbCompanyId())
        .data(abUserResponse)
        .message("Created successfully")
        .build();
  }

}
