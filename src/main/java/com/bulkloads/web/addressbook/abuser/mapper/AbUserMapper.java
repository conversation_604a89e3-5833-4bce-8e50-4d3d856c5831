package com.bulkloads.web.addressbook.abuser.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import com.bulkloads.common.Parsers;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.repository.AbCompanyRepository;
import com.bulkloads.web.addressbook.abuser.domain.data.AbUserData;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUserRole;
import com.bulkloads.web.addressbook.abuser.repository.AbUserRoleRepository;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserRequest;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public abstract class AbUserMapper {

  @Autowired
  private AbCompanyRepository abCompanyRepository;
  @Autowired
  private AbUserRoleRepository abUserRoleRepository;

  @Mapping(target = "abCompany", ignore = true)
  @Mapping(source = "abUserRoleIds", target = "abUserRoles")
  @Mapping(target = "validatePhone1", ignore = true)
  public abstract void requestToData(final AbUserRequest req, @MappingTarget final AbUserData abUserData);

  public abstract void dataToEntity(final AbUserData data, @MappingTarget final AbUser abuser);

  protected Optional<AbCompany> mapAbCompanyOptById(final Optional<Integer> abCompanyIdOpt) {
    return abCompanyIdOpt.flatMap(id -> {
      final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
      return abCompanyRepository.findByAbCompanyIdAndUserCompanyUserCompanyId(id, userCompanyId);
    });
  }

  protected Optional<List<AbUserRole>> mapAbUserRoleIdsOptById(final Optional<String> abUserRoleIds) {
    if (abUserRoleIds == null) {
      return null;
    }
    return abUserRoleIds
        .map(s -> abUserRoleRepository.findAllById(Parsers.parseIntegerCsvToList(s)))
        .or(() -> Optional.of(new ArrayList<>()));
  }
}
