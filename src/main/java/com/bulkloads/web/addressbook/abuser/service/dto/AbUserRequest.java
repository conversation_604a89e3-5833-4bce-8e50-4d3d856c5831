package com.bulkloads.web.addressbook.abuser.service.dto;

import java.util.Optional;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AbUserRequest {

  private Optional<@NotNull Integer> abCompanyId;
  private Optional<String> externalAbUserId;
  private Optional<String> firstName;
  private Optional<String> lastName;
  private Optional<String> email;

  @Schema(name = "phone_1", description = "User phone", requiredMode = Schema.RequiredMode.NOT_REQUIRED)
  @JsonProperty("phone_1")
  private Optional<String> phone1;

  private Optional<String> preferredContactMethod;
  private Optional<String> abUserNotes;
  private Optional<String> abUserRoleIds;
}
