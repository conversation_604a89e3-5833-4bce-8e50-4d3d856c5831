package com.bulkloads.web.addressbook.abuser.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.common.jpa.converter.CsvIntegerListConverter;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "ab_users")
@Getter
@Setter
public class AbUser extends AbstractAggregateRoot<AbUser> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ab_user_id")
  private Integer abUserId;

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "external_ab_user_id")
  private String externalAbUserId = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "source")
  private String source = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "source_id")
  private String sourceId = "";

  @CsvListSize(max = 100)
  @Convert(converter = CsvIntegerListConverter.class)
  @Column(name = "ab_user_role_ids")
  private List<Integer> abUserRoleIds = new ArrayList<>();

  @CsvListSize(max = 200)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "ab_user_roles")
  private List<String> abUserRolesValues = new ArrayList<>();

  @ManyToMany(cascade = {CascadeType.DETACH, CascadeType.MERGE, CascadeType.REFRESH})
  @JoinTable(name = "ab_user_roles_ref",
      joinColumns = @JoinColumn(name = "ab_user_id"),
      inverseJoinColumns = @JoinColumn(name = "ab_user_role_id"))
  private List<AbUserRole> abUserRoles = new ArrayList<>();

  @Column(name = "invitation_id")
  private Integer invitationId;

  @Size(max = 30, message = "Enter up to 30 chars")
  @Column(name = "first_name")
  private String firstName = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "last_name")
  private String lastName = "";

  @Email(message = "Enter a valid email")
  @Size(max = 100, message = "The email must be up to 100 chars")
  @Column(name = "email")
  private String email = "";

  @Column(name = "bad_email_date")
  private Instant badEmailDate;

  @Column(name = "bad_email_reason")
  private String badEmailReason = "";

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "phone_1")
  private String phone1 = "";

  @Size(max = 10, message = "Enter up to 10 chars")
  @Column(name = "phone_1_type")
  private String phone1Type = "";

  @Size(max = 25, message = "Enter up to 25 chars")
  @Column(name = "phone_2")
  private String phone2 = "";

  @Size(max = 10, message = "Enter up to 10 chars")
  @Column(name = "phone_2_type")
  private String phone2Type = "";

  @Size(max = 100, message = "Enter up to 100 chars")
  @Column(name = "preferred_contact_method")
  private String preferredContactMethod = "";

  @Column(name = "ab_user_notes")
  private String abUserNotes = "";

  @Column(name = "freq")
  private int freq = 0;

  @Size(max = 50, message = "Enter up to 50 chars")
  @Column(name = "default_geo_request_method")
  private String defaultGeoRequestMethod = "email";

  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "date_deleted")
  private Instant dateDeleted;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "last_truck_user_company_equipment_id")
  private UserCompanyEquipment lastTruckUserCompanyEquipment;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "last_trailer_user_company_equipment_id")
  private UserCompanyEquipment lastTrailerUserCompanyEquipment;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "ab_company_id")
  private AbCompany abCompany;

  @ManyToOne(cascade = {CascadeType.PERSIST, CascadeType.MERGE, CascadeType.DETACH, CascadeType.REFRESH}, fetch = FetchType.LAZY)
  @JoinColumn(name = "bl_user_id")
  private User blUser;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "defaultAssignedAbUser", fetch = FetchType.LAZY)
  private List<UserCompanyEquipment> userCompanyEquipments = new ArrayList<>();


  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }
}
