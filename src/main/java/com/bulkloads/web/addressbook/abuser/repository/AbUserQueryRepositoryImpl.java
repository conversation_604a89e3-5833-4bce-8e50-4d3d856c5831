package com.bulkloads.web.addressbook.abuser.repository;

import static com.bulkloads.common.Converters.instantToSql;
import static com.bulkloads.web.addressbook.abuser.repository.template.GetAbUsersAutoQueryTemplate.GET_AB_USERS_AUTO_QUERY_TEMPLATE;
import static com.bulkloads.web.addressbook.abuser.repository.template.GetAbUsersQueryTemplate.GET_AB_USERS_QUERY_TEMPLATE;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserAutoResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.AbUserTypeResponse;
import com.bulkloads.web.addressbook.abuser.service.dto.transformers.AbUserAutoResponseTransformer;
import com.bulkloads.web.addressbook.abuser.service.dto.transformers.AbUserResponseTransformer;
import org.intellij.lang.annotations.Language;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@Repository
@RequiredArgsConstructor
public class AbUserQueryRepositoryImpl implements AbUserQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final AbUserResponseTransformer abUserResponseTransformer;
  private final AbUserAutoResponseTransformer abUserAutoResponseTransformer;

  public AbUserResponse getAbUser(List<Integer> userCompanyIds, Integer abUserId) {

    Map<String, Object> params = new HashMap<>();
    params.put("userCompanyIds", userCompanyIds);
    params.put("abUserId", abUserId);

    return jpaNativeQueryService.queryForObject(GET_AB_USERS_QUERY_TEMPLATE, params, abUserResponseTransformer);
  }

  @Override
  public List<AbUserResponse> getAbUsersIncludingDeletedAbCompany(List<Integer> userCompanyIds, Integer abCompanyId) {
    Map<String, Object> params = new HashMap<>();
    params.put("userCompanyIds", userCompanyIds);
    params.put("abCompanyId", abCompanyId);
    params.put("includeDeletedAbCompany", true);
    return jpaNativeQueryService.query(GET_AB_USERS_QUERY_TEMPLATE, params, abUserResponseTransformer);
  }


  @Override
  public List<AbUserResponse> getAbUsers(
      List<Integer> userCompanyIds,
      List<Integer> abUserIds,
      String externalAbUserId,
      Integer blUserId,
      Integer abCompanyId,
      Integer abUserGroupId,
      Instant lastModifiedDate,
      boolean includeDeleted,
      Integer skip,
      Integer limit
  ) {

    Map<String, Object> params = new HashMap<>();

    params.put("userCompanyIds", userCompanyIds);
    params.put("abUserIds", abUserIds);
    params.put("externalAbUserId", externalAbUserId);
    params.put("blUserId", blUserId);
    params.put("abCompanyId", abCompanyId);
    params.put("abUserGroupId", abUserGroupId);
    params.put("lastModifiedDate", instantToSql(lastModifiedDate));
    params.put("includeDeleted", includeDeleted);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(GET_AB_USERS_QUERY_TEMPLATE, params, abUserResponseTransformer);
  }

  public List<AbUserAutoResponse> getAbUsersAuto(List<Integer> userCompanyIds, String term, List<Integer> userTypeIds, List<Integer> abUserRoleIds,
      Integer abCompanyId, String order, Integer skip, Integer limit) {

    Map<String, Object> params = new HashMap<>();

    params.put("userCompanyIds", userCompanyIds);
    params.put("term", term);
    params.put("userTypeIds", userTypeIds);
    params.put("abUserRoleIds", abUserRoleIds);
    params.put("abCompanyId", abCompanyId);
    params.put("order", order);
    params.put("skip", skip);
    params.put("limit", limit);

    return jpaNativeQueryService.query(GET_AB_USERS_AUTO_QUERY_TEMPLATE, params, abUserAutoResponseTransformer);
  }


  public List<AbUserTypeResponse> getAbUserTypes() {

    @Language("SQL") String sql = """
          select
              ut.user_type_id,
              ut.user_type
          from user_type ut where user_type_id not in (100)
        """;

    return jpaNativeQueryService.query(sql, new HashMap<>(), (columns, aliases) -> {
      QueryParts parts = new QueryParts(columns, aliases);
      return AbUserTypeResponse.builder().userTypeId(parts.asInteger("user_type_id")).userType(parts.asString("user_type")).build();
    });
  }

}
