package com.bulkloads.web.addressbook.abuser.repository.template;

public interface GetAbUsersQueryTemplate {

  String GET_AB_USERS_QUERY_TEMPLATE = """
      select
        abu.ab_user_id,
        abu.external_ab_user_id,
        abu.ab_company_id,
        abc.external_ab_company_id,
        abu.user_company_id,
        abc.company_name,
        abu.first_name,
        abu.last_name,
        abu.phone_1,
        abu.email,
        abu.bad_email_date,
        abu.bad_email_reason,
        abu.preferred_contact_method,
        abu.ab_user_notes,
        abu.ab_user_role_ids,
        abu.ab_user_roles,
        abu.bl_user_id,
        abc.user_type_ids,
        abc.user_types,
        abc.location,
        abc.active,
        abu.modified_date,
        abu.deleted,
        abu.last_truck_user_company_equipment_id,
        abu.last_trailer_user_company_equipment_id
      
      from ab_users abu
        inner join ab_companies abc using(ab_company_id)
      where 

      <% params.put("userCompanyIds", userCompanyIds) %>
      abc.user_company_id in :userCompanyIds
            
      <% if (paramExistsAdd("abUserId")) { %>
        and abu.ab_user_id = :abUserId
      <% } else { %>
        -- when syncing include deleted
        <% if (paramIsTrue("includeDeletedAbCompany")) { %>
          and abu.deleted = 0          
        <% } else if (!paramIsTrue("includeDeleted")) { %>
          and abu.deleted = 0
          and abc.deleted = 0
        <% } %>
      <% } %>

      <% if (paramExistsAdd("abUserIds")) { %>
        and abu.ab_user_id in (:abUserIds)
      <% } %>
            
      <% if (paramExistsAdd("externalAbUserId")) { %>
        and abu.external_ab_user_id = :externalAbUserId
      <% } %>

      <% if (paramExistsAdd("blUserId")) { %>
        and abu.bl_user_id = :blUserId
      <% } %>
            
      <% if (paramExistsAdd("abCompanyId")) { %>
        and abu.ab_company_id = :abCompanyId
      <% } %>

      <% if (paramExistsAdd("lastModifiedDate")) { %>
        and abu.modified_date >= :lastModifiedDate
      <% } %>

      order by abu.first_name, abu.last_name

      <% if (paramExistsAdd("limit")) { %>
          LIMIT
          <% if (paramExistsAdd("skip")) { %>
          :skip,
          <% } %>
          :limit
      <% } %>
      """;
}
