package com.bulkloads.web.loadalert.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;


@Entity
@Getter
@Setter
@Table(name = "rate_product_categories")
public class RateProductCategories {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "rate_product_category_id")
  private Integer rateProductCategoryId;

  @Column(name = "rate_product_category")
  private String rateProductCategory;

  @ManyToOne
  private LoadAlert loadAlert;
}
