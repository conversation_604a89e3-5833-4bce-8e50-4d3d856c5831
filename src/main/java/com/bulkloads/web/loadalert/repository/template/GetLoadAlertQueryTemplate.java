package com.bulkloads.web.loadalert.repository.template;

import org.intellij.lang.annotations.Language;

public class GetLoadAlertQueryTemplate {

  @Language("SQL")
  public static final String GET_LOAD_ALERTS_QUERY_TEMPLATE = """
        SELECT la.*
        from load_alerts la
        inner join bl_user_settings s ON la.user_id = s.user_id
        WHERE la.deleted = 0
        and s.deletion_date is null

        and la.site_id = 1

        <% if (paramExistsAdd("active")) { %>
        AND la.active = :active
        <% } %>

        <% if (paramIsTrue("send_push")) { %>
        AND la.user_id IN (SELECT user_id FROM user_email_categories WHERE email_category_id = 21 AND send_push = 1 AND site_id = 1)
        <% } %>

        <% if (paramExistsAdd("loadAlertId")) { %>
        AND la.load_alert_id = :loadAlertId
        <% } %>

        <% if (paramExistsAdd("userId")) { %>
        AND la.user_id = :userId
        <% } %>

        <% if (paramExistsAdd("dueTime")) { %>
        AND la.frequency <> 0
        AND INTERVAL(MINUTE, :dueTime, la.last_runs) >= (la.frequency - 1)
        <% } %>

        <% // if all params are blank ignore the rest of the query %>
        <% if (!paramExists("origin_country")
          && !paramExists("origin_state")
          && !paramExists("origin_city")
          && !paramExists("origin_zip")
          && !paramExists("origin_lat")
          && !paramExists("origin_long")
          && !paramExists("origin_radius")
          && !paramExists("destination_country")
          && !paramExists("destination_state")
          && !paramExists("destination_city")
          && !paramExists("destination_zip")
          && !paramExists("destination_lat")
          && !paramExists("destination_long")
          && !paramExists("destination_radius")
          && !paramExists("user_company_ids")
          && !paramExists("equipment_ids")
          && !paramExists("rate_product_category_ids")) { %>

        <% } else { %>

          <% if (paramDefinedAdd("user_company_ids")) { %>
          AND la.user_company_ids = :user_company_ids
          <% } %>
  
          <% if (paramDefinedAdd("origin_country")) { %>
          AND la.origin_country = :origin_country
          <% } %>
  
          <% if (paramDefinedAdd("origin_state")) { %>
          AND la.origin_state = :origin_state
          <% } %>
  
          <% if (paramDefinedAdd("origin_city")) { %>
          AND la.origin_city = :origin_city
          <% } %>
  
          <% if (paramDefinedAdd("origin_zip")) { %>
          AND la.origin_zip = :origin_zip
          <% } %>
  
          <% if (paramDefined("origin_lat")) { %>
            <% if (paramExistsAdd("origin_lat")) { %>
              AND la.origin_lat = :origin_lat
            <% } else { %>
              AND la.origin_lat IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefined("origin_long")) { %>
            <% if (paramExistsAdd("origin_long")) { %>
              AND la.origin_long = :origin_long
            <% } else { %>
              AND la.origin_long IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefined("origin_radius")) { %>
            <% if (paramExistsAdd("origin_radius")) { %>
              AND la.origin_radius = :origin_radius
            <% } else { %>
              AND la.origin_radius IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefinedAdd("destination_country")) { %>
          AND la.destination_country = :destination_country
          <% } %>
  
          <% if (paramDefinedAdd("destination_state")) { %>
          AND la.destination_state = :destination_state
          <% } %>
  
          <% if (paramDefinedAdd("destination_city")) { %>
          AND la.destination_city = :destination_city
          <% } %>
  
          <% if (paramDefinedAdd("destination_zip")) { %>
          AND la.destination_zip = :destination_zip
          <% } %>
  
          <% if (paramDefined("destination_lat")) { %>
            <% if (paramExistsAdd("destination_lat")) { %>
              AND la.destination_lat = :destination_lat
            <% } else { %>
              AND la.destination_lat IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefined("destination_long")) { %>
            <% if (paramExistsAdd("destination_long")) { %>
              AND la.destination_long = :destination_long
            <% } else { %>
              AND la.destination_long IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefined("destination_radius")) { %>
            <% if (paramExistsAdd("destination_radius")) { %>
              AND la.destination_radius = :destination_radius
            <% } else { %>
              AND la.destination_radius IS NULL
            <% } %>
          <% } %>
  
          <% if (paramDefinedAdd("equipment_ids")) { %>
          AND la.equipment_ids = :equipment_ids
          <% } %>
  
          <% if (paramDefinedAdd("rate_product_category_ids")) { %>
              AND la.rate_product_category_ids = :rate_product_category_ids
          <% } %>

        <% } %>

        ORDER BY la.load_alert_id
      """;

}
