package com.bulkloads.web.loadalert.service.dto;

import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class LoadAlertRequest {
  String originCountry;
  String originState;
  String originCity;
  String originZip;
  Double originLat;
  Double originLong;
  Integer originRadius;
  String destinationCountry;
  String destinationState;
  String destinationCity;
  String destinationZip;
  Double destinationLat;
  Double destinationLong;
  Integer destinationRadius;
  String userCompanyIds;
  String equipmentIds;
  String product;
  Integer frequency;
}
