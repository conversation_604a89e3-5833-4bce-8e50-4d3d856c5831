package com.bulkloads.web.loadalert.service;

import static com.bulkloads.common.StringUtil.stringToIntegerList;
import static com.bulkloads.common.UserUtil.getUserIdOrThrow;
import static com.bulkloads.config.AppConstants.WebSocket.ACTION;
import static com.bulkloads.config.AppConstants.WebSocket.Action.CREATED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.DELETED;
import static com.bulkloads.config.AppConstants.WebSocket.Action.UPDATED;
import static com.bulkloads.config.AppConstants.WebSocket.Channel.LOAD_ALERTS;
import static com.bulkloads.config.AppConstants.WebSocket.DATA;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.equipment.repository.EquipmentRepository;
import com.bulkloads.web.infra.websocket.WebSocketService;
import com.bulkloads.web.infra.websocket.dto.WebSocketPublishDto;
import com.bulkloads.web.loadalert.domain.LoadAlertDomainService;
import com.bulkloads.web.loadalert.domain.data.LoadAlertData;
import com.bulkloads.web.loadalert.domain.entity.LoadAlert;
import com.bulkloads.web.loadalert.mapper.LoadAlertMapper;
import com.bulkloads.web.loadalert.repository.LoadAlertRepository;
import com.bulkloads.web.loadalert.service.dto.LoadAlertRequest;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import com.bulkloads.web.loadalert.service.dto.LoadAlertSearchRequest;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.rate.repository.RateProductCategoryRepository;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import com.bulkloads.web.usercompany.repository.UserCompanyRepository;
import org.springframework.stereotype.Service;
import jakarta.persistence.EntityNotFoundException;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LoadAlertService {

  private final LoadAlertRepository loadAlertRepository;
  private final UserCompanyRepository userCompanyRepository;
  private final EquipmentRepository equipmentRepository;
  private final RateProductCategoryRepository rateProductCategoryRepository;

  private final CommonMapper commonMapper;

  private final LoadAlertMapper loadAlertMapper;

  private final WebSocketService webSocketService;

  private final LoadAlertDomainService loadAlertDomainService;

  public List<LoadAlertResponse> getLoadAlerts(final LoadAlertSearchRequest loadAlertSearchRequest) {
    // companies validation
    if (!loadAlertSearchRequest.getUserCompanyIds().isEmpty()) {
      List<Integer> ids = stringToIntegerList(loadAlertSearchRequest.getUserCompanyIds());
      List<UserCompany> companies =
          userCompanyRepository.findAllByUserCompanyIdInOrderByCompanyNameAsc(ids);

      // convert list to a string of comma-delimited integers
      loadAlertSearchRequest.setUserCompanyIds(companies.stream().map(UserCompany::getUserCompanyId).map(Object::toString).collect(Collectors.joining(",")));
    }

    // Equipments validation
    if (!loadAlertSearchRequest.getEquipmentIds().isEmpty()) {
      List<String> ids = commonMapper.stringToStringList(loadAlertSearchRequest.getEquipmentIds());
      List<Equipment> equipments = equipmentRepository.findAllByEquipmentIdInOrderByEquipmentIdAsc(ids);

      // find and create list of equipment ids and names
      loadAlertSearchRequest.setEquipmentIds(equipments.stream().map(Equipment::getEquipmentId).map(Object::toString).collect(Collectors.joining(",")));
    }

    // Rate product categories validation
    if (!loadAlertSearchRequest.getProduct().isEmpty()) {
      List<String> products = commonMapper.stringToStringList(loadAlertSearchRequest.getProduct());
      List<RateProductCategory> categories =
          rateProductCategoryRepository.findAllByRateProductCategoryInOrderByRateProductCategoryAsc(products);

      // find and concatenate
      loadAlertSearchRequest.setRateProductCategoryIds(
          categories.stream().map(RateProductCategory::getRateProductCategoryId).map(Object::toString).collect(Collectors.joining(","))
      );
      loadAlertSearchRequest.setProduct("");
    }

    int userId = getUserIdOrThrow();

    return loadAlertRepository.getLoadAlerts(userId, loadAlertSearchRequest);
  }

  public LoadAlertResponse createLoadAlert(LoadAlertRequest loadAlertRequest) {

    LoadAlertData data = loadAlertMapper.requestToData(loadAlertRequest);
    Result<LoadAlert> result = loadAlertDomainService.create(data);
    LoadAlert loadAlert = result.orElseThrow();
    loadAlertRepository.save(loadAlert);
    LoadAlertResponse response = loadAlertMapper.entityToResponse(loadAlert);
    sendToWebSocket(CREATED, loadAlert.getLoadAlertId(), response);
    return response;
  }

  public LoadAlertResponse updateLoadAlert(Integer loadAlertId, LoadAlertRequest request) {
    final int userId = getUserIdOrThrow();
    final LoadAlert loadAlertsToUpdate = findByLoadAlertIdAndUserId(loadAlertId, userId);
    final LoadAlertData data = loadAlertMapper.requestToData(request);
    final Result<LoadAlert> result = loadAlertDomainService.update(loadAlertsToUpdate, data);
    LoadAlert loadAlert = result.orElseThrow();
    loadAlert = loadAlertRepository.save(loadAlert);
    LoadAlertResponse response = loadAlertMapper.entityToResponse(loadAlert);
    sendToWebSocket(UPDATED, loadAlert.getLoadAlertId(), response);
    return response;
  }

  public void deleteLoadAlert(Integer loadAlertId) {
    final int userId = getUserIdOrThrow();
    final LoadAlert loadAlertsToRemove = findByLoadAlertIdAndUserId(loadAlertId, userId);
    final Result<LoadAlert> result = loadAlertDomainService.delete(loadAlertsToRemove);

    loadAlertRepository.save(result.orElseThrow());
    sendToWebSocket(DELETED, loadAlertId, null);
  }

  public void activateLoadAlert(Integer loadAlertId) {
    final int userId = getUserIdOrThrow();

    // Retrieve the LoadAlert entity from the repository
    LoadAlert loadAlert = loadAlertRepository.findByLoadAlertIdAndUserUserId(loadAlertId, userId)
        .orElseThrow(() -> new EntityNotFoundException("load_alert_id is missing"));

    // Update the active status and last run date
    loadAlert.setActive(true);
    loadAlert.setLastRun(Instant.now());

    // Save the updated LoadAlert entity
    loadAlertRepository.save(loadAlert);
  }

  public void deactivateLoadAlert(Integer loadAlertId) {
    final int userId = getUserIdOrThrow();

    // Retrieve the LoadAlert entity from the repository
    LoadAlert loadAlert = loadAlertRepository.findByLoadAlertIdAndUserUserId(loadAlertId, userId)
        .orElseThrow(() -> new EntityNotFoundException("load_alert_id is missing"));

    // Update the deactivate status and last run date
    loadAlert.setActive(false);
    loadAlert.setLastRun(Instant.now());

    // Save the updated LoadAlert entity
    loadAlertRepository.save(loadAlert);
  }

  private LoadAlert findByLoadAlertIdAndUserId(Integer loadAlertId, Integer userId) {
    Optional<LoadAlert> loadAlertOptional = loadAlertRepository.findByLoadAlertIdAndUserUserId(loadAlertId, userId);

    if (loadAlertOptional.isPresent()) {
      return loadAlertOptional.get();
    } else {
      throw new ValidationException("general", "load_alert_id is missing");
    }
  }

  public void sendToWebSocket(final String action, final int loadAlertId, final LoadAlertResponse data) {
    final Integer userId = getUserIdOrThrow();

    final Map<String, Object> message = buildMessage(action, loadAlertId, data);
    final Map<String, Object> meta = Map.of("user_id", userId);

    final WebSocketPublishDto publishDto = WebSocketPublishDto.builder()
        .channel(LOAD_ALERTS)
        .message(message)
        .meta(meta)
        .build();

    webSocketService.sendToWebSocket(publishDto);
  }

  private Map<String, Object> buildMessage(final String action, final int loadAlertId, final LoadAlertResponse data) {
    final Map<String, Object> message = new HashMap<>();
    message.put(ACTION, action);
    message.put("load_alert_id", loadAlertId);
    if (data != null) {
      message.put(DATA, data);
    }
    return message;
  }

}
