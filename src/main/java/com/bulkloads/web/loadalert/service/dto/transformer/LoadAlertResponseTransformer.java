package com.bulkloads.web.loadalert.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.loadalert.service.dto.LoadAlertResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class LoadAlertResponseTransformer implements TupleTransformer<LoadAlertResponse> {

  @Override
  public LoadAlertResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);
    return LoadAlertResponse.builder()
        .loadAlertId(parts.asInteger("load_alert_id"))
        .originCountry(parts.asString("origin_country"))
        .originState(parts.asString("origin_state"))
        .originCity(parts.asString("origin_city"))
        .originZip(parts.asString("origin_zip"))
        .originLat(parts.asDouble("origin_lat"))
        .originLong(parts.asDouble("origin_long"))
        .originRadius(parts.asInteger("origin_radius"))
        .destinationCountry(parts.asString("destination_country"))
        .destinationState(parts.asString("destination_state"))
        .destinationCity(parts.asString("destination_city"))
        .destinationZip(parts.asString("destination_zip"))
        .destinationLat(parts.asDouble("destination_lat"))
        .destinationLong(parts.asDouble("destination_long"))
        .destinationRadius(parts.asInteger("destination_radius"))
        .userCompanyIds(parts.asString("user_company_ids"))
        .companyNames(parts.asString("company_names"))
        .equipmentIds(parts.asString("equipment_ids"))
        .equipmentNames(parts.asString("equipment_names"))
        .product(parts.asString("product"))
        .frequency(parts.asInteger("frequency"))
        .dateAdded(parts.asInstant("date_added"))
        .dateEdited(parts.asInstant("date_edited"))
        .active(parts.asBoolean("active"))
        .build();

  }
}
