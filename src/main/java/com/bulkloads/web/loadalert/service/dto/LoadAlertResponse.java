package com.bulkloads.web.loadalert.service.dto;

import java.time.Instant;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class LoadAlertResponse {
  Integer loadAlertId;
  String originCountry;
  String originState;
  String originCity;
  String originZip;
  Double originLat;
  Double originLong;
  Integer originRadius;
  String destinationCountry;
  String destinationState;
  String destinationCity;
  String destinationZip;
  Double destinationLat;
  Double destinationLong;
  Integer destinationRadius;
  String userCompanyIds;
  String companyNames;
  String equipmentIds;
  String equipmentNames;
  String product;
  Integer frequency;
  Instant dateEdited;
  Instant dateAdded;
  Boolean active;
}