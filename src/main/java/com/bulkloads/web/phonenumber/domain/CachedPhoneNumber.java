package com.bulkloads.web.phonenumber.domain;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "phone_numbers")
@Getter
@Setter
public class CachedPhoneNumber {

  @Id
  @Column(name = "phone_number")
  private Long phoneNumber;


  @Size(max = 20, message = "Enter up to 20 chars")
  @NotNull
  @Column(name = "std_format")
  private String stdFormat = "";


  @Size(max = 45, message = "Enter up to 45 chars")
  @NotNull
  @Column(name = "carrier_type")
  private String carrierType = "";


  @Size(max = 60, message = "Enter up to 60 chars")
  @NotNull
  @Column(name = "carrier_name")
  private String carrierName = "";


  @Size(max = 20, message = "Enter up to 20 chars")
  @NotNull
  @Column(name = "error_code")
  private String errorCode = "";


  @Size(max = 200, message = "Enter up to 200 chars")
  @NotNull
  @Column(name = "error_message")
  private String errorMessage = "";


  @Column(name = "date_added")
  private Instant dateAdded = Instant.now();


  public void setCarrierName(String carrierName) {
    if (carrierName != null && carrierName.length() > 60) {
      this.carrierName = carrierName.substring(0, 60);
    } else {
      this.carrierName = carrierName;
    }
  }

}
