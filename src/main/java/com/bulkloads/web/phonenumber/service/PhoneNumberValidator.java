package com.bulkloads.web.phonenumber.service;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Getter
public class PhoneNumberValidator {

  public static boolean isValidPhoneNumber(String phoneNumber) {
    PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    try {
      Phonenumber.PhoneNumber parsedNumber = phoneNumberUtil.parse(phoneNumber, "US");
      return phoneNumberUtil.isValidNumber(parsedNumber);
    } catch (NumberParseException e) {
      log.info("e {}", e.getMessage());
      return false;
    }
  }

}
