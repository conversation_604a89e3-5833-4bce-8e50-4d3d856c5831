package com.bulkloads.web.product.service.dto.transformer;

import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.product.service.dto.ProductExternalListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ProductExternalListViewTransformer implements TupleTransformer<ProductExternalListResponse> {

  @Override
  public ProductExternalListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    ProductExternalListResponse response = new ProductExternalListResponse();
    response.setExternalRateProductCategoryId(parts.asInteger("external_rate_product_category_id"));
    response.setExternalRateProductCategory(parts.asString("external_rate_product_category"));
    response.setExternalRateProductCategoryType(parts.asString("external_rate_product_category_type"));
    return response;
  }

}
