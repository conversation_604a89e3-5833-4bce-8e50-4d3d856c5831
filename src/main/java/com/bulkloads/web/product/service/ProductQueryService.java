package com.bulkloads.web.product.service;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.product.repository.ProductQueryRepository;
import com.bulkloads.web.product.service.dto.ProductCategoryListResponse;
import com.bulkloads.web.product.service.dto.ProductExternalListResponse;
import com.bulkloads.web.product.service.dto.ProductListResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class ProductQueryService {

  private final ProductQueryRepository productQueryRepository;

  public List<ProductListResponse> getProducts(final String term) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return productQueryRepository.getProducts(userCompanyId, term);
  }

  public List<ProductExternalListResponse> getProductExternals() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return productQueryRepository.getProductExternals(userCompanyId);
  }

  public List<ProductCategoryListResponse> getProductCategories() {
    return productQueryRepository.getProductCategories();
  }

}
