package com.bulkloads.web.product.repository.template;

import org.intellij.lang.annotations.Language;

public class GetProductExternalsQueryTemplate {

  @Language("SQL")
  public static final String GET_PRODUCT_EXTERNALS_QUERY_TEMPLATE = """
          SELECT
              external_rate_product_category_id,
              external_rate_product_category,
              external_rate_product_category_type
          from external_rate_product_categories
          <% params.put("userCompanyId", userCompanyId) %>
          where user_company_id = :userCompanyId
          order by external_rate_product_category_id
      """;

}
