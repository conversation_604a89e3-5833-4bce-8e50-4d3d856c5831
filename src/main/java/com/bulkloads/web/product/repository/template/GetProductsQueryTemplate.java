package com.bulkloads.web.product.repository.template;

public class GetProductsQueryTemplate {

  /*
      for each word in 'term' must exist in 'commodity' column
   */

  public static final String GET_PRODUCTS_QUERY_TEMPLATE = """
          SELECT commodity as product, def_rate_type
          from commodities
          <% params.put("userCompanyId", userCompanyId) %>
          where user_company_id = :userCompanyId

          <% if (paramExists("term")) { %>
              <% var wildTerms = term.split("\\s+") %>
              <% for (int i = 0; i < wildTerms.length; i++) { %>
                  <% params.put("wildTerms_"+i, "%"+wildTerms[i]+"%") %>
                  AND commodity LIKE :<% print("wildTerms_" + i) %>
              <% } %>
          <% } %>
          order by commodity
      """;

}
