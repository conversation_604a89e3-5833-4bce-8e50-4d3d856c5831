package com.bulkloads.web.contracts.domain.entity;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.config.AppConstants;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.common.event.DomainEvent;
import com.bulkloads.web.rate.domain.entity.RateType;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import org.springframework.data.domain.AbstractAggregateRoot;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Entity
@Table(name = "contracts")
//@OptimisticLocking(type = OptimisticLockType.VERSION)
public class Contract extends AbstractAggregateRoot<Contract> {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "contract_id")
  private Integer contractId;

  @Size(max = 50, message = "Must be up to 50 chars")
  @NotNull
  @Column(name = "external_contract_id")
  private String externalContractId = "";

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

  @NotNull
  @Column(name = "contract_status")
  private String contractStatus = AppConstants.ContractStatus.OPEN;

  @Pattern(regexp = "(?i)Buy|Sell", message = "Should be 'Buy' or 'Sell'")
  @Column(name = "buy_sell")
  private String buySell = AppConstants.BuySell.SELL;

  @Size(max = 45, message = "Must be up to 45 chars")
  @Column(name = "contract_number")
  private String contractNumber = "";

  @DecimalMin(value = "0.0", message = "Enter a valid rate")
  @Column(name = "rate")
  private BigDecimal rate;

  @NotNull(message = "Enter a valid rate type")
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "rate_type")
  private RateType rateType;

  @DecimalMin(value = "0.0", message = "Enter a valid freight rate")
  @Column(name = "freight_rate")
  private BigDecimal freightRate;

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY, optional = false)
  @JoinColumn(name = "freight_rate_type")
  private RateType freightRateType;

  @Column(name = "freight_rate_per_mile")
  private BigDecimal freightRatePerMile;

  @NotNull(message = "Enter the # of loads")
  @Positive(message = "Enter a positive number of loads")
  @Column(name = "number_of_loads")
  private Integer numberOfLoads;

  @PositiveOrZero(message = "Remaining number of loads cannot be negative")
  @Column(name = "remaining_number_of_loads")
  private Integer remainingNumberOfLoads;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "quantity")
  private BigDecimal quantity;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "contract_total")
  private BigDecimal contractTotal;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "remaining_quantity")
  private BigDecimal remainingQuantity;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "weight")
  private BigDecimal weight;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "remaining_weight")
  private BigDecimal remainingWeight;

  @Column(name = "ship_from")
  private LocalDate shipFrom;

  @Column(name = "ship_to")
  private LocalDate shipTo;

  @ManyToOne(fetch = FetchType.LAZY, cascade = CascadeType.ALL)
  @JoinColumn(name = "commodity_id")
  private Commodity commodity;

  @Size(max = 100, message = "Enter up to 100 chars")
  @NotNull
  @Column(name = "commodity")
  private String commodityName = "";

  @Size(max = 150, message = "Enter up to 150 chars")
  @NotNull
  @Column(name = "contact_info")
  private String contactInfo = "";

  @Size(max = 500, message = "Enter up to 500 chars")
  @NotNull
  @Column(name = "notes")
  private String notes = "";

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "pickup_ab_company_id")
  private AbCompany pickupAbCompany;

  @Size(max = 150, message = "Enter up to 150 chars")
  @NotNull
  @Column(name = "pickup_company_name")
  private String pickupCompanyName = "";

  @Size(max = 120, message = "Enter up to 120 chars")
  @NotNull
  @Column(name = "pickup_address")
  private String pickupAddress = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "pickup_location")
  private String pickupLocation = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "pickup_city")
  private String pickupCity = "";

  @Size(max = 2, message = "Enter up to 2 chars")
  @Column(name = "pickup_state")
  private String pickupState = "";

  @Size(max = 7, message = "Enter up to 7 chars")
  @Column(name = "pickup_zip")
  private String pickupZip = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "pickup_country")
  private String pickupCountry = "";

  @Column(name = "pickup_lat")
  private Double pickupLat;

  @Column(name = "pickup_long")
  private Double pickupLong;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "drop_ab_company_id")
  private AbCompany dropAbCompany;

  @Size(max = 150, message = "Enter up to 150 chars")
  @NotNull
  @Column(name = "drop_company_name")
  private String dropCompanyName = "";

  @Size(max = 120, message = "Enter up to 120 chars")
  @NotNull
  @Column(name = "drop_address")
  private String dropAddress = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "drop_location")
  private String dropLocation = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "drop_city")
  private String dropCity = "";

  @Size(max = 2, message = "Enter up to 2 chars")
  @Column(name = "drop_state")
  private String dropState = "";

  @Size(max = 7, message = "Enter up to 7 chars")
  @Column(name = "drop_zip")
  private String dropZip = "";

  @Size(max = 45, message = "Enter up to 45 chars")
  @Column(name = "drop_country")
  private String dropCountry = "";

  @Column(name = "drop_lat")
  private Double dropLat;

  @Column(name = "drop_long")
  private Double dropLong;

  @DecimalMin(value = "0.0", message = "Enter a positive number")
  @Column(name = "miles")
  private BigDecimal miles;

  @NotNull
  @Column(name = "added_date")
  private Instant addedDate = Instant.now();

  @NotNull
  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "added_by_user_id")
  private User addedByUser;

  @Column(name = "edit_date")
  private Instant editDate;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "edit_by_user_id")
  private User editByUser;

  @Column(name = "closed_date")
  private Instant closedDate;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "closed_by_user_id")
  private User closedByUser;

  //    @Version
  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "deleted_by_user_id")
  private User deletedByUser;

  @Column(name = "deleted")
  private boolean deleted;

  public void registerDomainEvent(final DomainEvent event) {
    registerEvent(event);
  }

}
