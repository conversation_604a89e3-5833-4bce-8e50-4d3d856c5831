package com.bulkloads.web.contracts.api;

import static com.bulkloads.config.AppConstants.UserPermission.MANAGE_CONTRACTS;
import com.bulkloads.common.api.ApiResponse;
import com.bulkloads.web.contracts.api.dto.ContractApiRequest;
import com.bulkloads.web.contracts.mapper.ContractMapper;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.contracts.service.dto.ContractResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Positive;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/contracts")
@Tag(name = "Contracts")
@RequiredArgsConstructor
@Validated
@PreAuthorize("hasAuthority('" + MANAGE_CONTRACTS + "')")
public class ContractController {

  private final ContractService contractService;
  private final ContractMapper contractMapper;

  @Operation(summary = "Create contract")
  @PostMapping
  public ApiResponse<ContractResponse, Integer> createContract(
      @RequestBody final ContractApiRequest apiRequest) {
    final ContractRequest request = contractMapper.apiRequestToServiceRequest(apiRequest);
    final ContractResponse response = contractService.create(request);

    return ApiResponse.<ContractResponse, Integer>builder()
        .key(response.getContractId())
        .data(response)
        .message("Contract created")
        .build();
  }

  @Operation(summary = "Update contract")
  @PutMapping("/{contract_id}")
  public ApiResponse<ContractResponse, Integer> updateContract(
      @PathVariable("contract_id")
      @Positive(message = "Contract id should be positive") final int contractId,
      @RequestBody final ContractApiRequest apiRequest) {
    final ContractRequest request = contractMapper.apiRequestToServiceRequest(apiRequest);
    final ContractResponse response = contractService.update(contractId, request);

    return ApiResponse.<ContractResponse, Integer>builder()
        .key(response.getContractId())
        .data(response)
        .message("Contract updated")
        .build();
  }

  @Operation(summary = "Delete Contract")
  @DeleteMapping("/{contract_id}")
  public ApiResponse<ContractResponse, Integer> deleteContract(
      @PathVariable("contract_id")
      @Positive(message = "Contract id should be positive") final int contractId) {
    contractService.deleteById(contractId);

    return ApiResponse.<ContractResponse, Integer>builder()
        .key(contractId)
        .message("Contract deleted")
        .build();
  }

  @Operation(summary = "Close Contract")
  @PostMapping("/{contract_id}/close")
  public ApiResponse<ContractResponse, Integer> closeContract(
      @PathVariable("contract_id")
      @Positive(message = "Contract id should be positive") final int contractId) {
    contractService.setClosed(contractId);

    return ApiResponse.<ContractResponse, Integer>builder()
        .key(contractId)
        .message("Contract closed")
        .build();
  }

  @Operation(summary = "Open Contract")
  @PostMapping("/{contract_id}/reopen")
  public ApiResponse<ContractResponse, Integer> openContract(
      @PathVariable("contract_id")
      @Positive(message = "Contract id should be positive") final int contractId) {
    contractService.setOpened(contractId);

    return ApiResponse.<ContractResponse, Integer>builder()
        .key(contractId)
        .message("Contract re-opened")
        .build();
  }
}
