package com.bulkloads.web.contracts.service.dto;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import com.bulkloads.common.jackson.BooleanAsNumberSerializer;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class ContractResponse {

  int contractId;
  int userId;
  int userCompanyId;
  String contractStatus;
  String externalContractId;
  String buySell;
  String contractNumber;
  BigDecimal rate;
  String rateType;
  BigDecimal freightRate;
  String freightRateType;
  BigDecimal freightRatePerMile;
  Integer numberOfLoads;
  Integer remainingNumberOfLoads;
  BigDecimal quantity;
  BigDecimal contractTotal;
  BigDecimal remainingQuantity;
  BigDecimal weight;
  BigDecimal remainingWeight;
  Integer commodityId;
  @JsonProperty("commodity")
  String commodityName;
  LocalDate shipFrom;
  LocalDate shipTo;
  Integer pickupAbCompanyId;
  String pickupCompanyName;
  String pickupAddress;
  String pickupLocation;
  String pickupCity;
  String pickupState;
  String pickupZip;
  String pickupCountry;
  Double pickupLat;
  Double pickupLong;
  Integer dropAbCompanyId;
  String dropCompanyName;
  String dropAddress;
  String dropLocation;
  String dropCity;
  String dropState;
  String dropZip;
  String dropCountry;
  Double dropLat;
  Double dropLong;
  BigDecimal miles;
  String contactInfo;
  String notes;
  Instant addedDate;
  Instant closedDate;
  Instant modifiedDate;
  @JsonSerialize(using = BooleanAsNumberSerializer.class)
  boolean deleted;
}
