package com.bulkloads.web.contracts.handler;

import static com.bulkloads.common.StringUtil.toCamelCase;
import static com.bulkloads.config.AppConstants.BillWeightUse.UNLOAD_WEIGHT;
import static com.bulkloads.config.AppConstants.DELIVERED_COMPLETED;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import java.util.Set;
import com.bulkloads.web.assignment.domain.entity.Assignment;
import com.bulkloads.web.assignment.event.AssignmentBookingEvent;
import com.bulkloads.web.assignment.event.AssignmentBookingStatusChangedEvent;
import com.bulkloads.web.assignment.event.AssignmentUpdatedEvent;
import com.bulkloads.web.assignment.event.BookingUpdatedEvent;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.contracts.service.ContractService;
import com.bulkloads.web.load.domain.entity.Load;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class ContractEventHandler {

  private final ContractService contractService;
  private final AssignmentRepository assignmentRepository;

  @TransactionalEventListener(
      classes = {
          AssignmentBookingStatusChangedEvent.class
      },
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentStatusChanged(final AssignmentBookingStatusChangedEvent event) {

    final int assignmentId = event.getLoadAssignmentIds().get(0);
    final String newStatus = event.getNewStatus();
    final String oldStatus = event.getOldStatus();

    // if from non-delivered to delivered or vice versa
    if ((!DELIVERED_COMPLETED.contains(newStatus)
        && DELIVERED_COMPLETED.contains(oldStatus))
        || (!DELIVERED_COMPLETED.contains(oldStatus)
        && DELIVERED_COMPLETED.contains(newStatus))) {

      final Assignment assignment = assignmentRepository.getReferenceById(assignmentId);

      processAssignmentsContract(assignment);

    }
  }

  @TransactionalEventListener(
      classes = {
          AssignmentUpdatedEvent.class,
          BookingUpdatedEvent.class
      },
      phase = TransactionPhase.BEFORE_COMMIT)
  public void handleAssignmentStatusChanged(final AssignmentBookingEvent event) {

    final int assignmentId = event.getLoadAssignmentIds().get(0);
    final Set<String> affectedFields = event.getAffectedFields();

    if (affectedFields.contains(toCamelCase(UNLOAD_WEIGHT))) {
      final Assignment assignment = assignmentRepository.getReferenceById(assignmentId);
      processAssignmentsContract(assignment);
    }
  }

  private void processAssignmentsContract(Assignment assignment) {
    final Integer rerouteContractId = assignment.getRerouteContractId();
    if (isNull(rerouteContractId)) {
      final Load load = assignment.getLoad();
      if (nonNull(load) && nonNull(load.getContract())) {
        final Integer contractId = load.getContract().getContractId();
        contractService.refreshCalculatedFields(contractId);
      }
    } else {
      contractService.refreshCalculatedFields(rerouteContractId);
    }
  }
}
