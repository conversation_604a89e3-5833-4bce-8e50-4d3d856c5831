package com.bulkloads.web.activity.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import java.util.List;
import com.bulkloads.web.activity.service.ActivityQueryService;
import com.bulkloads.web.activity.service.dto.ActivityListResponse;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@RequestMapping(path = "/rest/activities")
@Tag(name = "Activities")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
public class ActivityQueryController {

  private final ActivityQueryService activityQueryService;

  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  @GetMapping
  public List<ActivityListResponse> getActivities(
      @RequestParam(value = "user_ids", required = false) String userIds,
      @RequestParam(value = "activity_type_id", required = false) Integer activityTypeId,
      @RequestParam(value = "past_days", defaultValue = "7") Integer pastDays,
      @RequestParam(value = "skip", defaultValue = "0") Integer skip,
      @RequestParam(value = "limit", defaultValue = "100") Integer limit) {
    return activityQueryService.getActivities(userIds, activityTypeId, pastDays, skip, limit);
  }

  @GetMapping("types")
  public List<ActivityTypeListResponse> getActivityTypes() {
    return activityQueryService.getActivityTypes();
  }
}
