package com.bulkloads.web.activity.service.dto.transformers;

import static com.bulkloads.common.Converters.jsonStringToMap;
import com.bulkloads.common.jpa.nativejpa.QueryParts;
import com.bulkloads.web.activity.service.dto.ActivityTypeListResponse;
import org.hibernate.query.TupleTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class ActivityTypeListResponseTransformer implements TupleTransformer<ActivityTypeListResponse> {

  @Override
  public ActivityTypeListResponse transformTuple(Object[] columns, String[] aliases) {
    QueryParts parts = new QueryParts(columns, aliases);

    ActivityTypeListResponse response = new ActivityTypeListResponse();
    response.setActivityTypeId(parts.asInteger("activity_type_id"));
    response.setActivityType(parts.asString("activity_type"));
    response.setActivityMetadata(jsonStringToMap(parts.asString("activity_metadata")));
    return response;
  }
}
