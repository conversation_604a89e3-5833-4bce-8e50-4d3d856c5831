package com.bulkloads.web.activity.service.dto;

import java.time.Instant;
import java.util.Map;
import lombok.Data;

@Data
public class ActivityListResponse {

  private Integer activityId;
  private Integer userId;
  private String firstName;
  private String lastName;
  private Instant addedDate;
  private String activity;
  private String action;
  private Integer activityTypeId;
  private String activityType;
  private Map<String, Object> data;
}
