package com.bulkloads.web.truck.domain.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "jqm_states")
public class JqmStates {

  @Id
  @Column(name = "stateID")
  private String stateId = "";

  @Column(name = "countryID")
  private String countryId = "";

  @Column(name = "state")
  private String state = "";
}
