package com.bulkloads.web.truck.repository.template;

import org.intellij.lang.annotations.Language;

public class GetTruckByStateQueryTemplate {


  @Language("SQL")
  public static final String GET_TRUCK_BY_STATE_QUERY_TEMPLATE = """
 SELECT
     upper(s.countryID) AS country,
     s.state AS `state_full`,
     upper(s.stateid) AS `state`,
     IFNULL(origin_count, 0) AS origin_count
 FROM
     jqm_states s
 LEFT JOIN (
     SELECT
         origin_country,
         origin_state,
         COUNT(truck_id) AS origin_count
     FROM
         trucks
     INNER JOIN user_info USING (user_id)
     INNER JOIN bl_user_settings bl USING (user_id)
     WHERE
         trucks.active = 1
         AND site_id = 1
         AND bl.deletion_date IS NULL
         AND user_info.deletion_date IS NULL
         <% if (paramExistsAdd("cId")) { %>
         AND trucks.user_company_id NOT IN (
             SELECT
                 blocked_user_company_id
             FROM
                 blocked_companies
             WHERE
                 user_company_id = :cId
         )
         AND trucks.user_company_id NOT IN (
             SELECT
                 user_company_id
             FROM
                 blocked_companies
             WHERE
                 blocked_user_company_id = :cId
         )
         <% } %>
     GROUP BY
         origin_country, origin_state
 ) out_trucks ON s.stateid = out_trucks.origin_state AND s.countryid = out_trucks.origin_country
 ORDER BY
     IF (s.countryid = 'us', 1, IF (s.countryid = 'ca', 2, 3)), s.stateid;
      """;
}
