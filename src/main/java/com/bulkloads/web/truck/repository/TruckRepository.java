package com.bulkloads.web.truck.repository;

import java.util.Optional;
import com.bulkloads.web.truck.domain.entity.Truck;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface TruckRepository extends JpaRepository<Truck, Integer>, TruckQueryRepository, TruckByStateQueryRepository {

  Optional<Truck> findByTruckIdAndUserCompanyUserCompanyId(final Integer truckId, final Integer userCompanyId);
}

