package com.bulkloads.web.truck.repository.template;

import org.intellij.lang.annotations.Language;

public class GetTruckQueryTemplate {

  @Language("SQL")
  public static final String GET_TRUCK_QUERY_TEMPLATE = """
      SELECT
         concat(origin_city, ', ', origin_state,
         if(length(origin_zipcode) >0, concat(' ',origin_zipcode),''),
         if(origin_country <> 'us',concat(', ',origin_country),'')) as origin,
         if(prefered_destination = '',
            if(destination_city = '', 'ANY' , concat(destination_city, ', ', destination_state)) ,
            prefered_destination
          ) as destination,
          trucks.site_id,
          trucks.user_company_id,
          trucks.contact_name,
          trucks.contact_number,
          trucks.contact_number_type,
          trucks.nickname,
          trucks.date_available,
          trucks.time_available,
          trucks.prefered_destination,
          trucks.trailer_type,
          trucks.trailer_type as equipment_name,
          trucks.comments,
          trucks.post_date,
          origin_city,
          origin_state,
          origin_zipcode,
          origin_country,
          origin_lat,
          origin_long,
          destination_city,
          destination_state,
          destination_zipcode,
          destination_country,
          dest_lat,
          dest_long,
          trucks.truck_id,
          trucks.user_id,
          user_info.first_name,
          user_info.last_name,
          CONCAT(user_info.first_name, ' ',user_info.last_name)as name,
          user_company.company_name,
          user_info.phone_1,
          user_info.phone_2,
          user_company.census_num,
          user_company.mc_num,
          user_company.user_types,
          ratings_private,
          user_info.state,
          trucks.repost_days,
          date(date_available + interval repost_days day) as expires_on,
          email,
          truck_size,
          truck_length,
          truck_area,
          (select min(sign_up_date) from user_info where user_info.user_company_id = user_company.user_company_id)as sign_up_date,
          (select membership_end_date from bl_user_company_settings where bl_user_company_settings.user_company_id = user_company.user_company_id)
            as company_membership_end_date

        FROM trucks
           INNER JOIN user_info on trucks.user_id = user_info.user_id
           INNER JOIN user_company on trucks.user_company_id = user_company.user_company_id

        WHERE active = 1
          AND user_info.deletion_date IS NULL
          AND site_id = 1

        <% if (paramExistsAdd("uId")) { %>
           and trucks.user_id = :uId
        <% } %>

        <% if (paramExistsAdd("cId")) { %>
           and trucks.user_company_id not in (select blocked_user_company_id from blocked_companies where user_company_id = :cId)
           and trucks.user_company_id not in (select user_company_id from blocked_companies where blocked_user_company_id = :cId)
        <% } %>

        <% if (paramExistsAdd("truckId")) { %>
            AND truck_id = :truckId
        <% } %>

        <% if (paramExistsAdd("truckDate")) { %>
            AND DATEDIFF(date_available, :truckDate) <= 0
            AND DATEDIFF(date_available, now()) >= -2
        <% } else if (paramExists("shipFrom") && paramExists("shipTo")) { %>
            <% params.put("shipFrom", shipFrom) %>
            <% params.put("shipTo", shipTo) %>
            AND DATEDIFF(date_available, :shipFrom) >= 0
            AND DATEDIFF(:shipTo, date_available) >= 0
        <% } %>

        <% if (paramExistsAdd("equipment")) { %>
            AND trailer_type IN (:equipment)
        <% } %>

        <% if (paramExistsAdd("distance") && paramExistsAdd("latitude") && paramExistsAdd("longitude")) { %>
            AND st_distance_sphere(
                point(:longitude, :latitude),
                point(trucks.origin_long, trucks.origin_lat))/1609.34 <= :distance
        <% } %>
  
        <% if (!paramExists("distance") && paramExistsAdd("originState") && !binding.variables.get("originState").equalsIgnoreCase("ALL")) { %>
          <% if (binding.variables.get("originState").equalsIgnoreCase("CANADA")) { %>
            AND origin_country = 'CA'
          <% } else { %>        
            AND origin_state = :originState
          <% } %>
        <% } %>

        -- Sorting
        <% if (!paramIsTrue("count")) { %>
          <% if (paramExists("orderBy")) { %>
            ORDER BY <% print(orderBy) %>
          <% } %>

          <% if (paramExistsAdd("limit")) { %>
            LIMIT
            <% if (paramExistsAdd("skip")) { %>
                :skip,
            <% } %>
            :limit
          <% } %>
        <% } %>
      """;
}
