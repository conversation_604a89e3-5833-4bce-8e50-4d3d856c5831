package com.bulkloads.web.truck.repository;

import static com.bulkloads.web.truck.repository.template.GetTruckByStateQueryTemplate.GET_TRUCK_BY_STATE_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.truck.service.dto.TruckByStateResponse;
import com.bulkloads.web.truck.service.dto.transformer.TruckByStateResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class TruckByStateQueryRepositoryImpl implements TruckByStateQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final TruckByStateResponseTransformer truckByStateResponseTransformer;

  @Override
  public List<TruckByStateResponse> getTruckTotalsByState(Integer cId) {
    Map<String, Object> params = new HashMap<>();
    params.put("cId", cId);
    return jpaNativeQueryService.query(GET_TRUCK_BY_STATE_QUERY_TEMPLATE, params, truckByStateResponseTransformer);
  }
}
