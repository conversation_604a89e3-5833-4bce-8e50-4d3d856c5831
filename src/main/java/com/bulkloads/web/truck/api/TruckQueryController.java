package com.bulkloads.web.truck.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.time.LocalDate;
import java.util.List;
import com.bulkloads.web.truck.service.TruckService;
import com.bulkloads.web.truck.service.dto.MyTruckResponse;
import com.bulkloads.web.truck.service.dto.TruckByStateResponse;
import com.bulkloads.web.truck.service.dto.TruckResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@RestController
@Tag(name = "Trucks")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@RequestMapping("/rest/trucks")

public class TruckQueryController {

  private final TruckService truckService;

  @GetMapping(value = "/my_trucks", headers = "Accept=application/json")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public List<MyTruckResponse> getMyTrucks(
      @Parameter(description = "The sort order. Values are case-insensitive. Allowed values are Newest,Oldest,Origin,Destination. Defaults to Newest.")
      @RequestParam(value = "order", defaultValue = "Newest") String order,
      @Parameter(description = "The number of records to skip")
      @RequestParam(value = "skip", defaultValue = "0") Integer skip,
      @Parameter(description = "The number of records to return")
      @RequestParam(value = "limit", defaultValue = "100") Integer limit
  ) {
    return truckService.getMyTrucks(order, skip, limit);
  }

  @GetMapping(value = "")
  public List<TruckResponse> getTrucks(
      @Parameter(name = "origin_state", description = "")
      @RequestParam(value = "origin_state", required = false) String originState,
      @Parameter(name = "origin_city", description = "")
      @RequestParam(value = "origin_city", required = false) String originCity,
      @Parameter(name = "distance", description = "")
      @RequestParam(value = "distance", required = false) Integer distance,
      @Parameter(name = "equipment", description = "")
      @RequestParam(value = "equipment", required = false) String equipment,
      @Parameter(name = "ship_from", description = "")
      @RequestParam(value = "ship_from", required = false) LocalDate shipFrom,
      @Parameter(name = "ship_to", description = "")
      @RequestParam(value = "ship_to", required = false) LocalDate shipTo,
      @Parameter(name = "days", description = "")
      @RequestParam(value = "days", required = false)
      @PositiveOrZero Integer days,
      @Parameter(name = "order", description = "The sort order. Values are case-insensitive. Allowed values are Newest,Oldest,Origin,Destination")
      @RequestParam(value = "order", required = false) String order,
      @Parameter(name = "skip", description = "the number of records to skip, defaults to 0")
      @RequestParam(value = "skip", required = false, defaultValue = "0") Integer skip,
      @Parameter(name = "limit", description = "the number of records to return, defaults to 100")
      @RequestParam(value = "limit", required = false, defaultValue = "100") Integer limit) {

    return truckService.getTrucks(originState, originCity, distance, equipment,
        shipFrom, shipTo,
        days, order, skip, limit);
  }

  @GetMapping(value = "/totals/by_state")
  public List<TruckByStateResponse> getTruckByState() {
    return truckService.getTruckByState();
  }

  @GetMapping(value = "/{truck_id}")
  public TruckResponse getByTruckId(@PathVariable("truck_id") Integer truckId) {
    return truckService.getByTruckId(truckId);
  }
}
