package com.bulkloads.web.truck.service.dto;

import java.time.Instant;
import java.time.LocalDate;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class TruckResponse {

  Integer truckId;
  Integer userId;
  Integer censusNum;
  String comments;
  LocalDate companyMembershipEndDate;
  String companyName;
  String contactName;
  String contactNumber;
  String contactNumberType;
  LocalDate dateAvailable;
  Double destLat;
  Double destLong;
  String destination;
  String destinationCity;
  String destinationCountry;
  String destinationState;
  String destinationZipcode;
  String email;
  LocalDate expiresOn;
  String equipmentName;
  String firstName;
  String lastName;
  String mcNum;
  String name;
  String nickname;
  String origin;
  String originCity;
  String originCountry;
  Double originLat;
  Double originLong;
  String originState;
  String originZipcode;
  @JsonProperty("phone_1")
  String phone1;
  @JsonProperty("phone_2")
  String phone2;
  Instant postDate;
  Instant postedDate;
  String preferedDestination;
  String ratingsPrivate;
  Integer repostDays;
  LocalDate signUpDate;
  Integer siteId;
  String state;
  String timeAvailable;
  String trailerType;
  Integer truckArea;
  String truckLength;
  String truckSize;
  Integer userCompanyId;
  String userTypes;

}
