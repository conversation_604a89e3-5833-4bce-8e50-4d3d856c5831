package com.bulkloads.web.washout.repository.template;


public class GetWashoutCommentsQueryTemplate {

  public static final String GET_WASHOUT_COMMENTS = """
      SELECT
          c.washout_comment_id,
          c.washout_id,
          c.washout_comment_user_id,
          c.washout_comment_date,
          c.washout_comment,
          c.washout_comment_type,
          c.washout_comment_approved,
          c.washout_comment_approved_date,
          c.washout_comment_approved_by_user_id,
          w.washout_name,
          u.user_id,
          u.avatar_small,
          u.first_name,
          u.last_name
      FROM
          washout_comments c
          inner join user_info u on c.washout_comment_user_id = u.user_id
          inner join washouts w on w.washout_id = c.washout_id
      WHERE
          c.deleted = 0

      <% if (paramExistsAdd("washoutId")) { %>
          AND c.washout_id = :washoutId
      <% } %>

      <% if (paramExistsAdd("washoutCommentId")) { %>
          AND c.washout_comment_id = :washoutCommentId
      <% } %>

      -- users see their own and also approved posts
      <% if (isAdmin) { %>
          <% if (paramExistsAdd("washoutCommentApproved")) { %>
              AND c.washout_comment_approved = :washoutCommentApproved
          <% } %>
      <% } else { %>
          AND (
              c.washout_comment_approved = 1
              <% if (paramExistsAdd("uId")) { %>
                  OR c.washout_comment_user_id = :uId
              <% } %>
          )
      <% } %>
                  
      <% if (paramExistsAdd("washoutCommentType")) { %>
          AND c.washout_comment_type = :washoutCommentType
      <% } %>
                  
      ORDER BY c.washout_comment_id
      """;

}
