package com.bulkloads.web.washout.repository;

import java.util.List;
import com.bulkloads.web.washout.service.dto.WashoutCommentResponse;
import org.springframework.stereotype.Repository;

@Repository
interface WashoutCommentQueryRepository {

  WashoutCommentResponse getWashoutComment(
      final Integer washoutCommentId,
      final Integer userId,
      final boolean isSiteAdmin);

  List<WashoutCommentResponse> getWashoutComments(
      final Integer washoutId,
      final Integer washoutCommentId,
      final String washoutCommentType,
      final Boolean washoutCommentApproved,
      final Integer userId,
      final boolean isSiteAdmin);
}
