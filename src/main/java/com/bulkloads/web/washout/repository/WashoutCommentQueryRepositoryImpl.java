package com.bulkloads.web.washout.repository;

import static com.bulkloads.web.washout.repository.template.GetWashoutCommentsQueryTemplate.GET_WASHOUT_COMMENTS;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.washout.service.dto.WashoutCommentResponse;
import com.bulkloads.web.washout.service.dto.transformers.WashoutCommentResponseTransformer;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
class WashoutCommentQueryRepositoryImpl implements WashoutCommentQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final WashoutCommentResponseTransformer washoutCommentResponseTransformer;

  @Override
  public WashoutCommentResponse getWashoutComment(
      final Integer washoutCommentId,
      final Integer userId,
      final boolean isSiteAdmin) {
    final Map<String, Object> params = new HashMap<>();
    params.put("washoutId", null);
    params.put("washoutCommentId", washoutCommentId);
    params.put("washoutCommentType", null);
    params.put("washoutCommentApproved", null);
    params.put("isAdmin", isSiteAdmin);

    Optional.ofNullable(userId).ifPresent(it -> params.put("uId", it));

    return jpaNativeQueryService.queryForObject(GET_WASHOUT_COMMENTS, params, washoutCommentResponseTransformer);

  }

  public List<WashoutCommentResponse> getWashoutComments(
      final Integer washoutId,
      final Integer washoutCommentId,
      final String washoutCommentType,
      final Boolean washoutCommentApproved,
      final Integer userId,
      final boolean isSiteAdmin) {

    final Map<String, Object> params = new HashMap<>();
    params.put("washoutId", washoutId);
    params.put("washoutCommentId", washoutCommentId);
    params.put("washoutCommentType", washoutCommentType);
    params.put("washoutCommentApproved", washoutCommentApproved);
    params.put("isAdmin", isSiteAdmin);

    Optional.ofNullable(userId).ifPresent(it -> params.put("uId", it));

    return jpaNativeQueryService.query(GET_WASHOUT_COMMENTS, params, washoutCommentResponseTransformer);
  }
}
