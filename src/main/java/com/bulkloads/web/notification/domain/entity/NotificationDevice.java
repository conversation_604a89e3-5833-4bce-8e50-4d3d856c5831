package com.bulkloads.web.notification.domain.entity;

import java.time.Instant;
import java.util.Map;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;


@Entity
@Table(name = "notification_devices")
@Getter
@Setter
public class NotificationDevice {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "notification_device_id")
  private Integer notificationDeviceId;


  @NotNull
  @Column(name = "notification_id")
  private Integer notificationId;


  @NotNull
  @Size(max = 100, message = "Up to 100 chars")
  @Column(name = "device_id")
  private String deviceId;


  @NotNull
  @Size(max = 1024, message = "Up to 1024 chars")
  @Column(name = "notification_token")
  private String notificationToken = "";


  @NotNull
  @Size(max = 1024, message = "Up to 1024 chars")
  @Column(name = "device_data")
  private String deviceData = "";


  @Column(name = "date_added")
  private Instant dateAdded;

  @NotNull
  @Column(name = "is_sent")
  private Boolean isSent = false;


  @Column(name = "date_sent")
  private Instant dateSent;


  @NotNull
  @Size(max = 100, message = "Up to 100 chars")
  @Column(name = "fcm_message_id")
  private String fcmMessageId = "";


  @Column(name = "errormsg")
  private String errormsg;


  @Column(name = "notification_json")
  @JdbcTypeCode(SqlTypes.JSON)
  private Map<String, Object> notificationJson;
}
