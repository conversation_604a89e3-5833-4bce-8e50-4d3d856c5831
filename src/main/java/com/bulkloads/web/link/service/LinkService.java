package com.bulkloads.web.link.service;

import java.time.Instant;
import java.util.Optional;
import com.bulkloads.common.StringUtil;
import com.bulkloads.config.AppProperties;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.link.domain.entity.Link;
import com.bulkloads.web.link.repository.LinkRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class LinkService {

  public static final String LINK_PATH = "/rest/links";

  private final LinkRepository linkRepository;
  private final ObjectMapper objectMapper;
  private final AppProperties appProperties;

  public Optional<Link> getLinkByShortCode(String shortCode) {
    return linkRepository.findByShortCode(shortCode);
  }

  public Optional<String> getMetadata(String shortCode) {
    return linkRepository.findByShortCode(shortCode)
        .map(Link::getMetadata);
  }

  @Transactional
  public String createShortLink(String destinationUrl) {

    // Step 1: Parse the incoming URL to extract its components
    UriComponents uriComponents = UriComponentsBuilder.fromUriString(destinationUrl).build();

    // Step 2: Create the metadata JSON object
    ObjectNode metadata = objectMapper.createObjectNode();
    metadata.put("path", uriComponents.getPath());

    ObjectNode paramsNode = objectMapper.createObjectNode();
    uriComponents.getQueryParams().forEach((key, values) -> {
      // Store the first value for each parameter
      if (!values.isEmpty()) {
        paramsNode.put(key, values.get(0));
      }
    });
    metadata.set("parameters", paramsNode);

    // Step 3: Create and save the Link entity
    Link link = new Link();
    link.setDestinationUrl(destinationUrl);
    final Instant now = Instant.now();
    link.setAddedDate(now);
    link.setEditDate(now);
    link.setDestinationUrl(destinationUrl);

    try {
      link.setMetadata(objectMapper.writeValueAsString(metadata));
    } catch (Exception e) {
      // Handle JSON processing exception, maybe throw a custom exception
      throw new BulkloadsException("Failed to serialize metadata for destinationUrl: " + destinationUrl, e);
    }

    // First save to get the ID
    link = linkRepository.save(link);

    // Generate short code from ID using guava's 62
    String shortCode = StringUtil.bigIntegerToBase62(link.getId());
    link.setShortCode(shortCode);

    // Second save with the short code
    linkRepository.save(link);

    return appProperties.getDomainUrl() + LINK_PATH + "/" + shortCode;
  }

}