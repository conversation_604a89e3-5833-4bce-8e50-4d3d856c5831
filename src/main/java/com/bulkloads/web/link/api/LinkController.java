package com.bulkloads.web.link.api;

import java.net.URI;
import com.bulkloads.web.link.api.dto.LinkResponse;
import com.bulkloads.web.link.service.LinkService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/links")
@RequiredArgsConstructor
public class LinkController {

  private final LinkService linkService;

  @Operation(summary = "Redirect to full url")
  @GetMapping("/{short_code}")
  public ResponseEntity<Object> redirect(@PathVariable("short_code") String shortCode) {
    return linkService.getLinkByShortCode(shortCode)
        .map(link -> ResponseEntity.status(HttpStatus.FOUND) // 302 Redirect
            .location(URI.create(link.getDestinationUrl()))
            .build())
        .orElse(ResponseEntity.notFound().build());
  }

  @GetMapping("/{short_code}/details")
  public ResponseEntity<String> getMetadata(@PathVariable("short_code") String shortCode) {
    return linkService.getMetadata(shortCode)
        .map(ResponseEntity::ok)
        .orElse(ResponseEntity.notFound().build());
  }

  @Operation(summary = "Create short link")
  @PostMapping
  public ResponseEntity<LinkResponse> createShortLink(@RequestBody String destinationUrl) {
    String shortLink = linkService.createShortLink(destinationUrl);

    LinkResponse response = LinkResponse.builder()
        .shortLink(shortLink)
        .destinationUrl(destinationUrl)
        .build();

    return ResponseEntity.ok(response);
  }

}
