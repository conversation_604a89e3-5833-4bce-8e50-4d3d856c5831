package com.bulkloads.web.link.domain.entity;

import java.math.BigInteger;
import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "dynamic_links")
@Getter
@Setter
public class Link {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private BigInteger id;

  @Column(name = "short_code", unique = true, length = 20)
  private String shortCode;

  @Column(name = "destination_url", nullable = false, length = 2048)
  private String destinationUrl;

  @Column(name = "metadata", columnDefinition = "JSON")
  private String metadata;

  @Column(name = "added_date", nullable = false)
  private Instant addedDate;

  @Column(name = "edit_date", nullable = false)
  private Instant editDate;

}