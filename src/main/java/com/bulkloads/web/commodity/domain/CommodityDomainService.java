package com.bulkloads.web.commodity.domain;

import static com.bulkloads.common.validation.ValidationUtils.hasChange;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import com.bulkloads.common.BaseDomainService;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.common.validation.ValidationMethod;
import com.bulkloads.web.commodity.domain.data.CommodityData;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.commodity.mapper.CommodityMapper;
import com.bulkloads.web.commodity.repository.CommodityRepository;
import com.bulkloads.web.equipment.domain.entity.Equipment;
import com.bulkloads.web.equipment.repository.EquipmentRepository;
import com.bulkloads.web.rate.repository.RateTypeRepository;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.user.service.UserService;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class CommodityDomainService extends BaseDomainService<Commodity> {

  public static final String KEY_EXISTING_COMMODITY_ID = "existing_commodity_id";

  private final CommodityRepository commodityRepository;
  private final EquipmentRepository equipmentRepository;
  private final RateTypeRepository rateTypeRepository;
  private final CommodityMapper commodityMapper;
  private final UserService userService;

  public Result<Commodity> create(CommodityData data) {
    final Commodity commodity = new Commodity();
    return super.validate(commodity, null, data, ValidationMethod.CREATE);
  }

  public Result<Commodity> update(Commodity commodity, CommodityData data) {
    return validate(commodity, null, data, ValidationMethod.UPDATE);
  }

  public Result<Commodity> delete(Commodity commodity) {
    final int userId = UserUtil.getUserIdOrThrow();
    commodity.setDeleted(true);
    commodity.setDeletedByUserId(userId);
    final Instant now = Instant.now();
    commodity.setDeletedDate(now);
    commodity.setModifiedDate(now);
    return new Result<>(commodity);
  }


  @Override
  public void validateDataAndMapToEntity(Result<Commodity> result, Commodity entity,
                                         Commodity existing, Object commodityData, ValidationMethod method) {
    CommodityData data = (CommodityData) commodityData;

    final User user = userService.getLoggedInUser();
    final Instant now = Instant.now();
    if (method == ValidationMethod.CREATE) {
      entity.setUser(user);
      entity.setUserCompany(user.getUserCompany());

      entity.setAddedByUserId(user.getUserId());
      entity.setAddedDate(now);

    } else if (method == ValidationMethod.UPDATE) {
      entity.setEditByUserId(user.getUserId());
      entity.setEditDate(now);
    }

    entity.setModifiedDate(now);

    // duplicate check
    if (hasChange(entity.getCommodity(), data.getCommodity())) {
      final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
      final Commodity commodityDuplicates =
          commodityRepository.findDuplicate(userCompanyId, data.getCommodity().get(), entity.getCommodityId());
      if (commodityDuplicates != null) {
        result.addError("commodity", "This commodity already exists");
        result.addError("existing_commodity_id",
            String.valueOf(commodityDuplicates.getCommodityId()));
      }
    }

    // equipment_ids
    if (hasChange(entity.getEquipmentIds(), data.getEquipmentIds())) {

      List<Equipment> equips = equipmentRepository
          .findAllById(data.getEquipmentIds().orElse(new ArrayList<>()));

      entity.setEquipmentIds(equips.stream()
          .map(Equipment::getEquipmentId)
          .collect(Collectors.toCollection(ArrayList::new))
      );
      entity.setEquipmentNames(equips.stream()
          .map(Equipment::getEquipmentName)
          .collect(Collectors.toCollection(ArrayList::new))
      );
    }
  }

  public void mapToEntityAuto(Object data, Commodity entity) {
    commodityMapper.dataToEntity((CommodityData) data, entity);
  }


  @Override
  public void validateEntity(Result<Commodity> result, Commodity entity) {
    // default_bill_weight_use
    var def = entity.getDefaultBillWeightUse();
    if (def != null && !def.equals("unload_weight") && !def.equals("loaded_weight") && !def.isEmpty()) {
      result.addError("default_bill_weight_use",
          "Invalid selection for Bill By Origin/Destination Weight for the commodity");
    }
  }

}
