package com.bulkloads.web.commodity.domain.entity;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import com.bulkloads.common.jpa.CsvListSize;
import com.bulkloads.web.common.jpa.converter.CsvStringListConverter;
import com.bulkloads.web.rate.domain.entity.RateProductCategory;
import com.bulkloads.web.user.domain.entity.User;
import com.bulkloads.web.usercompany.domain.entity.UserCompany;
import jakarta.persistence.Column;
import jakarta.persistence.Convert;
import jakarta.persistence.Entity;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "commodities")
@Getter
@Setter
public class Commodity {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "commodity_id")
  private Integer commodityId;

  @Size(max = 100, message = "Up to 100 chars")
  @NotBlank
  @Column(name = "commodity")
  private String commodity = "";

  @Size(max = 50, message = "Up to 50 chars")
  @Column(name = "external_commodity_id")
  private String externalCommodityId = "";

  @Size(max = 10, message = "Up to 10 chars")
  @Column(name = "commodity_abbr")
  private String commodityAbbr = "";

  @Size(max = 20, message = "Up to 20 chars")
  @Column(name = "color_code")
  private String colorCode = "";

  @Size(max = 10, message = "Up to 10 chars")
  @Column(name = "def_rate_type")
  private String defRateType = "2000";

  @ManyToOne
  @JoinColumn(name = "rate_product_category_id")
  private RateProductCategory rateProductCategory;

  @CsvListSize(max = 50)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_ids")
  private List<String> equipmentIds = new ArrayList<>();

  @CsvListSize(max = 300)
  @Convert(converter = CsvStringListConverter.class)
  @Column(name = "equipment_names")
  private List<String> equipmentNames = new ArrayList<>();

  @Size(max = 255, message = "Up to 255 chars")
  @Column(name = "default_bill_weight_use")
  private String defaultBillWeightUse = "unload_weight";

  @NotNull
  @Column(name = "added_date")
  private Instant addedDate;

  @NotNull
  @Column(name = "added_by_user_id")
  private Integer addedByUserId;

  @Column(name = "edit_date")
  private Instant editDate;

  @Column(name = "edit_by_user_id")
  private Integer editByUserId;

  @NotNull
  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted_by_user_id")
  private Integer deletedByUserId;

  @Column(name = "deleted")
  private Boolean deleted = false;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_id")
  private User user;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "user_company_id")
  private UserCompany userCompany;

}
