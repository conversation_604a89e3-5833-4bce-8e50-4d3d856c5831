package com.bulkloads.web.commodity.service;

import static com.bulkloads.web.commodity.domain.CommodityDomainService.KEY_EXISTING_COMMODITY_ID;
import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.commodity.domain.CommodityDomainService;
import com.bulkloads.web.commodity.domain.data.CommodityData;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.commodity.mapper.CommodityMapper;
import com.bulkloads.web.commodity.repository.CommodityRepository;
import com.bulkloads.web.commodity.service.dto.CommodityRequest;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Validated
@RequiredArgsConstructor
public class CommodityService {

  private final CommodityDomainService commodityDomainService;
  private final CommodityRepository commodityRepository;
  private final CommodityMapper commodityMapper;

  public List<CommodityResponse> getCommodities(final String term,
                                                final Integer commodityId,
                                                final Instant lastModifiedDate,
                                                final boolean includeDeleted) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return commodityRepository.getCommodities(userCompanyId, term, commodityId, lastModifiedDate, includeDeleted);
  }

  public CommodityResponse getCommodity(final Integer commodityId) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return commodityRepository.getCommodity(userCompanyId, commodityId);
  }

  public CommodityResponse create(@Valid final CommodityRequest request) {
    final CommodityData commodityData = mapToData(request);

    final Result<Commodity> commodityResult = commodityDomainService.create(commodityData);
    Commodity entity = commodityResult.orElseThrow();

    entity = commodityRepository.save(entity);
    return commodityMapper.entityToResponse(entity);
  }

  public CommodityResponse update(final int commodityId, @Valid final CommodityRequest request) {
    final Commodity existingEntity = findByCommodityIdAndUserCompanyUserCompanyId(commodityId);
    final CommodityData commodityData = mapToData(request);

    final Result<Commodity> commodityResult =
        commodityDomainService.update(existingEntity, commodityData);
    Commodity entity = commodityResult.orElseThrow();

    entity = commodityRepository.save(entity);
    return commodityMapper.entityToResponse(entity);
  }

  public void deleteById(final int commodityId) {
    final Commodity commodity = findByCommodityIdAndUserCompanyUserCompanyId(commodityId);
    commodityDomainService.delete(commodity);
    commodityRepository.save(commodity);
  }

  private CommodityData mapToData(final CommodityRequest request) {
    final Map<String, String> errors = new HashMap<>();
    final CommodityData commodityData = commodityMapper.requestToData(request, errors);

    if (!errors.isEmpty()) {
      throw new ValidationException(errors);
    }
    return commodityData;
  }

  private Commodity findByCommodityIdAndUserCompanyUserCompanyId(final int commodityId) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return commodityRepository
        .findByCommodityIdAndUserCompanyUserCompanyId(commodityId, userCompanyId)
        .orElseThrow(() -> new ValidationException("commodity_id", "Could not find id %s".formatted(commodityId)));
  }


  @Transactional
  public CommodityResponse createOrGetExisting(CommodityRequest request) {
    CommodityData data = mapToData(request);
    Result<Commodity> result = commodityDomainService.create(data);
    if (result.hasError(KEY_EXISTING_COMMODITY_ID)) {
      // If duplicate error exists, get the existing commodity ID
      String existingIdStr = result.getErrors().get(KEY_EXISTING_COMMODITY_ID);
      if (existingIdStr != null) {
        Integer id = Integer.parseInt(existingIdStr);
        return getCommodity(id);
      }
    }

    Commodity commodityToSave = result.orElseThrow();
    Commodity commodity = commodityRepository.save(commodityToSave);
    return commodityMapper.entityToResponse(commodity);
  }

}
