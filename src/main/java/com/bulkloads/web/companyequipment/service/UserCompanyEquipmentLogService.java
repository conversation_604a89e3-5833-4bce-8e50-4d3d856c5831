package com.bulkloads.web.companyequipment.service;

import com.bulkloads.common.UserUtil;
import com.bulkloads.common.validation.Result;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.companyequipment.domain.UserCompanyEquipmentLogDomainService;
import com.bulkloads.web.companyequipment.domain.data.UserCompanyEquipmentLogData;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentLog;
import com.bulkloads.web.companyequipment.mapper.UserCompanyEquipmentLogMapper;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentLogRepository;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogRequest;
import com.bulkloads.web.companyequipment.service.dto.UserCompanyEquipmentLogResponse;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class UserCompanyEquipmentLogService {

  private final UserCompanyEquipmentLogRepository userCompanyEquipmentLogRepository;
  private final UserCompanyEquipmentLogMapper userCompanyEquipmentLogMapper;
  private final UserCompanyEquipmentLogDomainService userCompanyEquipmentLogDomainService;
  private final UserCompanyEquipmentService userCompanyEquipmentService;


  @Transactional
  public UserCompanyEquipmentLogResponse create(
      int userCompanyEquipmentId,
      UserCompanyEquipmentLogRequest request) {

    UserCompanyEquipmentLogData data = userCompanyEquipmentLogMapper.requestToData(request);
    UserCompanyEquipment owningUserCompanyEquipment = userCompanyEquipmentService.findUserCompanyEquipmentById(userCompanyEquipmentId);
    Result<UserCompanyEquipmentLog> result = userCompanyEquipmentLogDomainService.create(owningUserCompanyEquipment, data);
    UserCompanyEquipmentLog userCompanyEquipmentToSave = result.orElseThrow();
    UserCompanyEquipmentLog userCompanyEquipmentLog = userCompanyEquipmentLogRepository.save(userCompanyEquipmentToSave);
    return userCompanyEquipmentLogMapper.entityToResponse(userCompanyEquipmentLog);
  }


  @Transactional
  public UserCompanyEquipmentLogResponse update(
      int userCompanyEquipmentId,
      int userCompanyEquipmentLogId,
      UserCompanyEquipmentLogRequest request) {

    UserCompanyEquipmentLog userCompanyEquipmentToUpdate = findUserCompanyEquipmentLogById(userCompanyEquipmentLogId);
    UserCompanyEquipmentLogData data = userCompanyEquipmentLogMapper.requestToData(request);
    UserCompanyEquipment owningUserCompanyEquipment = userCompanyEquipmentService.findUserCompanyEquipmentById(userCompanyEquipmentId);
    Result<UserCompanyEquipmentLog> result = userCompanyEquipmentLogDomainService.update(owningUserCompanyEquipment, userCompanyEquipmentToUpdate, data);
    UserCompanyEquipmentLog userCompanyEquipmentToSave = result.orElseThrow();
    UserCompanyEquipmentLog userCompanyEquipmentLog = userCompanyEquipmentLogRepository.save(userCompanyEquipmentToSave);
    return userCompanyEquipmentLogMapper.entityToResponse(userCompanyEquipmentLog);
  }


  @Transactional
  public void remove(int userCompanyEquipmentId, int userCompanyEquipmentLogId) {
    UserCompanyEquipmentLog userCompanyEquipmentLog = findUserCompanyEquipmentLogById(userCompanyEquipmentLogId);
    UserCompanyEquipment owningUserCompanyEquipment = userCompanyEquipmentService.findUserCompanyEquipmentById(userCompanyEquipmentId);
    final Result<UserCompanyEquipmentLog> result = userCompanyEquipmentLogDomainService.remove(owningUserCompanyEquipment, userCompanyEquipmentLog);
    userCompanyEquipmentLogRepository.save(result.orElseThrow());
  }

//  public UserCompanyEquipmentLogResponse getUserCompanyEquipmentById(int userCompanyEquipmentLogId) {
//    return userCompanyEquipmentLogMapper.entityToResponse(
//        userCompanyEquipmentLogRepository
//            .fetchUserCompanyEquipmentLogById(UserUtil.getUserCompanyIdOrThrow(), userCompanyEquipmentLogId));
//  }
//
//
//  public List<UserCompanyEquipmentLogResponse> getUserCompanyEquipments() {
//    return userCompanyEquipmentLogRepository
//        .fetchUserCompanyEquipmentLogs(UserUtil.getUserCompanyIdOrThrow())
//        .stream().map(userCompanyEquipmentLogMapper::entityToResponse)
//        .collect(toList());
//  }
//

  public UserCompanyEquipmentLog findUserCompanyEquipmentLogById(
      final int userCompanyEquipmentLogId) {

    return userCompanyEquipmentLogRepository.getUserCompanyEquipmentLog(
            UserUtil.getUserCompanyIdOrThrow(),
            userCompanyEquipmentLogId)
        .orElseThrow(() -> new ValidationException("user_company_equipment_log_id", "You are not allowed to update this equipment log"));
  }

}
