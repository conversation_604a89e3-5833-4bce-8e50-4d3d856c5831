package com.bulkloads.web.companyequipment.domain.entity;

import java.io.Serializable;
import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@EqualsAndHashCode
@Embeddable
@NoArgsConstructor
@AllArgsConstructor
public class UserCompanyEquipmentFileId implements Serializable {

  @Column(name = "user_company_equipment_id")
  Integer userCompanyEquipmentId;

  @Column(name = "file_id")
  Integer fileId;

}