package com.bulkloads.web.state.service;

import java.util.List;
import com.bulkloads.web.state.repository.StateQueryRepository;
import com.bulkloads.web.state.service.dto.StateResponse;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class StateService {


  private final StateQueryRepository stateRepository;

  public List<StateResponse> getStates(
      final String term
  ) {
    return stateRepository.getStates(term);
  }


}
