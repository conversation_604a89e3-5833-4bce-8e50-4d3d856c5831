package com.bulkloads.web.infra.email.domain;

import org.hibernate.validator.constraints.URL;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
@AllArgsConstructor
public class Attachment {

  @NotEmpty
  String filename;
  @NotEmpty
  @URL
  String url;
}
