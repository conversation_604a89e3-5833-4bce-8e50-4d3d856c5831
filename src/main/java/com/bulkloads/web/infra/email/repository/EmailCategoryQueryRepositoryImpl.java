package com.bulkloads.web.infra.email.repository;

import static com.bulkloads.web.infra.email.repository.template.UserEmailCategoryQueryTemplate.USER_EMAIL_CATEGORY_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.infra.email.dto.transformer.EmailCategoryResponseTransformer;
import com.bulkloads.web.setting.service.dto.NotificationSettingResponse;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class EmailCategoryQueryRepositoryImpl implements EmailCategoryQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final EmailCategoryResponseTransformer emailCategoryResponseTransformer;

  @Override
  public List<NotificationSettingResponse> getNotificationSettings(Integer userId) {
    Map<String, Object> params = new HashMap<>();

    params.put("user_id", userId);

    return jpaNativeQueryService.query(USER_EMAIL_CATEGORY_QUERY_TEMPLATE, params, emailCategoryResponseTransformer);
  }

  @Override
  public NotificationSettingResponse getNotificationSettingsId(Integer userId, Integer emailCategoryId) {
    Map<String, Object> params = new HashMap<>();

    params.put("user_id", userId);
    params.put("email_category_id", emailCategoryId);

    return jpaNativeQueryService.queryForObject(USER_EMAIL_CATEGORY_QUERY_TEMPLATE, params, emailCategoryResponseTransformer);
  }

}
