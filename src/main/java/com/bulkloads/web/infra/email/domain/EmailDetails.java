package com.bulkloads.web.infra.email.domain;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import lombok.Builder;
import lombok.Value;

@Value
@Builder
public class EmailDetails {

  Integer senderUserId;
  @NotEmpty
  String fromEmail;
  @NotEmpty
  String replyToEmail;
  @NotEmpty
  List<String> toEmails;
  @NotEmpty
  String subject;
  @NotEmpty
  String message;
  Integer toUserId;
  String cc;
  String bcc;
  String toName;
  String fromName;
  @NotEmpty
  String failTo;
  Integer siteId;
  String description;
  @Builder.Default
  List<@Valid Attachment> attachments = Collections.emptyList();
  Integer category;
  boolean prioritise;

  public Optional<Integer> getSenderUserId() {
    return Optional.ofNullable(senderUserId);
  }

  public Optional<Integer> getToUserId() {
    return Optional.ofNullable(toUserId);
  }

  public Optional<String> getCc() {
    return Optional.ofNullable(cc);
  }

  public Optional<String> getBcc() {
    return Optional.ofNullable(bcc);
  }

  public Optional<String> getToName() {
    return Optional.ofNullable(toName);
  }

  public Optional<String> getFromName() {
    return Optional.ofNullable(fromName);
  }
}