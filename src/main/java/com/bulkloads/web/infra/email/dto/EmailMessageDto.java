package com.bulkloads.web.infra.email.dto;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Builder;
import lombok.Value;
import lombok.extern.jackson.Jacksonized;

@Value
@Builder
@Jacksonized
public class EmailMessageDto {

  @Positive
  Integer emailId;
  @NotBlank
  String fromEmail;
  @NotBlank
  String subject;
  @NotBlank
  String message;
  @NotEmpty
  List<@NotEmpty String> toEmails;
  String cc;
  String bcc;
  @NotBlank
  String replyToEmail;
  @NotNull
  Instant sendDate;
  @NotEmpty
  String failTo;
  List<@Valid AttachmentDto> attachments;
  Map<String, String> headers;
  String description;

  public Optional<Integer> getEmailId() {
    return Optional.ofNullable(emailId);
  }

  public Optional<String> getCc() {
    return Optional.ofNullable(cc);
  }

  public Optional<String> getBcc() {
    return Optional.ofNullable(bcc);
  }
}
