package com.bulkloads.web.infra.quickbooks.quickbooks;

import java.util.ArrayList;
import java.util.List;
import com.bulkloads.exception.BulkloadsException;
import com.intuit.ipp.data.IntuitEntity;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.services.QueryResult;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class QbRepository {

  private static final int PAGE_SIZE = 100;
  private final QbDataService qbDataService;

  protected <T extends IntuitEntity> T findById(final Class<T> clazz, final String id) {
    final DataService dataService = qbDataService.getDataService();
    try {
      T entity = clazz.getDeclaredConstructor().newInstance();
      entity.setId(id);
      return dataService.findById(entity);
    } catch (FMSException e) {
      throw new BulkloadsException("Error when calling QuickBooks findById for " + clazz.getSimpleName(), e);
    } catch (ReflectiveOperationException e) {
      throw new BulkloadsException("Error creating instance of " + clazz.getSimpleName(), e);
    }
  }

  protected <T extends IntuitEntity> List<T> findAll(final Class<T> clazz) {
    return findAllWithFilter(clazz, null);
  }

  protected <T extends IntuitEntity> List<T> findAllIsActiveTrue(final Class<T> clazz) {
    return findAllWithFilter(clazz, "Active = true");
  }

  protected <T extends IntuitEntity> T save(final T entity) {
    final DataService dataService = qbDataService.getDataService();
    try {
      return dataService.add(entity);
    } catch (FMSException e) {
      throw new BulkloadsException("Error when calling QuickBooks add for " + entity.getClass().getSimpleName(), e);
    }
  }

  protected <T extends IntuitEntity> void delete(final T entity) {
    final DataService dataService = qbDataService.getDataService();
    try {
      dataService.delete(entity);
    } catch (FMSException e) {
      throw new BulkloadsException("Error when calling QuickBooks delete for " + entity.getClass().getSimpleName(), e);
    }
  }

  protected <T extends IntuitEntity> void voidRequest(final T entity) {
    final DataService dataService = qbDataService.getDataService();
    try {
      dataService.voidRequest(entity);
    } catch (FMSException e) {
      throw new BulkloadsException("Error when calling QuickBooks voidRequest for " + entity.getClass().getSimpleName(), e);
    }
  }

  private <T extends IntuitEntity> List<T> findAllWithFilter(final Class<T> clazz, final String whereClause) {
    final List<T> allEntities = new ArrayList<>();
    final String entityName = clazz.getSimpleName();
    final DataService dataService = qbDataService.getDataService();

    int startPosition = 1;
    boolean hasMoreRecords = true;

    try {
      while (hasMoreRecords) {
        final StringBuilder queryBuilder = new StringBuilder("SELECT * FROM " + entityName);
        if (StringUtils.hasText(whereClause)) {
          queryBuilder.append(" WHERE ").append(whereClause);
        }
        queryBuilder.append(" STARTPOSITION ").append(startPosition).append(" MAXRESULTS ").append(PAGE_SIZE);

        final QueryResult queryResult = dataService.executeQuery(queryBuilder.toString());

        final List<T> pageEntities = queryResult.getEntities()
            .stream()
            .map(clazz::cast)
            .toList();

        allEntities.addAll(pageEntities);

        if (pageEntities.size() < PAGE_SIZE) {
          hasMoreRecords = false;
        } else {
          startPosition += PAGE_SIZE;
        }
      }

      return allEntities;
    } catch (FMSException e) {
      throw new BulkloadsException("Error when calling QuickBooks executeQuery for " + clazz.getSimpleName(), e);
    }
  }
}
