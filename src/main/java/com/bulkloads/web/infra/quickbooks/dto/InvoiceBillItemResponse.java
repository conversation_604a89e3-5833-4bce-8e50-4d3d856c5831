package com.bulkloads.web.infra.quickbooks.dto;

import java.math.BigDecimal;
import java.time.LocalDate;

public record InvoiceBillItemResponse(
    int loadAssignmentId,
    String loadAssignmentNumber,
    LocalDate hauledDate,
    BigDecimal itemAmount,
    String pickupCity,
    String pickupState,
    String dropCity,
    String dropState,
    boolean isSurcharge,
    String surchargeType,
    String qbItemId) {
}
