package com.bulkloads.web.infra.quickbooks.mapper;

import java.util.List;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.infra.quickbooks.dto.CompanyInfoDto;
import com.bulkloads.web.infra.quickbooks.dto.CustomerDto;
import com.bulkloads.web.infra.quickbooks.dto.ItemDto;
import com.bulkloads.web.infra.quickbooks.dto.VendorDto;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.Item;
import com.intuit.ipp.data.Vendor;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    uses = CommonMapper.class)
public abstract class QbMapper {

  @Mapping(source = "id", target = "id")
  @Mapping(source = "companyName", target = "name")
  @Mapping(source = "email.address", target = "email")
  public abstract CompanyInfoDto mapCompanyInfo(final CompanyInfo companyInfo);

  @Mapping(source = "id", target = "id")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "displayName", target = "companyName")
  @Mapping(source = "givenName", target = "firstName")
  @Mapping(source = "familyName", target = "lastName")
  public abstract CustomerDto mapCustomer(final Customer entity);

  public List<CustomerDto> mapCustomers(final List<Customer> entities) {
    return entities.stream().map(this::mapCustomer).toList();
  }

  @Mapping(source = "id", target = "id")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "displayName", target = "companyName")
  @Mapping(source = "givenName", target = "firstName")
  @Mapping(source = "familyName", target = "lastName")
  public abstract VendorDto mapVendor(final Vendor entity);

  public List<VendorDto> mapVendors(final List<Vendor> entities) {
    return entities.stream().map(this::mapVendor).toList();
  }

  @Mapping(source = "id", target = "id")
  @Mapping(source = "active", target = "active")
  @Mapping(source = "name", target = "name")
  @Mapping(source = "type", target = "type")
  public abstract ItemDto mapItem(final Item entity);

  public List<ItemDto> mapPayments(final List<Item> entities) {
    return entities.stream().map(this::mapItem).toList();
  }
}
