package com.bulkloads.web.infra.quickbooks.quickbooks;

import static com.bulkloads.config.AppConstants.AccountingProviderId.QUICKBOOKS;
import static com.bulkloads.config.AppConstants.QuickBooksEnvironments.PRODUCTION;
import static java.util.Objects.isNull;

import java.util.Collections;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.BulkloadsException;
import com.intuit.ipp.core.Context;
import com.intuit.ipp.core.ServiceType;
import com.intuit.ipp.exception.FMSException;
import com.intuit.ipp.security.OAuth2Authorizer;
import com.intuit.ipp.services.DataService;
import com.intuit.ipp.util.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.client.OAuth2AuthorizeRequest;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.core.OAuth2AccessToken;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
public class QbServiceFactory {

  public static final String SELECT_REALM_ID_SQL = """
          SELECT realm_id FROM oauth2_authorized_client_metadata WHERE principal_name = ?
      """;

  private final OAuth2AuthorizedClientManager authorizedClientManager;
  private final JdbcTemplate jdbcTemplate;
  private final String environment;

  public QbServiceFactory(final OAuth2AuthorizedClientManager authorizedClientManager,
                          final JdbcTemplate jdbcTemplate,
                          @Value("${quickbooks.environment:sandbox}")
                                  final String environment) {
    this.authorizedClientManager = authorizedClientManager;
    this.jdbcTemplate = jdbcTemplate;
    this.environment = environment;
  }

  protected DataService createDataService() {
    final Context context = buildContext();
    if (!PRODUCTION.equalsIgnoreCase(environment)) {
      log.debug("Using sandbox environment");
      Config.setProperty(Config.BASE_URL_QBO, "https://sandbox-quickbooks.api.intuit.com/v3/company");
    }
    log.info("QuickBooks DataService created successfully");
    return new DataService(context);
  }

  private Context buildContext() {
    log.debug("Building QuickBooks context");
    final OAuth2AuthorizedClient client = getAuthorizedClient();
    final OAuth2AccessToken accessToken = client.getAccessToken();
    final String tokenValue = accessToken.getTokenValue();
    final String principalName = client.getPrincipalName();

    final String realmId = findRealmIdByPrincipalName(principalName);
    final OAuth2Authorizer authorizer = new OAuth2Authorizer(tokenValue);
    final ServiceType serviceType = ServiceType.QBO;

    try {
      log.debug("Creating context with realmId: {}", realmId);
      return new Context(authorizer, serviceType, realmId);
    } catch (FMSException e) {
      final String msg = "Failed to create QuickBooks context";
      log.error(msg, e);
      throw new BulkloadsException(msg, e);
    }
  }

  private OAuth2AuthorizedClient getAuthorizedClient() {
    final String principalName = String.valueOf(UserUtil.getUserIdOrThrow());
    final Authentication authentication = new UsernamePasswordAuthenticationToken(principalName, null, Collections.emptyList());
    final OAuth2AuthorizeRequest authorizeRequest = OAuth2AuthorizeRequest
        .withClientRegistrationId(QUICKBOOKS)
        .principal(authentication)
        .build();

    final OAuth2AuthorizedClient oAuth2AuthorizedClient = authorizedClientManager.authorize(authorizeRequest);
    if (isNull(oAuth2AuthorizedClient)) {
      log.error("No authorized client found for principal: {}", principalName);
      throw new BulkloadsException("No authorized client found for QuickBooks");
    }

    return oAuth2AuthorizedClient;
  }

  private String findRealmIdByPrincipalName(final String principalName) {
    log.debug("Finding realm ID for principal: {}", principalName);
    return jdbcTemplate.queryForObject(SELECT_REALM_ID_SQL, String.class, principalName);
  }
}
