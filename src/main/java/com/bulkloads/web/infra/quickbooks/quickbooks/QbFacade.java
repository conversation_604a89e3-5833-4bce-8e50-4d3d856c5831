package com.bulkloads.web.infra.quickbooks.quickbooks;

import java.math.BigDecimal;
import java.util.List;
import com.bulkloads.web.addressbook.abuser.domain.entity.AbUser;
import com.bulkloads.web.infra.quickbooks.dto.CompanyInfoDto;
import com.bulkloads.web.infra.quickbooks.dto.CreateBillDto;
import com.bulkloads.web.infra.quickbooks.dto.CreateInvoiceDto;
import com.bulkloads.web.infra.quickbooks.dto.CustomerDto;
import com.bulkloads.web.infra.quickbooks.dto.InvoiceItemDto;
import com.bulkloads.web.infra.quickbooks.dto.ItemDto;
import com.bulkloads.web.infra.quickbooks.dto.VendorDto;
import com.bulkloads.web.infra.quickbooks.mapper.QbMapper;
import com.bulkloads.web.user.domain.entity.User;
import com.intuit.ipp.data.AccountBasedExpenseLineDetail;
import com.intuit.ipp.data.Bill;
import com.intuit.ipp.data.CompanyInfo;
import com.intuit.ipp.data.Customer;
import com.intuit.ipp.data.EmailAddress;
import com.intuit.ipp.data.Invoice;
import com.intuit.ipp.data.Item;
import com.intuit.ipp.data.Line;
import com.intuit.ipp.data.LineDetailTypeEnum;
import com.intuit.ipp.data.Payment;
import com.intuit.ipp.data.ReferenceType;
import com.intuit.ipp.data.SalesItemLineDetail;
import com.intuit.ipp.data.TelephoneNumber;
import com.intuit.ipp.data.Vendor;
import com.intuit.ipp.data.WebSiteAddress;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;

@Component
@RequiredArgsConstructor
public class QbFacade {

  private final QbRepository qbRepository;
  private final QbMapper qbMapper;

  public CompanyInfoDto findCompanyInfo() {
    final List<CompanyInfo> companyInfos = qbRepository.findAll(CompanyInfo.class);
    return qbMapper.mapCompanyInfo(companyInfos.get(0));
  }

  public CustomerDto findCustomerById(final String id) {
    final Customer entity = qbRepository.findById(Customer.class, id);
    return qbMapper.mapCustomer(entity);
  }

  public List<CustomerDto> findAllCustomers() {
    final List<Customer> entities = qbRepository.findAllIsActiveTrue(Customer.class);
    return qbMapper.mapCustomers(entities);
  }

  public VendorDto findVendorById(final String id) {
    final Vendor entity = qbRepository.findById(Vendor.class, id);
    return qbMapper.mapVendor(entity);
  }

  public List<VendorDto> findAllVendors() {
    final List<Vendor> entities = qbRepository.findAllIsActiveTrue(Vendor.class);
    return qbMapper.mapVendors(entities);
  }

  public ItemDto findItemById(final String id) {
    final Item entity = qbRepository.findById(Item.class, id);
    return qbMapper.mapItem(entity);
  }

  public List<ItemDto> findAllItems() {
    final List<Item> entities = qbRepository.findAllIsActiveTrue(Item.class);
    return qbMapper.mapPayments(entities);
  }

  public String createInvoice(final CreateInvoiceDto dto) {
    final Invoice invoice = new Invoice();

    invoice.setTotalCostAmount(dto.getTotalCostAmount());
    invoice.setCustomerRef(createReferenceType(dto.getCustomerId()));

    final List<Line> lines = dto.getInvoiceItems().stream()
        .map(invoiceItemDto -> createLine(invoiceItemDto, LineDetailTypeEnum.SALES_ITEM_LINE_DETAIL))
        .toList();

    invoice.setLine(lines);

    final Invoice entity = qbRepository.save(invoice);
    return entity.getId();
  }

  public String createBill(final CreateBillDto dto) {
    final Bill bill = new Bill();

    bill.setTotalCostAmount(dto.getTotalCostAmount());
    bill.setVendorRef(createReferenceType(dto.getVendorId()));

    final List<Line> lines = dto.getInvoiceItems().stream()
        .map(invoiceItemDto -> createLine(invoiceItemDto, LineDetailTypeEnum.ACCOUNT_BASED_EXPENSE_LINE_DETAIL))
        .toList();

    bill.setLine(lines);

    final Bill entity = qbRepository.save(bill);
    return entity.getId();
  }

  //TODO fill in
  public String createCustomer(final AbUser abUser) {
    final Customer customer = new Customer();

    customer.setGivenName(abUser.getFirstName());
//    customer.setMiddleName("B");
    customer.setFamilyName(abUser.getLastName());
//    customer.setTitle("Mr");
//    customer.setSuffix("Jr");
    customer.setDisplayName("customerAbUser " + abUser.getFirstName() + " " + abUser.getLastName());
    customer.setCompanyName(abUser.getUserCompany().getCompanyName());
//    customer.setFullyQualifiedName("King Groceries");
    customer.setNotes(abUser.getAbUserNotes());

    final EmailAddress emailAddress = new EmailAddress();
    emailAddress.setAddress(abUser.getEmail());
    customer.setPrimaryEmailAddr(emailAddress);

    final TelephoneNumber primaryPhone = new TelephoneNumber();
    primaryPhone.setFreeFormNumber(abUser.getPhone1());
    customer.setPrimaryPhone(primaryPhone);

//    final PhysicalAddress address = new PhysicalAddress();
//    address.setLine1("123 Main Street");
//    address.setCity("Mountain View");
//    address.setCountrySubDivisionCode("CA");
//    address.setPostalCode("94042");
//    address.setCountry("USA");
//    customer.setBillAddr(address);

    final Customer entity = qbRepository.save(customer);
    return entity.getId();
  }

  //TODO fill in
  public String createVendor(final AbUser abUser) {
    final Vendor vendor = new Vendor();

    vendor.setGivenName(abUser.getFirstName());
    vendor.setFamilyName(abUser.getLastName());
//    vendor.setTitle("Ms.");
//    vendor.setSuffix("Sr.");
    vendor.setDisplayName("vendorAbUser " + abUser.getFirstName() + " " + abUser.getLastName());
    vendor.setCompanyName(abUser.getUserCompany().getCompanyName());
//    vendor.setPrintOnCheckName("Theo's Auto Shop");
//    vendor.setAcctNum("35372649");
//    vendor.setTaxIdentifier("99-5688293");

    final EmailAddress emailAddress = new EmailAddress();
    emailAddress.setAddress(abUser.getEmail());
    vendor.setPrimaryEmailAddr(emailAddress);

    final WebSiteAddress webAddress = new WebSiteAddress();
//    webAddress.setURI("http://TheosAutoShop.com");
    vendor.setWebAddr(webAddress);

    final TelephoneNumber primaryPhone = new TelephoneNumber();
    primaryPhone.setFreeFormNumber(abUser.getPhone1());
    vendor.setPrimaryPhone(primaryPhone);

    final TelephoneNumber mobilePhone = new TelephoneNumber();
    mobilePhone.setFreeFormNumber(abUser.getPhone2());
    vendor.setMobile(mobilePhone);

//    final PhysicalAddress address = new PhysicalAddress();
//    address.setLine1("Theo's Auto Shop");
//    address.setLine2("Theo Bradley");
//    address.setLine3("29834 Mustang Ave.");
//    address.setCity("Millbrae");
//    address.setCountrySubDivisionCode("CA");
//    address.setPostalCode("94030");
//    address.setCountry("U.S.A");
//    vendor.setBillAddr(address);

    final Vendor entity = qbRepository.save(vendor);
    return entity.getId();
  }

  //TODO fill in
  public String createVendor(final User user) {
    final Vendor vendor = new Vendor();

    vendor.setGivenName(user.getFirstName());
    vendor.setFamilyName(user.getLastName());
//    vendor.setTitle("Ms.");
//    vendor.setSuffix("Sr.");
    vendor.setDisplayName("vendorUser " + user.getFirstName() + " " + user.getLastName());
    vendor.setCompanyName(user.getUserCompany().getCompanyName());
//    vendor.setPrintOnCheckName("Theo's Auto Shop");
//    vendor.setAcctNum("35372649");
//    vendor.setTaxIdentifier("99-5688293");

    final EmailAddress emailAddress = new EmailAddress();
    emailAddress.setAddress(user.getEmail());
    vendor.setPrimaryEmailAddr(emailAddress);

    final WebSiteAddress webAddress = new WebSiteAddress();
//    webAddress.setURI("http://TheosAutoShop.com");
    vendor.setWebAddr(webAddress);

    final TelephoneNumber primaryPhone = new TelephoneNumber();
    primaryPhone.setFreeFormNumber(user.getPhone1());
    vendor.setPrimaryPhone(primaryPhone);

    final TelephoneNumber mobilePhone = new TelephoneNumber();
    mobilePhone.setFreeFormNumber(user.getPhone2());
    vendor.setMobile(mobilePhone);

//    final PhysicalAddress address = new PhysicalAddress();
//    address.setLine1("Theo's Auto Shop");
//    address.setLine2("Theo Bradley");
//    address.setLine3("29834 Mustang Ave.");
//    address.setCity("Millbrae");
//    address.setCountrySubDivisionCode("CA");
//    address.setPostalCode("94030");
//    address.setCountry("U.S.A");
//    vendor.setBillAddr(address);

    final Vendor entity = qbRepository.save(vendor);
    return entity.getId();
  }

  //TODO fill in
  public String createPayment() {
    Payment payment = new Payment();
    payment.setTotalAmt(BigDecimal.valueOf(25));
    final ReferenceType customerRef = createReferenceType("20");
    payment.setCustomerRef(customerRef);
    final Payment entity = qbRepository.save(payment);
    return entity.getId();
  }

  public void voidInvoice(final String id) {
    final Invoice invoice = qbRepository.findById(Invoice.class, id);
    qbRepository.voidRequest(invoice);
  }

  private Line createLine(final InvoiceItemDto invoiceItem, final LineDetailTypeEnum lineDetailTypeEnum) {
    final Line line = new Line();
    line.setAmount(invoiceItem.getAmount());
    line.setDetailType(lineDetailTypeEnum);

    if (lineDetailTypeEnum == LineDetailTypeEnum.ACCOUNT_BASED_EXPENSE_LINE_DETAIL) {
      final AccountBasedExpenseLineDetail accountBasedExpenseLineDetail = new AccountBasedExpenseLineDetail();
      final ReferenceType accountRef = new ReferenceType();
      accountRef.setValue(invoiceItem.getQbItemId());
      accountBasedExpenseLineDetail.setAccountRef(accountRef);
      line.setAccountBasedExpenseLineDetail(accountBasedExpenseLineDetail);
    } else {
      final SalesItemLineDetail salesItemLineDetail = new SalesItemLineDetail();
      final ReferenceType itemRef = new ReferenceType();
      itemRef.setValue(invoiceItem.getQbItemId());
      salesItemLineDetail.setItemRef(itemRef);
      salesItemLineDetail.setUnitPrice(invoiceItem.getUnitPrice());
      salesItemLineDetail.setQty(invoiceItem.getQty());
      line.setSalesItemLineDetail(salesItemLineDetail);
    }

    return line;
  }

  private ReferenceType createReferenceType(final String id) {
    final ReferenceType referenceType = new ReferenceType();
    referenceType.setValue(id);
    return referenceType;
  }

}
