package com.bulkloads.web.infra.messaging.consumer;

import java.time.Instant;
import java.util.Map;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.notification.domain.entity.Notification;
import com.bulkloads.web.notification.domain.entity.NotificationDevice;
import com.bulkloads.web.notification.repository.NotificationDeviceRepository;
import com.bulkloads.web.notification.repository.NotificationRepository;
import com.bulkloads.web.notification.service.dto.NotificationMessageDto;
import com.bulkloads.web.user.domain.entity.UserDevice;
import com.bulkloads.web.user.repository.UserDeviceRepository;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@Validated
@RequiredArgsConstructor
public class NotificationQueueConsumer {

  private final UserDeviceRepository userDeviceRepository;
  private final NotificationRepository notificationRepository;
  private final NotificationDeviceRepository notificationDeviceRepository;
  private final FirebaseMessaging firebaseMessaging;
  private final ObjectMapper mapper;

  @RabbitListener(queues = "${bulkloads.notification.queue-name}", errorHandler = "bulkloadsRabbitListenerErrorHandler")
  public void receive(@Valid @Payload final NotificationMessageDto notificationMessageDto) {
    log.trace("Received notification queue message {}", notificationMessageDto);

    int notyId = notificationMessageDto.getNotificationId();
    Notification notification = notificationRepository
        .findById(notyId)
        .orElseThrow(() -> new BulkloadsException("notificationId not found: " + notyId));

    userDeviceRepository
        .findByUserUserIdAndPushEnabledIsTrue(notificationMessageDto.getUserId())
        .forEach(userDevice -> sendToDevice(userDevice, notification));

  }

  public void sendToDevice(UserDevice device, Notification notification) {

    NotificationDevice nd = new NotificationDevice();
    nd.setNotificationId(notification.getNotificationId());
    nd.setDeviceId(device.getDeviceId());
    nd.setNotificationToken(device.getNotificationToken());
    nd.setDeviceData(notification.getData());
    nd.setDateAdded(Instant.now());

    nd = notificationDeviceRepository.save(nd);

    com.google.firebase.messaging.Notification fcmNotification = com.google.firebase.messaging.Notification.builder()
        .setTitle(notification.getTitle())
        .setBody(notification.getBody())
        .build();

    Map<String, String> data;
    try {
      data = mapper.readValue(notification.getData(), new TypeReference<>() {
      });
    } catch (JsonProcessingException e) {
      throw new BulkloadsException("Error parsing notification data " + notification.getData(), e);
    }
    nd.setNotificationJson(buildNotificationJson(data, device.getNotificationToken(), notification.getTitle(), notification.getBody()));


    Message msg = Message.builder()
        .setToken(device.getNotificationToken())
        .setNotification(fcmNotification)
        .putAllData(data)
        .build();

    try {
      String messageId = firebaseMessaging.send(msg);
      nd.setIsSent(true);
      nd.setDateSent(Instant.now());
      nd.setFcmMessageId(messageId);
    } catch (FirebaseMessagingException e) {
      nd.setErrormsg(e.toString());

      // disable notifications for that device
      device.setPushEnabled(false);
      device.setDateUpdated(Instant.now());
      userDeviceRepository.save(device);

    }
    notificationDeviceRepository.save(nd);
  }

  private Map<String, Object> buildNotificationJson(Map<String, String> data, final String notificationToken,
                                                    final String title, final String body) {
    return Map.of("data", data,
        "token", notificationToken,
        "notification",
          Map.of("body", body,
            "title", title));
  }

}
