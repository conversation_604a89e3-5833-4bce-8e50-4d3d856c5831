package com.bulkloads.web.infra.dynlink;

import com.bulkloads.web.link.service.LinkService;
import com.bulkloads.web.load.domain.template.DynamicLink;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class DynamicLinkService {

  private final FirebaseDynamicLinkFacade firebaseDynamicLinkFacade;
  private final LinkService linkService;

  public DynamicLink createDynamicLink(final String url) {
    // return firebaseDynamicLinkFacade.createDynamicLink(url, false);
    DynamicLink dl = DynamicLink.builder()
        .originalLink(url)
        .dynamicLink(linkService.createShortLink(url))
        .build();
    
    return dl;
  }
}
