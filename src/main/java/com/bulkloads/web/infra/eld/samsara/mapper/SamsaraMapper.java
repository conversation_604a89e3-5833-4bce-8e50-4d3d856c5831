package com.bulkloads.web.infra.eld.samsara.mapper;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import com.bulkloads.web.infra.eld.samsara.dto.Driver;
import com.bulkloads.web.infra.eld.samsara.dto.DriverHosStatus;
import com.bulkloads.web.infra.eld.samsara.dto.Vehicle;
import com.bulkloads.web.infra.eld.samsara.dto.VehicleStats;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    uses = CommonMapper.class,
    imports = UserUtil.class)
public abstract class SamsaraMapper {

  @Mapping(target = "eldProviderId", constant = "samsara")
  @Mapping(source = "id", target = "externalId")
  @Mapping(target = "firstName", expression = "java(driver.name().contains(\" \") ? driver.name().substring(0, driver.name().indexOf(\" \")) : driver.name())")
  @Mapping(target = "lastName", expression = "java(driver.name().contains(\" \") ? driver.name().substring(driver.name().indexOf(\" \")) : driver.name())")
  @Mapping(target = "email", ignore = true)
  @Mapping(source = "username", target = "username")
  @Mapping(source = "notes", target = "notes")
  @Mapping(source = "driverActivationStatus", target = "status")
  @Mapping(source = "phone", target = "phone1")
  @Mapping(source = "timezone", target = "timezone")
  @Mapping(source = "updatedAtTime", target = "updatedAt")
  @Mapping(target = "syncUserId", expression = "java(UserUtil.getUserIdOrThrow())")
  @Mapping(target = "userCompanyId", expression = "java(UserUtil.getUserCompanyIdOrThrow())")
  @Mapping(target = "userId", ignore = true)
  @Mapping(target = "abUserId", ignore = true)
  public abstract EldDriverDto mapDriver(final Driver driver);

  public List<EldDriverDto> mapDrivers(final List<Driver> drivers) {
    return drivers.stream().map(this::mapDriver).toList();
  }

  @Mapping(target = "eldProviderId", constant = "samsara")
  @Mapping(source = "id", target = "externalId")
  @Mapping(source = "licensePlate", target = "licensePlateNumber")
  @Mapping(target = "status", ignore = true)
  @Mapping(source = "year", target = "modelYear")
  @Mapping(source = "updatedAtTime", target = "updatedAt")
  @Mapping(target = "syncUserId", expression = "java(UserUtil.getUserIdOrThrow())")
  @Mapping(target = "userCompanyId", expression = "java(UserUtil.getUserCompanyIdOrThrow())")
  @Mapping(target = "userCompanyEquipmentId", ignore = true)
  public abstract EldVehicleDto mapVehicle(final Vehicle vehicle);

  public List<EldVehicleDto> mapVehicles(final List<Vehicle> vehicles) {
    return vehicles.stream().map(this::mapVehicle).toList();
  }

  @Mapping(target = "eldProviderId", constant = "samsara")
  @Mapping(source = "driver.id", target = "externalId")
  @Mapping(source = "clocks._break.timeUntilBreakDurationMs", target = "breakTimeRemaining")
  @Mapping(source = "clocks.cycle.cycleRemainingDurationMs", target = "cycleTimeRemaining")
  @Mapping(source = "clocks.cycle.cycleStartedAtTime", target = "cycleStartedTime")
  @Mapping(expression = "java(driverHosStatus.clocks().cycle().cycleTomorrowDurationMs() / 1000)", target = "cycleTomorrowTimeRemaining")
  @Mapping(expression = "java(driverHosStatus.clocks().drive().driveRemainingDurationMs() / 1000)", target = "driveTimeRemaining")
  @Mapping(expression = "java(driverHosStatus.clocks().shift().shiftRemainingDurationMs() / 1000)", target = "shiftTimeRemaining")
  @Mapping(source = "driverHosStatus.currentDutyStatus.hosStatusType", target = "currentDutyStatus")
  public abstract EldDriverHosDto mapDriverHosStatuses(final DriverHosStatus driverHosStatus);

  public List<EldDriverHosDto> mapDriverHosStatuses(final List<DriverHosStatus> driverHosStatuses) {
    return driverHosStatuses.stream().map(this::mapDriverHosStatuses).toList();
  }

  @Mapping(target = "eldProviderId", constant = "samsara")
  @Mapping(source = "id", target = "externalId")
  @Mapping(source = "gps.headingDegrees", target = "bearing")
  @Mapping(source = "gps.latitude", target = "latitude")
  @Mapping(source = "gps.longitude", target = "longitude")
  @Mapping(source = "gps.speedMilesPerHour", target = "mph")
  @Mapping(source = "gps.time", target = "timestamp")
  public abstract EldVehicleLocationDto mapVehicleLocations(final VehicleStats data);

  public List<EldVehicleLocationDto> mapVehicleLocations(final List<VehicleStats> vehicleStats) {
    return vehicleStats.stream().map(this::mapVehicleLocations).toList();
  }
}
