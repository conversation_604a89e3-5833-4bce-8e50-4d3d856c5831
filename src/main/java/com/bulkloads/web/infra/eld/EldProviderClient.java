package com.bulkloads.web.infra.eld;

import static org.springframework.security.oauth2.client.web.reactive.function.client.ServletOAuth2AuthorizedClientExchangeFilterFunction.oauth2AuthorizedClient;
import java.util.List;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientManager;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.util.MultiValueMap;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

@Slf4j
public abstract class EldProviderClient {

  protected final OAuth2AuthorizedClientService authorizedClientService;
  protected final WebClient webClient;

  protected EldProviderClient(final OAuth2AuthorizedClientService authorizedClientService,
                              final OAuth2AuthorizedClientManager authorizedClientManager) {
    this.authorizedClientService = authorizedClientService;
    this.webClient = buildWebClient(authorizedClientManager);
  }

  protected abstract WebClient buildWebClient(final OAuth2AuthorizedClientManager authorizedClientManager);

  protected abstract String getClientName();

  protected abstract List<EldDriverDto> fetchDrivers();

  protected abstract List<EldVehicleDto> fetchVehicles();

  protected abstract List<EldVehicleLocationDto> fetchVehicleLocations(final List<String> externalIds);

  protected abstract List<EldDriverHosDto> fetchDriverHosStatuses(final List<String> externalIds);

  protected <T> T makeApiCall(final String endpoint, final HttpMethod method, final MultiValueMap<String, String> queryParams, final Class<T> responseType) {
    return webClient
        .method(method)
        .uri(uriBuilder -> {
          uriBuilder.path(endpoint);
          queryParams.forEach((key, values) -> values.forEach(value -> uriBuilder.queryParam(key, value)));
          return uriBuilder.build();
        })
        .attributes(oauth2AuthorizedClient(getOAuth2AuthorizedClient()))
        .retrieve()
        .onStatus(httpStatus -> httpStatus.value() == HttpStatus.NOT_FOUND.value(), this::logError)
        .onStatus(HttpStatusCode::isError, this::throwError)
        .bodyToMono(responseType)
        .block();
  }

  private OAuth2AuthorizedClient getOAuth2AuthorizedClient() {
    final String principalName = "" + UserUtil.getUserIdOrThrow();
    final String clientName = getClientName();
    return Optional
        .ofNullable(authorizedClientService.<OAuth2AuthorizedClient>loadAuthorizedClient(clientName, principalName))
        .orElseThrow(() -> new BulkloadsException("%s API client not found for user: %s".formatted(clientName, principalName)));
  }

  private Mono<RuntimeException> logError(final ClientResponse clientResponse) {
    return clientResponse.bodyToMono(String.class)
        .doOnNext(body -> log.error("{} API error: {}", getClientName(), body))
        .then(Mono.empty());
  }

  private Mono<RuntimeException> throwError(final ClientResponse clientResponse) {
    return clientResponse.bodyToMono(String.class)
        .map(body -> new BulkloadsException(getClientName() + " API error: %s".formatted(body)));
  }
}
