package com.bulkloads.web.infra.eld.motive.mapper;

import java.util.List;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.infra.eld.dto.EldDriverDto;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import com.bulkloads.web.infra.eld.motive.dto.DriverAvailableTime;
import com.bulkloads.web.infra.eld.motive.dto.User;
import com.bulkloads.web.infra.eld.motive.dto.Vehicle;
import com.bulkloads.web.infra.eld.motive.dto.VehicleLocation;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    uses = CommonMapper.class,
    imports = UserUtil.class)
public abstract class MotiveMapper {

  @Mapping(target = "eldProviderId", constant = "motive")
  @Mapping(source = "id", target = "externalId")
  @Mapping(target = "username", ignore = true)
  @Mapping(target = "notes", ignore = true)
  @Mapping(target = "phone1", expression = "java(user.phoneCountryCode() + user.phone())")
  @Mapping(source = "timeZone", target = "timezone")
  @Mapping(target = "syncUserId", expression = "java(UserUtil.getUserIdOrThrow())")
  @Mapping(target = "userCompanyId", expression = "java(UserUtil.getUserCompanyIdOrThrow())")
  @Mapping(target = "userId", ignore = true)
  @Mapping(target = "abUserId", ignore = true)
  public abstract EldDriverDto mapUser(final User user);

  public List<EldDriverDto> mapUsers(final List<User> users) {
    return users.stream().map(this::mapUser).toList();
  }

  @Mapping(target = "eldProviderId", constant = "motive")
  @Mapping(source = "id", target = "externalId")
  @Mapping(source = "year", target = "modelYear")
  @Mapping(target = "syncUserId", expression = "java(UserUtil.getUserIdOrThrow())")
  @Mapping(target = "userCompanyId", expression = "java(UserUtil.getUserCompanyIdOrThrow())")
  @Mapping(target = "userCompanyEquipmentId", ignore = true)
  public abstract EldVehicleDto mapVehicle(final Vehicle vehicle);

  public List<EldVehicleDto> mapVehicles(final List<Vehicle> vehicles) {
    return vehicles.stream().map(this::mapVehicle).toList();
  }

  @Mapping(target = "eldProviderId", constant = "motive")
  @Mapping(source = "id", target = "externalId")
  @Mapping(source = "currentLocation.bearing", target = "bearing")
  @Mapping(source = "currentLocation.lat", target = "latitude")
  @Mapping(source = "currentLocation.lon", target = "longitude")
  @Mapping(expression = "java(vehicleLocation.currentLocation() != null ? vehicleLocation.currentLocation().kph() / 1.609 : 0)", target = "mph")
  @Mapping(source = "currentLocation.locatedAt", target = "timestamp")
  @Mapping(source = "number", target = "name")
  public abstract EldVehicleLocationDto mapVehicleLocations(final VehicleLocation vehicleLocation);

  public List<EldVehicleLocationDto> mapVehicleLocations(final List<VehicleLocation> vehicleLocations) {
    return vehicleLocations.stream().map(this::mapVehicleLocations).toList();
  }

  @Mapping(target = "eldProviderId", constant = "motive")
  @Mapping(source = "id", target = "externalId")
  @Mapping(source = "availableTime._break", target = "breakTimeRemaining")
  @Mapping(source = "availableTime.cycle", target = "cycleTimeRemaining")
  @Mapping(source = "lastCycleReset.startTime", target = "cycleStartedTime")
  @Mapping(source = "recap.secondsTomorrow", target = "cycleTomorrowTimeRemaining")
  @Mapping(source = "availableTime.drive", target = "driveTimeRemaining")
  @Mapping(source = "availableTime.shift", target = "shiftTimeRemaining")
  @Mapping(source = "dutyStatus", target = "currentDutyStatus")
  public abstract EldDriverHosDto mapDriverAvailableTime(final DriverAvailableTime driverAvailableTime);

  public List<EldDriverHosDto> mapDriverAvailableTime(final List<DriverAvailableTime> driverAvailableTimeList) {
    return driverAvailableTimeList.stream().map(this::mapDriverAvailableTime).toList();
  }
}
