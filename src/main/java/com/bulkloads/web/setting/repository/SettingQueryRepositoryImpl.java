package com.bulkloads.web.setting.repository;

import static com.bulkloads.web.setting.repository.template.SettingQueryTemplate.SETTINGS_QUERY_TEMPLATE;
import java.util.HashMap;
import java.util.Map;
import com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService;
import com.bulkloads.web.setting.service.dto.SettingResponse;
import com.bulkloads.web.setting.service.dto.transformer.GetSettingResponseTransformer;
import org.springframework.stereotype.Repository;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Repository
public class SettingQueryRepositoryImpl implements SettingQueryRepository {

  private final JpaNativeQueryService jpaNativeQueryService;
  private final GetSettingResponseTransformer getSettingResponseTransformer;

  @Override
  public SettingResponse getSetting(Integer userId) {
    Map<String, Object> params = new HashMap<>();

    params.put("user_id", userId);

    return jpaNativeQueryService.queryForObject(SETTINGS_QUERY_TEMPLATE, params, getSettingResponseTransformer);
  }
}
