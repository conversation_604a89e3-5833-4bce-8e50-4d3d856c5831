package com.bulkloads.web.setting.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.setting.service.SettingService;
import com.bulkloads.web.setting.service.dto.NotificationSettingResponse;
import com.bulkloads.web.setting.service.dto.SettingResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;

@RestController
@Tag(name = "Setting")
@CrossOrigin(origins = "*", maxAge = 3600)
@RequiredArgsConstructor
@RequestMapping("/rest/settings")
public class SettingQueryController {

  private final SettingService settingService;

  @GetMapping(value = "")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public SettingResponse getSetting() {
    return settingService.getSettings();
  }

  @GetMapping(value = "/notifications")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public List<NotificationSettingResponse> getSettingNotification() {
    return settingService.getNotificationSettings();
  }

  @GetMapping(value = "/notifications/{emailCategoryId}")
  @PreAuthorize("hasRole('" + ROLE_USER + "')")
  public NotificationSettingResponse getSettingNotificationId(@PathVariable("emailCategoryId") Integer emailCategoryId) {
    return settingService.getNotificationSettingsId(emailCategoryId);
  }

}
