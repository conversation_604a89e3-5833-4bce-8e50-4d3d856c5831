package com.bulkloads.web.routing.repository;

import java.util.Optional;
import com.bulkloads.web.routing.domain.entity.Route;
import com.bulkloads.web.routing.domain.vo.Location;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.data.repository.query.QueryByExampleExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface RouteRepository extends JpaRepository<Route, Integer>, QueryByExampleExecutor<Route> {

  //TODO consider using QueryDSL!!
  @Query(value = """
      SELECT r.*
      FROM Routes r
      WHERE r.startAddress = :#{#origin.address}
      AND r.startCity = :#{#origin.city}
      AND r.startState = :#{#origin.state}
      AND r.startZip = :#{#origin.zip}
      AND r.startCountry = :#{#origin.country}
      AND r.endAddress = :#{#destination.address}
      AND r.endCity = :#{#destination.city}
      AND r.endState = :#{#destination.state}
      AND r.endZip = :#{#destination.zip}
      AND r.endCountry = :#{#destination.country}
      LIMIT 1
      """,
      nativeQuery = true)
  Optional<Route> findCached(@Param("origin") final Location origin,
                             @Param("destination") final Location destination);
}
