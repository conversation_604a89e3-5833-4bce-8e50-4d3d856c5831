package com.bulkloads.web.eld.repository;

import java.util.List;
import com.bulkloads.web.eld.domain.entity.EldUserCompanyEquipment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface EldUserCompanyEquipmentRepository extends JpaRepository<EldUserCompanyEquipment, Integer> {

  List<EldUserCompanyEquipment> findByEldProviderIdAndUserCompanyId(final String providerId, final int userCompanyId);

  List<EldUserCompanyEquipment> findByEldProviderIdAndUserCompanyIdAndUserCompanyEquipmentNotNull(final String providerId, final int userCompanyId);

  List<EldUserCompanyEquipment> findAllByUserCompanyEquipmentNotNullAndUserCompanyId(final int userCompanyId);

  @Modifying
  @Query("UPDATE EldUserCompanyEquipment e SET e.active = false WHERE e.eldProviderId = :eldProviderId AND e.userCompanyId = :userCompanyId")
  void deactivateByEldProviderIdAndUserCompanyId(@Param("eldProviderId") final String eldProviderId, @Param("userCompanyId") final int userCompanyId);
}
