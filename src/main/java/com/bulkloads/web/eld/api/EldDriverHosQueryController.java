package com.bulkloads.web.eld.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;
import java.util.List;
import com.bulkloads.web.eld.api.dto.EldDriverHosResponse;
import com.bulkloads.web.eld.api.openapi.EldDriverHosQueryApiDoc;
import com.bulkloads.web.eld.service.EldDriverHosService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/eld/hos")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class EldDriverHosQueryController implements EldDriverHosQueryApiDoc {

  private final EldDriverHosService service;

  @GetMapping
  public List<EldDriverHosResponse> findAll(@RequestParam(value = "user_ids", required = false) final List<Integer> userIds) {
    return service.findAll(userIds);
  }
}
