package com.bulkloads.web.eld.api.openapi;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

@Tag(name = "ELD UserInfo API")
public interface EldUserInfoApiDoc {

  @Operation(summary = "Link ELD User Info to User")
  void linkToUser(
      @Parameter(name = "eld_user_info_id", description = "The id of the ELD User Info", required = true)
      final int eldUserInfoId,
      @Parameter(name = "user_id", description = "The id of the User", required = true)
      final int userId);

  @Operation(summary = "Link ELD User Info to AbUser")
  void linkToAbUser(
      @Parameter(name = "eld_user_info_id", description = "The id of the ELD User Info", required = true)
      final int eldUserInfoId,
      @Parameter(name = "ab_user_id", description = "The id of the AbUser", required = true)
      final int abUserId);

  @Operation(summary = "Unlink ELD User Info from User")
  void unlink(
      @Parameter(name = "eld_user_info_id", description = "The id of the ELD User Info", required = true)
      final int eldUserInfoId);
}
