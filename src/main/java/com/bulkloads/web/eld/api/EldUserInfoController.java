package com.bulkloads.web.eld.api;

import static com.bulkloads.config.AppConstants.UserRole.ROLE_USER;

import com.bulkloads.web.eld.api.openapi.EldUserInfoApiDoc;
import com.bulkloads.web.eld.service.EldUserInfoService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/rest/eld/users")
@RequiredArgsConstructor
@PreAuthorize("hasRole('" + ROLE_USER + "')")
public class EldUserInfoController implements EldUserInfoApiDoc {

  private final EldUserInfoService service;

  @PutMapping("/{eld_user_info_id}/link/user/{user_id}")
  public void linkToUser(@PathVariable("eld_user_info_id") final int eldUserInfoId,
                         @PathVariable("user_id") final int userId) {
    service.linkToUser(eldUserInfoId, userId);
  }

  @PutMapping("/{eld_user_info_id}/link/ab-user/{ab_user_id}")
  public void linkToAbUser(@PathVariable("eld_user_info_id") final int eldUserInfoId,
                           @PathVariable("ab_user_id") final int abUserId) {
    service.linkToAbUser(eldUserInfoId, abUserId);
  }

  @DeleteMapping("/{eld_user_info_id}/unlink")
  public void unlink(@PathVariable("eld_user_info_id") final int eldUserInfoId) {
    service.unlink(eldUserInfoId);
  }
}
