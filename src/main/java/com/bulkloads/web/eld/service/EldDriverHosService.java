package com.bulkloads.web.eld.service;

import java.util.List;
import com.bulkloads.web.eld.api.dto.EldDriverHosResponse;
import com.bulkloads.web.eld.mapper.EldDriverHosMapper;
import com.bulkloads.web.infra.eld.EldProviderFacade;
import com.bulkloads.web.infra.eld.dto.EldDriverHosDto;
import com.bulkloads.web.setting.service.SettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class EldDriverHosService {

  private final EldDriverHosMapper eldDriverHosMapper;
  private final EldUserInfoService eldUserInfoService;
  private final EldProviderFacade eldProviderFacade;
  private final SettingService settingService;

  public List<EldDriverHosResponse> findAll(final List<Integer> userIds) {
    final String providerId = settingService.getEldProviderId();
    final List<String> externalIds = eldUserInfoService.findExternalIdByUserId(userIds);
    final List<EldDriverHosDto> eldDriverHosDtos = eldProviderFacade.fetchDriverHosStatuses(providerId, externalIds);
    return eldDriverHosMapper.map(eldDriverHosDtos);
  }
}
