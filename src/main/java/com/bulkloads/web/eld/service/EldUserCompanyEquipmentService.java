package com.bulkloads.web.eld.service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipment;
import com.bulkloads.web.companyequipment.domain.entity.UserCompanyEquipmentGeoHistory;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentGeoHistoryRepository;
import com.bulkloads.web.companyequipment.repository.UserCompanyEquipmentRepository;
import com.bulkloads.web.eld.api.dto.EldUserCompanyEquipmentResponse;
import com.bulkloads.web.eld.domain.entity.EldUserCompanyEquipment;
import com.bulkloads.web.eld.mapper.EldUserCompanyEquipmentMapper;
import com.bulkloads.web.eld.repository.EldUserCompanyEquipmentRepository;
import com.bulkloads.web.infra.eld.dto.EldVehicleDto;
import com.bulkloads.web.infra.eld.dto.EldVehicleLocationDto;
import com.bulkloads.web.setting.service.SettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import lombok.RequiredArgsConstructor;

@Service
@Transactional
@RequiredArgsConstructor
public class EldUserCompanyEquipmentService {

  private final EldUserCompanyEquipmentRepository eldUserCompanyEquipmentRepository;
  private final EldUserCompanyEquipmentMapper eldUserCompanyEquipmentMapper;
  private final UserCompanyEquipmentGeoHistoryRepository userCompanyEquipmentGeoHistoryRepository;
  private final UserCompanyEquipmentRepository userCompanyEquipmentRepository;
  private final SettingService settingService;

  public EldUserCompanyEquipmentResponse findById(final int eldUserCompanyEquipmentId) {
    return eldUserCompanyEquipmentMapper.map(eldUserCompanyEquipmentRepository.getReferenceById(eldUserCompanyEquipmentId));
  }

  public List<EldUserCompanyEquipmentResponse> findAllByProviderIdAndUserCompanyId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final String providerId = settingService.getEldProviderId();

    final List<EldUserCompanyEquipment> entities = eldUserCompanyEquipmentRepository.findByEldProviderIdAndUserCompanyId(providerId, userCompanyId);
    return eldUserCompanyEquipmentMapper.map(entities);
  }

  public List<String> findMatchedExternalIdsByProviderIdAndUserCompanyId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    final String providerId = settingService.getEldProviderId();

    return eldUserCompanyEquipmentRepository.findByEldProviderIdAndUserCompanyIdAndUserCompanyEquipmentNotNull(providerId, userCompanyId)
        .stream()
        .map(EldUserCompanyEquipment::getExternalId)
        .toList();
  }

  public void link(final int eldUserCompanyEquipmentId, final int userCompanyEquipmentId) {
    final UserCompanyEquipment userCompanyEquipment = userCompanyEquipmentRepository.getReferenceById(userCompanyEquipmentId);
    final EldUserCompanyEquipment eldUserCompanyEquipment = eldUserCompanyEquipmentRepository.getReferenceById(eldUserCompanyEquipmentId);

    if (!eldUserCompanyEquipment.getUserCompanyId().equals(userCompanyEquipment.getUserCompany().getUserCompanyId())) {
      throw new ValidationException("user_company_id", "The user company does not match the eld user company equipment company");
    }

    eldUserCompanyEquipment.setUserCompanyEquipment(userCompanyEquipment);
  }

  public void unlink(final int eldUserCompanyEquipmentId) {
    final EldUserCompanyEquipment entity = eldUserCompanyEquipmentRepository.getReferenceById(eldUserCompanyEquipmentId);
    entity.setUserCompanyEquipment(null);
  }

  public void syncUserCompanyEquipments(final String providerId, final List<EldVehicleDto> eldVehicleDtos) {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();

    final Map<String, EldUserCompanyEquipment> eldUserCompanyEquipmentByExternalId = collectEldVehicleByProviderIdAndCompanyId(providerId, userCompanyId);

    List<EldUserCompanyEquipment> newToBeSaved = new ArrayList<>();
    final Set<String> dtoExternalIds = new HashSet<>();

    eldVehicleDtos.forEach(dto -> {
      final String dtoExternalId = dto.externalId();
      final EldUserCompanyEquipment entity = eldUserCompanyEquipmentByExternalId.get(dtoExternalId);
      dtoExternalIds.add(dtoExternalId);
      if (entity != null) {
        eldUserCompanyEquipmentMapper.map(dto, entity);
      } else {
        final EldUserCompanyEquipment newEntity = eldUserCompanyEquipmentMapper.map(dto);
        newToBeSaved.add(newEntity);
      }
    });

    if (!eldUserCompanyEquipmentByExternalId.isEmpty() && !eldUserCompanyEquipmentByExternalId.keySet().containsAll(dtoExternalIds)) {
      eldUserCompanyEquipmentByExternalId.values().stream()
          .filter(e -> !dtoExternalIds.contains(e.getExternalId()))
          .forEach(e -> e.setActive(false));
    }

    eldUserCompanyEquipmentRepository.saveAll(newToBeSaved);
  }

  private Map<String, EldUserCompanyEquipment> collectEldVehicleByProviderIdAndCompanyId(final String providerId, final int userCompanyId) {
    return eldUserCompanyEquipmentRepository
        .findByEldProviderIdAndUserCompanyId(providerId, userCompanyId)
        .stream()
        .collect(Collectors.toMap(EldUserCompanyEquipment::getExternalId, e -> e));
  }

  public void syncUserCompanyEquipmentsLocations(final List<EldVehicleLocationDto> dtos) {
    final Map<String, UserCompanyEquipment> userCompanyEquipmentByExternalId = collectMatchedUserCompanyEquipmentByExternalId();

    final List<UserCompanyEquipmentGeoHistory> toBeSaved = dtos
        .stream()
        .map(dto -> {
          final String dtoExternalId = dto.externalId();
          final UserCompanyEquipment userCompanyEquipment = userCompanyEquipmentByExternalId.get(dtoExternalId);
          return eldUserCompanyEquipmentMapper.map(dto, userCompanyEquipment);
        }).toList();

    userCompanyEquipmentGeoHistoryRepository.saveAll(toBeSaved);
  }

  private Map<String, UserCompanyEquipment> collectMatchedUserCompanyEquipmentByExternalId() {
    final int userCompanyId = UserUtil.getUserCompanyIdOrThrow();
    return eldUserCompanyEquipmentRepository
        .findAllByUserCompanyEquipmentNotNullAndUserCompanyId(userCompanyId)
        .stream()
        .collect(Collectors.toMap(EldUserCompanyEquipment::getExternalId, EldUserCompanyEquipment::getUserCompanyEquipment));
  }
}
