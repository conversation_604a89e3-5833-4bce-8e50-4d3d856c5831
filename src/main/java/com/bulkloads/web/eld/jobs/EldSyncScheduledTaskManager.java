package com.bulkloads.web.eld.jobs;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;
import com.bulkloads.web.eld.service.EldSyncService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EldSyncScheduledTaskManager {

  private final EldSyncService eldSyncService;

  @Scheduled(cron = "0 */10 * * * *")
  @SchedulerLock(name = "syncVehicleLocations", lockAtMostFor = "7m", lockAtLeastFor = "1m")
  public void syncVehicleLocations() {
    long start = System.currentTimeMillis();
    eldSyncService.synchronizeUserCompanyEquipmentLocations();
    long duration = System.currentTimeMillis() - start;
    log.info("Executed syncVehicleLocations scheduled task. took: {} (s)", duration / 1000);
  }


  @Scheduled(cron = "0 */10 * * * *")
  @SchedulerLock(name = "syncDriverHos", lockAtMostFor = "7m", lockAtLeastFor = "1m")
  public void syncDriverHos() {
    long start = System.currentTimeMillis();
    eldSyncService.synchronizeDriverHos();
    long duration = System.currentTimeMillis() - start;
    log.info("Executed syncDriverHos scheduled task. took: {} (s)", duration / 1000);
  }

}
