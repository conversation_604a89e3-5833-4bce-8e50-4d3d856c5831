package com.bulkloads.web.integration.agtrax.domain.entity;

import java.time.LocalDate;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Entity
@Table(name = "grades")
@Getter
@Setter
public class Grade {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "grade_id", nullable = false)
  private Integer gradeId;

  @Size(max = 50)
  @Column(name = "grade_name", nullable = false, length = 50)
  private String gradeName;

  @Size(max = 4)
  @Column(name = "grade_code", nullable = false, length = 4)
  private String gradeCode;

  @Size(max = 500)
  @Column(name = "grade_description", nullable = false, length = 500)
  private String gradeDescription = "";

  @Column(name = "deleted", nullable = false)
  private Boolean deleted = false;

  @Column(name = "deleted_date")
  private LocalDate deletedDate;
}