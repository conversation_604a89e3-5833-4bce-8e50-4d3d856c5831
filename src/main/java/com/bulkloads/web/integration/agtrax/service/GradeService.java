package com.bulkloads.web.integration.agtrax.service;

import java.util.List;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.BulkloadsException;
import com.bulkloads.web.assignment.repository.AssignmentRepository;
import com.bulkloads.web.file.domain.entity.FileFieldDefinition;
import com.bulkloads.web.file.repository.FileFieldDefinitionRepository;
import com.bulkloads.web.file.repository.FileFieldRepository;
import com.bulkloads.web.integration.agtrax.domain.entity.ExternalGrade;
import com.bulkloads.web.integration.agtrax.domain.entity.Grade;
import com.bulkloads.web.integration.agtrax.dto.GradeResponse;
import com.bulkloads.web.integration.agtrax.repository.ExternalGradeRepository;
import com.bulkloads.web.integration.agtrax.repository.GradeRepository;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GradeService {

  private final GradeRepository gradeRepository;
  private final ExternalGradeRepository externalGradeRepository;
  private final FileFieldDefinitionRepository fileFieldDefinitionRepository;
  private final AssignmentRepository assignmentRepository;
  private final FileFieldRepository fileFieldRepository;

  public List<GradeResponse> getGrades() {
    return gradeRepository.findByDeletedFalse()
        .stream()
        .map(grade -> GradeResponse.builder()
            .gradeId(grade.getGradeId())
            .gradeName(grade.getGradeName())
            .gradeCode(grade.getGradeCode())
            .build())
        .collect(Collectors.toList());
  }

  @Transactional
  public void matchExternalGradeWithGrade(Integer gradeId, Integer externalGradeId) {

    Integer userId = UserUtil.getUserIdOrThrow();

    Grade grade = gradeRepository.findById(gradeId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Grade not found"));

    ExternalGrade externalGrade = externalGradeRepository.findById(externalGradeId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "External grade not found"));

    // Match the grade with the external grade
    externalGrade.setGradeId(grade.getGradeId());

    // Save the external grade
    externalGradeRepository.save(externalGrade);

    // Get the file field definition for this grade
    FileFieldDefinition fieldDefinition = fileFieldDefinitionRepository.findByGradeId(grade.getGradeId())
        .orElseThrow(() -> new BulkloadsException("Grade " + grade.getGradeName() + " has no file field definition"));

    // Update the external grades in the file_fields for active loads
    fileFieldRepository.updateExternalFileFields(
        List.of(userId),
        List.of(externalGrade.getUserCompanyId()),
        List.of(externalGrade.getExternalGradeCode()),
        List.of(fieldDefinition.getFieldName()),
        List.of(fieldDefinition.getFieldLabel()),
        List.of(grade.getGradeId())
    );

    // Update assignment.hasUnmatchedExternalGrades for all affected assignments
    assignmentRepository.findAssignmentsNoLongerWithUnmatchedExternalGrades(externalGrade.getUserCompanyId())
        .forEach(assignment -> {
          assignment.setHasUnmatchedExternalGrades(false);
          assignmentRepository.save(assignment);
        });

  }

}
