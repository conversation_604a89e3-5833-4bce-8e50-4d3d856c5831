package com.bulkloads.web.integration.agtrax.domain.entity;

import java.time.Instant;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import lombok.Getter;
import lombok.Setter;

@Entity
@Getter
@Setter
@Table(name = "agtrax_scale_tickets")
public class AgTraxScaleTicket {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "agtrax_scale_ticket_id")
  private Integer agtraxScaleTicketId;

  @Column(name = "user_company_id")
  private Integer userCompanyId;

  @Column(name = "serial_id")
  private Integer serialId;

  @Column(name = "date")
  private Instant date;

  @Column(name = "ticket_number", length = 50)
  private String ticketNumber;

  @Column(name = "branch_id")
  private Integer branchId;

  @Column(name = "commodity_id")
  private Integer commodityId;

  @Column(name = "external_commodity_id", length = 50)
  private String externalCommodityId;

  @Column(name = "gross_pounds", length = 50)
  private String grossPounds;

  @Column(name = "tare_pounds", length = 50)
  private String tarePounds;

  @Column(name = "reference1", length = 255)
  private String reference1;

  @Column(name = "reference2", length = 255)
  private String reference2;

  @Column(name = "discount_schedule_id")
  private Integer discountScheduleId;

  @Column(name = "destination_id")
  private Integer destinationId;

  @Column(name = "hauler_id")
  private Integer haulerId;

  @Column(name = "origin_payload", columnDefinition = "json")
  private String originPayload;

  @Column(name = "destination_payload", columnDefinition = "text")
  private String destinationPayload;

  @Column(name = "post_response", columnDefinition = "text")
  private String postResponse;

  @Column(name = "destination_file_id")
  private Integer destinationFileId;

  @Column(name = "load_assignment_id")
  private Integer loadAssignmentId;

  @Column(name = "created_date")
  private Instant createdDate;

  @Column(name = "modified_date")
  private Instant modifiedDate;

  @Column(name = "deleted_date")
  private Instant deletedDate;

  @Column(name = "deleted")
  private Boolean deleted;

  @Transient
  private boolean hasUnmatchedExternalGrades;

  public boolean getHasUnmatchedExternalGrades() {
    return hasUnmatchedExternalGrades;
  }

  public void setHasUnmatchedExternalGrades(boolean hasUnmatchedExternalGrades) {
    this.hasUnmatchedExternalGrades = hasUnmatchedExternalGrades;
  }
}
