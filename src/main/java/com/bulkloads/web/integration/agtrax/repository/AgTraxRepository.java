package com.bulkloads.web.integration.agtrax.repository;

import java.util.List;
import java.util.Optional;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicket;
import com.bulkloads.web.integration.agtrax.domain.entity.AgTraxScaleTicketFactor;
import com.bulkloads.web.integration.agtrax.dto.CandidateAssignmentResponse;
import com.bulkloads.web.integration.agtrax.dto.UnmatchedOriginTicketResponse;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AgTraxRepository extends JpaRepository<AgTraxScaleTicket, Integer> {

  /**
   * Find a scale ticket by ID that is not deleted
   *
   * @param agtraxScaleTicketId The scale ticket ID
   * @return Optional containing the scale ticket if found
   */
  @Query("SELECT st FROM AgTraxScaleTicket st WHERE st.agtraxScaleTicketId = :agtraxScaleTicketId AND st.deleted = false")
  Optional<AgTraxScaleTicket> findByAgtraxScaleTicketIdAndDeletedFalse(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);

  /**
   * Find a scale ticket by ticket number and user company ID
   *
   * @param loadingTicketNumber The ticket number
   * @param userCompanyId The user company ID
   * @return Optional containing the scale ticket if found
   */
  @Query(value = """
      SELECT st FROM AgTraxScaleTicket st
      WHERE st.ticketNumber = :loadingTicketNumber AND st.userCompanyId = :userCompanyId AND st.deleted = false AND st.loadAssignmentId IS NULL""")
  Optional<AgTraxScaleTicket> findByTicketNumberAndUserCompanyIdAndDeletedFalseAndLoadAssignmentIdIsNull(
      @Param("loadingTicketNumber") String loadingTicketNumber,
      @Param("userCompanyId") Integer userCompanyId);

  /**
   * Find a scale ticket by user company ID and load assignment ID
   *
   * @param userCompanyId   The user company ID
   * @param loadAssignmentId The load assignment ID
   * @return Optional containing the scale ticket if found
   */
  Optional<AgTraxScaleTicket> findByUserCompanyIdAndDeletedFalseAndLoadAssignmentId(
      Integer userCompanyId, Integer loadAssignmentId);

  /**
   * Get unmatched origin tickets for admin view
   *
   * @return List of unmatched origin ticket responses
   */
  @Query(nativeQuery = true, value = """
      select
          uc.user_company_id,
          uc.company_name,
          st.agtrax_scale_ticket_id,
          st.serial_id,
          st.ticket_number,
          st.reference1,
          st.reference2,
          st.discount_schedule_id,
          st.date
      from
          agtrax_scale_tickets st
              inner join user_company uc on st.user_company_id = uc.user_company_id
      where
          st.deleted = 0
          and load_assignment_id is null
      order by
          st.agtrax_scale_ticket_id
      limit 200
      """)
  List<UnmatchedOriginTicketResponse> getUnmatchedOriginTicketsAdmin();

  /**
   * Get candidate assignments for a scale ticket
   */
  @Query(nativeQuery = true, value = """
      select
          la.load_assignment_id,
          la.load_id,
          la.loading_ticket_number,
          pick_c.company_name as pickup_company_name,
          drop_c.company_name as drop_company_name,
          to_abc.company_name as carrier_company_name
      from load_assignments la
      inner join loads l using(load_id)
      inner join ab_companies pick_c on l.pickup_ab_company_id = pick_c.ab_company_id
      inner join ab_companies drop_c on l.drop_ab_company_id = drop_c.ab_company_id
      inner join ab_companies to_abc on la.to_ab_company_id = to_abc.ab_company_id
      where la.user_company_id = :userCompanyId
      and la.unloading_ticket_file_id is null
      and la.deleted = 0
      and pick_c.external_ab_company_id = :branchId
      and drop_c.external_ab_company_id = :destinationId
      and to_abc.external_ab_company_id = :haulerId
      """)
  List<CandidateAssignmentResponse> findCandidateAssignmentsByCompanies(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("branchId") Integer branchId,
      @Param("destinationId") Integer destinationId,
      @Param("haulerId") Integer haulerId);

  /**
   * Get candidate assignments by ticket number
   *
   * @param userCompanyId The user company ID
   * @param ticketNumber  The ticket number
   * @return List of candidate assignment responses
   */
  @Query(nativeQuery = true, value = """
      select
          load_assignment_id,
          load_id,
          loading_ticket_number
      from load_assignments
      where user_company_id = :userCompanyId
      and loading_ticket_file_id is null
      and deleted = 0
      and loading_ticket_number = :ticketNumber
      limit 1
      """)
  List<CandidateAssignmentResponse> findCandidateAssignmentsByTicketNumber(
      @Param("userCompanyId") Integer userCompanyId,
      @Param("ticketNumber") String ticketNumber);

  /**
   * Gets factors for a scale ticket
   *
   * @param agtraxScaleTicketId The scale ticket ID
   * @return List of scale ticket factors
   */
  @Query("SELECT f FROM AgTraxScaleTicketFactor f WHERE f.agtraxScaleTicketId = :agtraxScaleTicketId")
  List<AgTraxScaleTicketFactor> findFactorsByAgtraxScaleTicketId(@Param("agtraxScaleTicketId") Integer agtraxScaleTicketId);
}
