package com.bulkloads.web.integration.agtrax.handler;

import com.bulkloads.web.assignment.event.AssignmentOcrDataApprovedEvent;
import com.bulkloads.web.integration.agtrax.AgTraxInternalService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.event.TransactionPhase;
import org.springframework.transaction.event.TransactionalEventListener;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgtraxOcrEventHandler {

  private final AgTraxInternalService agTraxInternalService;

  @TransactionalEventListener(
      classes = AssignmentOcrDataApprovedEvent.class,
      phase = TransactionPhase.AFTER_COMMIT
  )
  @Transactional(propagation = Propagation.REQUIRES_NEW)
  public void handleOcrDataApproved(final AssignmentOcrDataApprovedEvent event) {
    log.info("OCR data approved Event handler called for assignments: {}", event.getLoadAssignmentIds());
    // wait for 3 seconds so that CF can see the newly committed transaction
    try {
      Thread.sleep(3000);
    } catch (InterruptedException e) {
      throw new RuntimeException(e);
    }

    agTraxInternalService.repostScaleTicketsByLoadAssignmentIdsAsync(event.getLoadAssignmentIds());
  }
}