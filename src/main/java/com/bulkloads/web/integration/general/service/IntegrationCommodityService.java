package com.bulkloads.web.integration.general.service;

import static com.bulkloads.common.validation.ValidationUtils.existsAndIsNotEmpty;
import static com.bulkloads.common.validation.ValidationUtils.isMissingOrIsEmpty;
import java.util.Optional;
import com.bulkloads.common.UserUtil;
import com.bulkloads.exception.ValidationException;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.commodity.repository.CommodityRepository;
import com.bulkloads.web.commodity.service.CommodityService;
import com.bulkloads.web.commodity.service.dto.CommodityRequest;
import com.bulkloads.web.commodity.service.dto.CommodityResponse;
import com.bulkloads.web.integration.general.service.dto.IntegrationCommodityRequest;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class IntegrationCommodityService {

  private final CommodityService commodityService;
  private final CommodityRepository commodityRepository;

  public CommodityResponse createUpdateCommodity(IntegrationCommodityRequest request) {
    // Case 1: If commodityId is provided, look up directly
    if (existsAndIsNotEmpty(request.getCommodityId())) {
      final CommodityResponse commodityResponse = commodityService.getCommodity(request.getCommodityId().get());
      if (commodityResponse == null) {
        throw new ValidationException("commodity_id", "commodity_id: " + request.getCommodityId().get() + " not found");
      }
      return commodityResponse;
    }

    CommodityRequest commodityReq = CommodityRequest.builder()
        .externalCommodityId(request.getExternalCommodityId())
        .commodity(request.getCommodity())
        .commodityAbbr(request.getCommodityAbbr())
        .build();

    // Case 2 & 3: Handle external ID cases
    if (existsAndIsNotEmpty(request.getExternalCommodityId())) {
      Integer userCompanyId = UserUtil.getUserCompanyIdOrThrow();
      Optional<Commodity> existing = commodityRepository
          .findByUserCompanyUserCompanyIdAndExternalCommodityId(
              userCompanyId,
              request.getExternalCommodityId().get()
          );

      // Case 2: If only external ID is provided and commodity exists, return it
      if (existing.isPresent() && isOnlyExternalIdProvided(request)) {
        return commodityService.getCommodity(existing.get().getCommodityId());
      }

      // Case 3: If external ID exists and other fields provided, update the commodity
      if (existing.isPresent()) {
        return commodityService.update(existing.get().getCommodityId(), commodityReq);
      } else {
        return commodityService.createOrGetExisting(commodityReq);
      }
    }

    // Case 4: If only commodity info is provided (no IDs), create new
    if (existsAndIsNotEmpty(request.getCommodity())) {
      return commodityService.createOrGetExisting(commodityReq);
    }

    throw new ValidationException("commodity_id", "Either commodity_id, external_commodity_id, or commodity must be provided");
  }

  private boolean isOnlyExternalIdProvided(IntegrationCommodityRequest request) {
    return isMissingOrIsEmpty(request.getCommodity())
        && isMissingOrIsEmpty(request.getCommodityAbbr())
        && isMissingOrIsEmpty(request.getCommodityId());
  }

}
