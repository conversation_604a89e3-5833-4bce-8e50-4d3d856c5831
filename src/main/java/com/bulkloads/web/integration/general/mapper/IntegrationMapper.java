package com.bulkloads.web.integration.general.mapper;

import java.util.Optional;
import com.bulkloads.web.addressbook.abcompany.domain.entity.AbCompany;
import com.bulkloads.web.addressbook.abcompany.service.dto.AbCompanyRequest;
import com.bulkloads.web.commodity.domain.entity.Commodity;
import com.bulkloads.web.contracts.domain.entity.Contract;
import com.bulkloads.web.contracts.mapper.CommonMapper;
import com.bulkloads.web.contracts.service.dto.ContractRequest;
import com.bulkloads.web.integration.general.service.dto.IntegrationAbCompanyRequest;
import com.bulkloads.web.integration.general.service.dto.IntegrationAbCompanyResponse;
import com.bulkloads.web.integration.general.service.dto.IntegrationCommodityResponse;
import com.bulkloads.web.integration.general.service.dto.IntegrationContractRequest;
import com.bulkloads.web.integration.general.service.dto.IntegrationContractResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
    unmappedTargetPolicy = org.mapstruct.ReportingPolicy.IGNORE,
    uses = CommonMapper.class)
public interface IntegrationMapper {

  // contracts
  @Mapping(source = "request.shipFromDate", target = "shipFrom")
  @Mapping(source = "request.shipToDate", target = "shipTo")
  @Mapping(source = "pickupAbCompanyId", target = "pickupAbCompanyId")
  @Mapping(source = "dropAbCompanyId", target = "dropAbCompanyId")
  @Mapping(source = "commodityId", target = "commodityId")
  @Mapping(source = "request.commodity", target = "commodity", ignore = true)
  ContractRequest toContractRequest(IntegrationContractRequest request,
                                    Optional<Integer> pickupAbCompanyId,
                                    Optional<Integer> dropAbCompanyId,
                                    Optional<Integer> commodityId);

  @Mapping(target = "shipFromDate", source = "shipFrom")
  @Mapping(target = "shipToDate", source = "shipTo")
  @Mapping(target = "rateType", expression = "java(contract.getRateType() != null ? contract.getRateType().getRateType() : null)")
  @Mapping(target = "freightRateType", expression = "java(contract.getFreightRateType() != null ? contract.getFreightRateType().getRateType() : null)")
  @Mapping(target = "commodity", source = "commodity")
  @Mapping(target = "origin", source = "pickupAbCompany")
  @Mapping(target = "destination", source = "dropAbCompany")
  IntegrationContractResponse toIntegrationContractResponse(Contract contract);

  // commodities
  private IntegrationCommodityResponse toIntegrationCommodityResponse(Commodity commodity) {
    if (commodity == null) {
      return null;
    }
    return IntegrationCommodityResponse.builder()
        .externalCommodityId(commodity.getExternalCommodityId())
        .commodity(commodity.getCommodity())
        .commodityAbbr(commodity.getCommodityAbbr())
        .build();
  }

  // ab_companies
  AbCompanyRequest toAbCompanyRequest(IntegrationAbCompanyRequest request);


  private IntegrationAbCompanyResponse toIntegrationAbCompanyResponse(Contract contract, AbCompany company) {
    if (company == null) {
      return null;
    }
    return IntegrationAbCompanyResponse.builder()
        .externalAbCompanyId(company.getExternalAbCompanyId())
        .companyName(contract.getPickupCompanyName())
        .address(contract.getPickupAddress())
        .location(contract.getPickupLocation())
        .city(contract.getPickupCity())
        .state(contract.getPickupState())
        .zip(contract.getPickupZip())
        .country(contract.getPickupCountry())
        .latitude(contract.getPickupLat())
        .longitude(contract.getPickupLong())
        .apptRequired(company.getApptRequired())
        .receivingHours(company.getReceivingHours())
        .directions(company.getDirections())
        .companyNotes(company.getCompanyNotes())
        .build();
  }

}