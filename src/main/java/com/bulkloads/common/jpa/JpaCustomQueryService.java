package com.bulkloads.common.jpa;

import static com.bulkloads.common.StringUtil.trimNewLines;
import static com.bulkloads.common.jpa.nativejpa.JpaNativeQueryService.BASIC_GROOVY_QUERY_FUNCTIONS;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.bulkloads.exception.BulkloadsException;
import org.springframework.stereotype.Component;
import groovy.text.SimpleTemplateEngine;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.TypedQuery;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class JpaCustomQueryService {

  final EntityManager entityManager;

  public <T> List<T> query(String sqlQuery, Map<String, Object> queryParams, Class<T> resultClass) {
    try {
      return queryBuilder(sqlQuery, queryParams, resultClass).getResultList();
    } catch (NoResultException e) {
      return new ArrayList<>();
    }
  }

  public <T> T queryForObject(String sqlQuery, Map<String, Object> queryParams, Class<T> resultClass) {
    try {
      return queryBuilder(sqlQuery, queryParams, resultClass).getSingleResult();
    } catch (NoResultException e) {
      return null;
    }
  }

  @SneakyThrows
  private <T> TypedQuery<T> queryBuilder(String sqlQueryTemplate, Map<String, Object> queryParams, Class<T> resultClass) {
    QueryStringAndParams queryStringAndParams = buildQueryFromSqlTemplate(sqlQueryTemplate, queryParams);
    TypedQuery<T> jpaQuery = entityManager.createQuery(queryStringAndParams.getQuery(), resultClass);
    queryStringAndParams.getResolvedQueryParams().forEach(jpaQuery::setParameter);
    return jpaQuery;
  }

  @Data
  @AllArgsConstructor
  public static class QueryStringAndParams {

    private String query;
    private Map<String, Object> resolvedQueryParams;
  }

  @SneakyThrows
  public static QueryStringAndParams buildQueryFromSqlTemplate(String sqlQueryTemplate, Map<String, Object> queryParams) {
    SimpleTemplateEngine engine = new SimpleTemplateEngine();

    Map<String, Object> binding = new HashMap<>(queryParams);

    // make available in the groovy context the parameters
    // the user provides with the queryParams argument
    binding.putAll(queryParams);

    // We do a putIfAbsent here because in some queries we use automated
    // pre-creation of params from MUI query filters.
    binding.putIfAbsent("params", new HashMap<>());

    @SuppressWarnings("unchecked")
    Map<String, Object> resolvedQueryParams = (Map<String, Object>) binding.get("params");

    try {
      String sql = engine.createTemplate(BASIC_GROOVY_QUERY_FUNCTIONS + sqlQueryTemplate)
          .make(binding).toString();

      if (log.isTraceEnabled()) {
        log.trace("""
            queryParams: %s
            binding: %s
            resolvedQueryParams: %s
            %s
            """.formatted(
            queryParams,
            binding,
            resolvedQueryParams,
            trimNewLines(sql)
        ));
      }
      return new QueryStringAndParams(sql, resolvedQueryParams);

    } catch (Exception e) {
      log.error("""
          queryParams: %s
          binding: %s
          resolvedQueryParams: %s
          %s
          """.formatted(
          queryParams,
          binding,
          resolvedQueryParams,
          BASIC_GROOVY_QUERY_FUNCTIONS + sqlQueryTemplate
      ));
      throw new BulkloadsException(e);
    }
  }

}
