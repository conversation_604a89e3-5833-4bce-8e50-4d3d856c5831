package com.bulkloads.common.mui.model;

import static com.bulkloads.common.Converters.asList;
import static com.bulkloads.common.mui.model.FilterItem.BOOLEAN_OPERATORS;
import static com.bulkloads.common.mui.model.FilterItem.DATE_OPERATORS;
import static com.bulkloads.common.mui.model.FilterItem.NUMERIC_OPERATORS;
import static com.bulkloads.common.mui.model.FilterItem.STRING_OPERATORS;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import com.bulkloads.exception.BulkloadsException;
import com.qs.core.model.QSArray;
import com.qs.core.model.QSObject;
import lombok.Getter;
import lombok.extern.log4j.Log4j2;

@Log4j2
public class FilterModel {

  @Getter
  private List<FilterItem> items;

  @Getter
  private String logicOperator;

  /**
   * Text search across all columns.
   */

  @Getter
  private List<String> quickFilterValues;

  /**
   * We assume the quickFilter operator is `and` for current implementation
   */

  @Getter
  private String quickFilterLogicOperator = "and";

  // Not used in the current implementation
  private Boolean quickFilterExcludeHiddenColumns;

  public FilterModel(List<FilterItem> items, String logicOperator, List<String> quickFilterValues) {
    this.items = items;
    this.logicOperator = logicOperator;
    this.quickFilterValues = quickFilterValues;
  }

  public static FilterModel parse(QSObject qs, Map<String, FieldInfo> fields) {
    List<FilterItem> filterItems = new ArrayList<>();

    QSObject filter = (QSObject) qs.get("filter");

    if (filter == null) {
      return new FilterModel(filterItems, "and", new ArrayList<>());
    }
    QSArray items = (QSArray) filter.getOrDefault("items", new QSArray());

    items.stream()
        .map(o -> (QSObject) o)
        .map(filterItem -> buildFilterItemFromQsObject(filterItem, fields))
        .filter(Objects::nonNull)
        .peek(item -> {
          log.debug("filterItem: field=[{}] operator=[{}] class=[{}] values={}",
                    item.getField(), item.getOperator(), item.getClasz().getSimpleName(), item.getValues());
        })
        .forEach(filterItems::add);

    String logicOperator = (String) filter.get("logicOperator");

    // normalize the logicOperator, only `and` and `or` are allowed
    // on any other input fallback to `and`
    if (logicOperator == null || !(logicOperator.equals("or") || logicOperator.equals("and"))) {
      logicOperator = "and";
    }

    QSArray quickFilterValuesArray = (QSArray) filter.getOrDefault("quickFilterValues", new QSArray());

    List<String> quickFilterValues = quickFilterValuesArray
        .stream()
        .map(v -> (String) v)
        .filter(Objects::nonNull)
        .collect(asList());

    // CAUTION: Quickfilters are not passed as separate array items, but in the first elemnt
    // of the array thus we need to split by space

    if (!quickFilterValues.isEmpty()) {
      quickFilterValues = Arrays.asList(quickFilterValues.get(0).split(" "));
    }

    return new FilterModel(filterItems, logicOperator, quickFilterValues);
  }

  static String parseFilterOperator(Object operatorObject) {
    if (operatorObject == null) {
      return "equals";
    } else {
      if (operatorObject instanceof String operator) {

        Set<String> allOperators = new HashSet<>(STRING_OPERATORS);

        allOperators.addAll(NUMERIC_OPERATORS);
        allOperators.addAll(BOOLEAN_OPERATORS);
        allOperators.addAll(DATE_OPERATORS);

        if (allOperators.contains(operator)) {
          return operator;
        } else {
          return "equals";
        }

      } else {
        return "equals";
      }
    }
  }

  public static FilterItem buildFilterItemFromQsObject(QSObject item, Map<String, FieldInfo> fields) {

    FilterItem filterItem = new FilterItem();

    if (item.get("field") == null || !(item.get("field") instanceof String)) {
      return null;
    }
    if (item.get("operator") == null) {
      return null;
    }

    filterItem.setField((String) item.get("field"));

    final FieldInfo fieldInfo = fields.get(filterItem.getField());
    if (fieldInfo == null) {
      throw new BulkloadsException(("Uknown field " + filterItem.getField()));
    }

    filterItem.setClasz(fieldInfo.getType());
    filterItem.setOperator(parseFilterOperator(item.get("operator")));
    filterItem.parseValues(item.get("value"));

    return filterItem;
  }

}
