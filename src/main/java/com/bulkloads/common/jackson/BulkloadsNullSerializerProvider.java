package com.bulkloads.common.jackson;

import java.io.IOException;
import java.time.temporal.Temporal;
import java.util.List;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.BeanProperty;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.DefaultSerializerProvider;
import com.fasterxml.jackson.databind.ser.SerializerFactory;
import org.apache.logging.log4j.util.Strings;
import lombok.NoArgsConstructor;

public class BulkloadsNullSerializerProvider extends DefaultSerializerProvider {

  private final List<Class<?>> supportedTypes = List.of(String.class, Temporal.class);

  public BulkloadsNullSerializerProvider() {
    super();
  }

  public BulkloadsNullSerializerProvider(BulkloadsNullSerializerProvider provider,
                                         SerializationConfig config,
                                         SerializerFactory jsf) {
    super(provider, config, jsf);
  }

  @Override
  public BulkloadsNullSerializerProvider createInstance(SerializationConfig config,
                                                        SerializerFactory jsf) {
    return new BulkloadsNullSerializerProvider(this, config, jsf);
  }

  @Override
  public JsonSerializer<Object> findNullValueSerializer(BeanProperty property) throws JsonMappingException {
    final Class<?> rawClass = property.getType().getRawClass();
    if (supportedTypes.stream().anyMatch(aClass -> aClass.isAssignableFrom(rawClass))) {
      return EmptyStringSerializer.INSTANCE;
    } else {
      return super.findNullValueSerializer(property);
    }
  }

  @NoArgsConstructor(access = lombok.AccessLevel.PRIVATE)
  public static class EmptyStringSerializer extends JsonSerializer<Object> {

    public static final JsonSerializer<Object> INSTANCE = new EmptyStringSerializer();

    @Override
    public void serialize(Object o, JsonGenerator jsonGenerator, SerializerProvider serializerProvider)
        throws IOException {
      jsonGenerator.writeString(Strings.EMPTY);
    }
  }
}
