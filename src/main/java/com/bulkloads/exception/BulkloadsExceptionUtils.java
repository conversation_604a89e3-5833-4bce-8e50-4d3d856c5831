package com.bulkloads.exception;

import java.util.Arrays;
import java.util.stream.Collectors;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public final class BulkloadsExceptionUtils {

  private static final String CAUSED_BY = "Caused by:";
  private static final String SPLIT_REGEX = "\r?\n\t|\r?\n";

  public static String getCausedByChainMessage(final String stackTrace) {
    final String[] stackTraceLines = stackTrace.split(SPLIT_REGEX);
    return stackTraceLines[0] + System.lineSeparator() + "->" + System.lineSeparator() +  Arrays.stream(stackTraceLines)
        .filter(s -> s.contains(CAUSED_BY))
        .map(s -> s.substring(s.indexOf(CAUSED_BY)))
        .collect(Collectors.joining(System.lineSeparator() + "->" + System.lineSeparator()));
  }
}
