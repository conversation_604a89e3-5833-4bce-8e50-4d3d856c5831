package com.bulkloads.config;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.amqp.core.Declarable;
import org.springframework.amqp.core.Declarables;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.amqp.rabbit.annotation.EnableRabbit;
import org.springframework.amqp.rabbit.annotation.RabbitListenerConfigurer;
import org.springframework.amqp.rabbit.config.RetryInterceptorBuilder;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.RabbitListenerEndpointRegistrar;
import org.springframework.amqp.rabbit.retry.RejectAndDontRequeueRecoverer;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.handler.annotation.support.DefaultMessageHandlerMethodFactory;
import org.springframework.validation.Validator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@EnableRabbit
@Configuration
@RequiredArgsConstructor
@Slf4j
public class RabbitMqConfig implements RabbitListenerConfigurer {

  public static final String DLQ = ".dlq";
  private final AppProperties appProperties;
  private final Validator validator;

  @Bean
  public Declarables rabbitQueues() {
    final List<String> queueNames = getQueueNames();
    final Declarables declarables = new Declarables();
    final Collection<Declarable> declarablesCollection = declarables.getDeclarables();
    for (String queueName : queueNames) {
      declarablesCollection.add(buildQueue(queueName));
      declarablesCollection.add(builderDeadLetterQueue(queueName));
    }
    return declarables;
  }

  @Bean
  public Jackson2JsonMessageConverter jackson2JsonMessageConverter(final ObjectMapper objectMapper) {
    return new Jackson2JsonMessageConverter(objectMapper);
  }

  @Bean
  public DefaultMessageHandlerMethodFactory validatingHandlerMethodFactory() {
    final DefaultMessageHandlerMethodFactory factory = new DefaultMessageHandlerMethodFactory();
    factory.setValidator(validator);
    return factory;
  }

  @Bean
  public SimpleRabbitListenerContainerFactory quickBooksQueueListenerContainerFactory(final ConnectionFactory connectionFactory,
                                                                                      final Jackson2JsonMessageConverter messageConverter) {
    final SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
    factory.setConnectionFactory(connectionFactory);
    factory.setMessageConverter(messageConverter);
    factory.setDefaultRequeueRejected(false);

    factory.setAdviceChain(
        RetryInterceptorBuilder.stateless()
            .maxAttempts(5)
            .backOffOptions(5000, 3.0, 60000)
            .recoverer(new RejectAndDontRequeueRecoverer())
            .build()
    );

    return factory;
  }

  @Override
  public void configureRabbitListeners(final RabbitListenerEndpointRegistrar registrar) {
    registrar.setMessageHandlerMethodFactory(validatingHandlerMethodFactory());
  }

  private List<String> getQueueNames() {
    return Arrays.asList(
        appProperties.getNotification().getQueueName(),
        appProperties.getMailing().getEmailQueueName(),
        appProperties.getPubNub().getQueueName(),
        appProperties.getConfirmation().getQueueName(),
        appProperties.getOffer().getQueueName(),
        appProperties.getSplitPdf().getQueueName(),
        appProperties.getInvoice().getQueueName(),
        appProperties.getSms().getQueueName(),
        appProperties.getQuickBooks().getQueueName()
    );
  }

  private Queue buildQueue(String queueName) {
    //The empty string ("") indicates that the dead-lettered messages will be routed to the default exchange.
    // The default exchange is a direct exchange with no name (an empty string).
    return QueueBuilder
        .durable(queueName)
        .deadLetterExchange("")
        .deadLetterRoutingKey(queueName + DLQ)
        .build();
  }

  private Queue builderDeadLetterQueue(String queueName) {
    return QueueBuilder
        .durable(queueName + DLQ)
        .build();
  }
}
