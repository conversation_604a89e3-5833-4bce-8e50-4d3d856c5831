package com.bulkloads.config;

import javax.annotation.PostConstruct;
import org.springframework.boot.info.GitProperties;
import org.springframework.stereotype.Component;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@AllArgsConstructor
public class GitCommitHashReporter {

  private final GitProperties gitProperties;

  @PostConstruct
  public void init() {
    log.info("Git commit: {}", gitProperties.getCommitId());
  }
}
