package com.bulkloads.security;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintWriter;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpServletResponseWrapper;
import lombok.Getter;

@Getter
public class CachedBodyHttpServletResponse extends HttpServletResponseWrapper {

  private final ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
  private final PrintWriter writer = new PrintWriter(byteArrayOutputStream);
  private final ServletOutputStream outputStream;

  public CachedBodyHttpServletResponse(final HttpServletResponse response) {
    super(response);
    this.outputStream = new CachedBodyServletOutputStream(byteArrayOutputStream);
  }

  public byte[] getResponseData() {
    return byteArrayOutputStream.toByteArray();
  }

  @Override
  public void flushBuffer() throws IOException {
    super.flushBuffer();
    writer.flush();
    outputStream.flush();
  }
}