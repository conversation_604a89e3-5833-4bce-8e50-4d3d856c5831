package com.bulkloads.security.oauth;

import java.io.IOException;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.WebUtils;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

public class QuickBooksRealmIdCaptureFilter extends OncePerRequestFilter {

  public static final String REALM_ID_PARAMETER = "realmId";
  public static final String QUICKBOOKS_AUTH_REDIRECT_URI = "/login/oauth2/code/quickbooks";

  @Override
  protected void doFilterInternal(final HttpServletRequest request,
                                  @NotNull final HttpServletResponse response,
                                  @NotNull final FilterChain filterChain) throws ServletException, IOException {
    if (request.getRequestURI().endsWith(QUICKBOOKS_AUTH_REDIRECT_URI)) {
      String realmId = request.getParameter(REALM_ID_PARAMETER);
      if (realmId != null && !realmId.isEmpty()) {
        WebUtils.setSessionAttribute(request, REALM_ID_PARAMETER, realmId);
      }
    }

    filterChain.doFilter(request, response);
  }
}
