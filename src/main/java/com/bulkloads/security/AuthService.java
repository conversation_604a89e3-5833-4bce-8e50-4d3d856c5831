package com.bulkloads.security;

import static com.bulkloads.config.AppConstants.Claims.AID;
import static com.bulkloads.config.AppConstants.Claims.LA_IDS;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

  private final AuthenticationManager authenticationManager;
  private final BulkLoadsJwtTokenService bulkloadsJwtTokenService;

  public String getUserAuthToken(final Integer userId,
                                 final Integer abUserId,
                                 final List<Integer> loadAssignmentIds,
                                 final int expiresInDays) {

    final Map<String, String> claims = new HashMap<>();
    final String abUserIdString = Optional.ofNullable(abUserId).map(String::valueOf).orElse("");
    claims.put(AID, abUserIdString);
    if (loadAssignmentIds.isEmpty()) {
      claims.put(LA_IDS, "");
    } else {
      claims.put(LA_IDS, String.join(",", loadAssignmentIds.stream().map(String::valueOf).collect(Collectors.joining(","))));
    }

    final long expirationInMills = expiresInDays * 24 * 60 * 60 * 1000L;
    final String userIdString = Optional.ofNullable(userId).map(String::valueOf).orElse("");

    return bulkloadsJwtTokenService.generateAccessToken(userIdString, claims, expirationInMills);
  }

  public String authenticate(final String username, final String password) {
    final UsernamePasswordAuthenticationToken authenticationToken =
        new UsernamePasswordAuthenticationToken(
            username, password);
    final Authentication authentication = authenticationManager.authenticate(authenticationToken);

    //TODO checkLoginExtras

    final Actor actor = (Actor) authentication.getPrincipal();
    final String userId = String.valueOf(actor.getUserId().orElse(-1));
    final Map<String, String> claims = Map.of(AID, "", LA_IDS, "");
    return bulkloadsJwtTokenService.generateAccessToken(userId, claims);
  }
}
