package com.bulkloads.security;

import static com.bulkloads.config.AppConstants.API_KEY_ATTRIBUTE;
import static com.bulkloads.config.AppConstants.Header.X_API_KEY;
import static com.google.common.net.HttpHeaders.X_FORWARDED_FOR;
import static java.util.Objects.isNull;
import static java.util.Objects.nonNull;
import static org.springframework.http.HttpHeaders.USER_AGENT;
import java.time.Instant;
import java.util.Optional;
import java.util.stream.Collectors;
import com.bulkloads.common.UserUtil;
import com.bulkloads.web.apikey.projection.ApiKeyResultWithEndpointId;
import com.bulkloads.web.apikey.repository.ApiKeyRepository;
import com.bulkloads.web.requestlog.RequestLog;
import com.bulkloads.web.requestlog.RequestLogRepository;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class LoggingInterceptor implements HandlerInterceptor {

  private final ApiKeyRepository apiKeyRepository;
  private final RequestLogRepository repository;

  @Override
  public boolean preHandle(@NotNull final HttpServletRequest request,
                           @NotNull final HttpServletResponse response,
                           @NotNull final Object handler) {
    return true;
  }

  @Override
  public void postHandle(@NotNull final HttpServletRequest request,
                         @NotNull final HttpServletResponse response,
                         @NotNull final Object handler,
                         final ModelAndView modelAndView) {
    // Do nothing
  }

  @Override
  public void afterCompletion(final @NotNull HttpServletRequest request,
                              final @NotNull HttpServletResponse response,
                              final @NotNull Object handler,
                              final Exception ex) {

    final RequestLog requestLog = buildRequestLog(request, response);
    repository.save(requestLog);
  }

  private RequestLog buildRequestLog(final HttpServletRequest request,
                                     final HttpServletResponse response) {
    RequestLog requestLog = new RequestLog();
    UserUtil.getUserId().ifPresent(requestLog::setUserId);
    UserUtil.getUserCompanyId().ifPresent(requestLog::setUserCompanyId);

    requestLog.setMethod(request.getMethod());
    requestLog.setUrl(request.getRequestURI());
    requestLog.setIpAddress(getRemoteAddr(request));
    requestLog.setUserAgent(isNull(request.getHeader(USER_AGENT)) ? "" : request.getHeader(USER_AGENT));
    requestLog.setDate(Instant.now());
    getApiKeyId(request).ifPresent(requestLog::setApiKeyId);
    requestLog.setBody(StringUtils.left(getRequestBody(request), 2047));
    requestLog.setResponseStatus(response.getStatus());
    getResponseBody(response).map(val -> StringUtils.left(val, 300)).ifPresent(requestLog::setResponse);
    return requestLog;
  }

  private Optional<Integer> getApiKeyId(final HttpServletRequest request) {
    // First check if we already have the API key in request attributes
    Object apiKeyAttribute = request.getAttribute(API_KEY_ATTRIBUTE);
    if (apiKeyAttribute instanceof ApiKeyResultWithEndpointId) {
      return Optional.of(((ApiKeyResultWithEndpointId) apiKeyAttribute).getApiKey().getId());
    }

    // Fall back to database query if not found in attributes
    Optional<Integer> result = Optional.empty();
    if (request.getHeader(X_API_KEY) != null) {
      result = apiKeyRepository
          .findByApiKey(request.getHeader(X_API_KEY))
          .flatMap(apiKey -> Optional.of(apiKey.getId()));
    }
    return result;
  }

  private String getRemoteAddr(final HttpServletRequest request) {
    String ipFromHeader = request.getHeader(X_FORWARDED_FOR);
    if (ipFromHeader != null && !ipFromHeader.isEmpty()) {
      return ipFromHeader;
    }
    return request.getRemoteAddr();
  }

  @SneakyThrows
  private Optional<String> getResponseBody(final HttpServletResponse response) {
    Optional<String> result = Optional.empty();
    if (response instanceof CachedBodyHttpServletResponse customResponse) {
      result = Optional.of(new String(customResponse.getResponseData(), response.getCharacterEncoding()));
    }
    return result;
  }

  private String getRequestBody(final HttpServletRequest request) {
    if (nonNull(request.getContentType()) && request.getContentType().contains(MediaType.MULTIPART_FORM_DATA_VALUE)) {
      return "form-data";
    }
    try {
      return request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
    } catch (Exception e) {
      return "[Error reading request body]";
    }
  }
}
