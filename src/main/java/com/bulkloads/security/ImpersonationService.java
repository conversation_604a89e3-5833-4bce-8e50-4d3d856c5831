package com.bulkloads.security;

import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ImpersonationService {

  private final BulkLoadsUserDetailsService userDetailsService;

  public void impersonate(final int userId) {
    Actor actor = userDetailsService.loadUserByIdWithPermissions(userId, null);
    var newContext = SecurityContextHolder.createEmptyContext();
    newContext.setAuthentication(new UsernamePasswordAuthenticationToken(actor, null, actor.getAuthorities()));
    SecurityContextHolder.setContext(newContext);
  }

}
