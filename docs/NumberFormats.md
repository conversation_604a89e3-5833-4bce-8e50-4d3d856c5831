# Ids (e.g. Invoice ID must not include commas as a thousand separator)

# Amounts (dollarFormat) and Percentages

`amountFormat` (synonym `percentageFormat`):

100,1234.00
100,1234.10
100,1234.01
100,1234.12

NOTE: dollarFormat, includes the dollar sign prefix.

# Dollars

Same as `amountFormat` prefixed by a `$` sign.

$100,1234.00
$100,1234.10
$100,1234.01
$100,1234.12

# Weight, Volume, Miles, Hours and Rate

`numberFormat` (synonyms: `weightFormat`, `hourFormat`, `rateFormat`, `volumeFormat`)

- Use commas
- Up to 5 decimal places if not zero

100,1234
100,1234.1
100,1234.01
100,1234.00001
